﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BarcodeLib" version="3.1.5" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="Newtonsoft.Json.Bson" version="1.0.2" targetFramework="net48" />
  <package id="SkiaSharp" version="2.88.8" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.Linux.NoDependencies" version="2.88.8" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.macOS" version="2.88.8" targetFramework="net48" />
  <package id="SkiaSharp.NativeAssets.Win32" version="2.88.8" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Resources.Extensions" version="8.0.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.5" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.DepartmentManagement
{
    public partial class UseManagement : UserControl
    {
        public UseManagement()
        {
            InitializeComponent();
            this.Load += UseManagement_Load;
            this.useBtnSearch.Click += UseBtnSearch_Click;
            this.useComboBoxPageSize.SelectedIndexChanged += UseComboBoxPageSize_SelectedIndexChanged;
            this.useComboBoxStatus.SelectedIndexChanged += UseComboBoxStatus_SelectedIndexChanged;
            InitStatusComboBox();
        }

        private void UseManagement_Load(object sender, EventArgs e)
        {
            InitGridView();
            BindGrid();
        }

        private void InitStatusComboBox()
        {
            useComboBoxStatus.Properties.Items.Clear();
            useComboBoxStatus.Properties.Items.Add("全部");
            useComboBoxStatus.Properties.Items.Add("待用");
            useComboBoxStatus.Properties.Items.Add("已用");
            useComboBoxStatus.Properties.Items.Add("作废");
            useComboBoxStatus.SelectedIndex = 0;
        }

        private void UseComboBoxPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            BindGrid();
        }

        private void UseComboBoxStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            BindGrid();
        }

        private void UseBtnSearch_Click(object sender, EventArgs e)
        {
            BindGrid();
        }

        private void InitGridView()
        {
            this.useGridView.Appearance.HeaderPanel.BackColor = Color.FromArgb(221, 235, 247);
            this.useGridView.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.useGridView.Appearance.HeaderPanel.Font = new Font("微软雅黑", 9F, FontStyle.Bold);
            this.useGridView.Appearance.HeaderPanel.Options.UseFont = true;
            this.useGridView.OptionsView.ShowGroupPanel = false;
            this.useGridView.Columns.Clear();

            this.useGridView.Columns.AddVisible("id", "编号");
            this.useGridView.Columns.AddVisible("useCode", "使用编号");
            this.useGridView.Columns.AddVisible("patientName", "患者姓名");
            this.useGridView.Columns.AddVisible("patientAge", "患者年龄");
            this.useGridView.Columns.AddVisible("identitycard", "身份证号");
            this.useGridView.Columns.AddVisible("visitingdepartment", "就诊科室");
            this.useGridView.Columns.AddVisible("doctorName", "医生姓名");
            this.useGridView.Columns.AddVisible("registrationPerson", "登记人");
            this.useGridView.Columns.AddVisible("registrationTime", "登记时间");
            this.useGridView.Columns.AddVisible("registrationStatue", "状态");

            // 设置日期格式
            var colRegTime = this.useGridView.Columns["registrationTime"];
            colRegTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colRegTime.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置状态列样式（蓝色文本）
            var colStatus = this.useGridView.Columns["registrationStatue"];
            if (colStatus != null)
            {
                colStatus.AppearanceCell.ForeColor = Color.FromArgb(0, 112, 192);
                colStatus.AppearanceCell.Options.UseForeColor = true;
            }

            AddOperationColumn();
            useGridView.BestFitColumns();
        }

        private void AddOperationColumn()
        {
            RepositoryItemButtonEdit buttonEdit = new RepositoryItemButtonEdit();
            
            // 添加修改按钮
            EditorButton editButton = new EditorButton(ButtonPredefines.Glyph);
            editButton.Caption = "修改";
            editButton.ToolTip = "修改";
            editButton.Kind = ButtonPredefines.Glyph;
            editButton.Appearance.ForeColor = Color.Blue;
            editButton.Tag = "Edit";
            buttonEdit.Buttons.Add(editButton);
            
            // 添加删除按钮
            EditorButton deleteButton = new EditorButton(ButtonPredefines.Delete);
            deleteButton.Caption = "删除";
            deleteButton.ToolTip = "删除";
            deleteButton.Kind = ButtonPredefines.Delete;
            deleteButton.Appearance.ForeColor = Color.Red;
            deleteButton.Tag = "Delete";
            buttonEdit.Buttons.Add(deleteButton);

            buttonEdit.TextEditStyle = TextEditStyles.HideTextEditor;
            buttonEdit.ButtonClick += ButtonEdit_ButtonClick;
            GridColumn operationColumn = this.useGridView.Columns.AddVisible("operation", "操作");
            operationColumn.ColumnEdit = buttonEdit;
            operationColumn.Width = 120;
            operationColumn.OptionsColumn.AllowEdit = true;
            operationColumn.OptionsColumn.FixedWidth = true;
            operationColumn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            operationColumn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.useGridControl.RepositoryItems.Add(buttonEdit);
        }

        private void ButtonEdit_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            try
            {
                int rowHandle = this.useGridView.FocusedRowHandle;
                if (rowHandle < 0) { MessageBox.Show("请先选择一行数据！"); return; }
                int id = Convert.ToInt32(this.useGridView.GetRowCellValue(rowHandle, "id"));
                string useCode = this.useGridView.GetRowCellValue(rowHandle, "useCode")?.ToString();
                if (id <= 0) { MessageBox.Show("无法获取记录ID！"); return; }

                if (e.Button.Tag.ToString() == "Edit")
                {
                    MessageBox.Show($"即将编辑使用编号为 {useCode} 的记录");
                    // TODO: 实现编辑功能
                }
                else if (e.Button.Tag.ToString() == "Delete")
                {
                    DeleteRecord(id);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作出错: {ex.Message}");
            }
        }

        private async void DeleteRecord(int id)
        {
            DialogResult result = MessageBox.Show($"确定要删除编号为 {id} 的记录吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No) return;
            try
            {
                useGridControl.UseWaitCursor = true;
                //string deleteUrl = $"http://localhost:5192/api/DepartmentManagement/use";
                string deleteUrl = $"http://***********:4060/api/DepartmentManagement/use";
                useLblRecordCount.Text = "正在删除数据...";
                Application.DoEvents();
                using (HttpClient client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                    HttpResponseMessage response = await client.DeleteAsync(deleteUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();
                        var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                        if (apiResponse != null && apiResponse.code == 200)
                        {
                            MessageBox.Show("删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            BindGrid();
                        }
                        else
                        {
                            MessageBox.Show($"删除失败: {apiResponse?.msg ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        MessageBox.Show($"删除请求失败: HTTP {(int)response.StatusCode} - {response.ReasonPhrase}\n响应内容: {errorContent}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除过程中出错: {ex.Message}\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                useGridControl.UseWaitCursor = false;
                BindGrid();
            }
        }

        private async void BindGrid()
        {
            try
            {
                //string baseUrl = "http://localhost:5172/api/DepartmentManagement/use";
                string baseUrl = "http://***********:4050/api/DepartmentManagement/use";
                var queryParams = new List<string>(); 
                if (useComboBoxStatus.SelectedIndex > 0)
                {
                    string selectedStatus = useComboBoxStatus.SelectedItem.ToString();
                    string statusValue = ConvertChineseToStatus(selectedStatus);
                    queryParams.Add($"registrationStatue={Uri.EscapeDataString(statusValue)}");
                }
                string url = baseUrl;
                if (queryParams.Count > 0)
                {
                    url += "?" + string.Join("&", queryParams);
                }
                useLblRecordCount.Text = "正在查询数据...";
                Application.DoEvents();
                using (HttpClient client = new HttpClient())
                {
                    var response = await client.GetAsync(url);
                    string result = await response.Content.ReadAsStringAsync();
                    if (!response.IsSuccessStatusCode)
                    {
                        MessageBox.Show("接口请求失败: " + result);
                        useGridControl.DataSource = null;
                        return;
                    }
                    var jobj = JObject.Parse(result);
                    // 关键：获取 pageData 数组
                    var dataArray = jobj["data"]?["pageData"] as JArray;

                    var table = new DataTable();
                    table.Columns.Add("id", typeof(int));
                    table.Columns.Add("useCode", typeof(string));
                    table.Columns.Add("patientName", typeof(string));
                    table.Columns.Add("patientAge", typeof(string));
                    table.Columns.Add("identitycard", typeof(string));
                    table.Columns.Add("visitingdepartment", typeof(string));
                    table.Columns.Add("doctorName", typeof(string));
                    table.Columns.Add("registrationPerson", typeof(string));
                    table.Columns.Add("registrationTime", typeof(string));
                    table.Columns.Add("registrationStatue", typeof(string));
                    if (dataArray != null)
                    {
                        foreach (var item in dataArray)
                        {
                            string status = item["registrationStatue"]?.ToString() ?? "";
                            string chineseStatus = ConvertStatusToChinese(status);
                            table.Rows.Add(
                                item["id"],
                                item["useCode"]?.ToString() ?? "",
                                item["patientName"]?.ToString() ?? "",
                                item["patientAge"]?.ToString() ?? "",
                                item["identitycard"]?.ToString() ?? "",
                                item["visitingdepartment"]?.ToString() ?? "",
                                item["doctorName"]?.ToString() ?? "",
                                item["registrationPerson"]?.ToString() ?? "",
                                item["registrationTime"]?.ToString() ?? "",
                                chineseStatus
                            );
                        }
                    }
                    useGridControl.DataSource = table;
                    useLblRecordCount.Text = $"共 {table.Rows.Count} 条记录";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("获取使用列表数据失败：" + ex.Message);
                useGridControl.DataSource = null;
            }
        }

        private string ConvertStatusToChinese(string status)
        {
            if (int.TryParse(status, out int statusCode))
            {
                switch (statusCode)
                {
                    case 1: return "待用";
                    case 2: return "已用";
                    case 3: return "作废";
                    default: return status;
                }
            }
            if (status == "待用" || status == "已用" || status == "作废")
            {
                return status;
            }
            return status;
        }

        private string ConvertChineseToStatus(string chineseStatus)
        {
            switch (chineseStatus)
            {
                case "待用": return "1";
                case "已用": return "2";
                case "作废": return "3";
                default: return chineseStatus;
            }
        }
    }


}
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public partial class CleaningManagement : UserControl
    {
        private string ApiBaseUrl = ApiUrl.ApiReadUrl; // 读取API地址
        private string ApiWriteUrl = ApiUrl.ApiWriteUrl; // 写入API地址

        public CleaningManagement()
        {
            InitializeComponent();
            InitializeGridControl();
        }







        private void InitializeGridControl()
        {
            // 初始化gridView1 (已完成列表)
            InitializeGridView1();

            // 初始化gridView2 (清洗列表)
            InitializeGridView2();
        }

        private void InitializeGridView1()
        {
            // 设置GridView1属性 (已完成列表)
            gridView1.OptionsBehavior.Editable = false;
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ShowIndicator = true;
            gridView1.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;

            // 设置列
            gridView1.Columns.Clear();
            gridView1.Columns.AddVisible("CleaningBatch", "清洗批次");
            gridView1.Columns.AddVisible("Cleaner", "清洗人");
            gridView1.Columns.AddVisible("FinishTime", "完成时间");
            gridView1.Columns.AddVisible("Operation", "操作");

            // 设置列宽
            gridView1.Columns["CleaningBatch"].Width = 120;
            gridView1.Columns["Cleaner"].Width = 100;
            gridView1.Columns["FinishTime"].Width = 150;
            gridView1.Columns["Operation"].Width = 100;

            // 格式化日期列
            gridView1.Columns["FinishTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridView1.Columns["FinishTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置行高
            gridView1.RowHeight = 35;

            // 绑定事件
            gridView1.CustomColumnDisplayText += GridView1_CustomColumnDisplayText;
            gridView1.RowClick += GridView1_RowClick;
            gridView1.CustomDrawCell += GridView1_CustomDrawCell;
            gridView1.MouseMove += GridView1_MouseMove;
        }

        private void InitializeGridView2()
        {
            // 设置GridView2属性 (清洗列表)
            gridView2.OptionsBehavior.Editable = false;
            gridView2.OptionsView.ShowGroupPanel = false;
            gridView2.OptionsView.ShowIndicator = true;
            gridView2.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            gridView2.OptionsView.ShowColumnHeaders = true;
            gridView2.OptionsView.EnableAppearanceEvenRow = true;
            gridView2.OptionsView.EnableAppearanceOddRow = true;

            // 设置选择模式
            gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView2.OptionsSelection.EnableAppearanceFocusedRow = true;
            gridView2.OptionsSelection.MultiSelect = false;

            // 设置列
            gridView2.Columns.Clear();
            var colId = gridView2.Columns.AddField("Id"); // 添加隐藏的ID列
            colId.Visible = false; // 设置为不可见
            var colCleaningBatch = gridView2.Columns.AddVisible("CleaningBatch", "清洗批次");
            var colStartTime = gridView2.Columns.AddVisible("StartTime", "开始时间");
            var colCleaningDuration = gridView2.Columns.AddVisible("CleaningDuration", "清洗时长");
            var colCleaner = gridView2.Columns.AddVisible("Cleaner", "清洗人");
            var colOperation = gridView2.Columns.AddVisible("Operation", "操作");

            // 设置列宽和对齐方式
            colCleaningBatch.Width = 120;
            colCleaningBatch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colCleaningBatch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colStartTime.Width = 150;
            colStartTime.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colStartTime.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colCleaningDuration.Width = 100;
            colCleaningDuration.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colCleaningDuration.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colCleaner.Width = 100;
            colCleaner.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colCleaner.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colOperation.Width = 150;
            colOperation.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colOperation.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            // 格式化日期列
            colStartTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colStartTime.DisplayFormat.FormatString = "MM-dd HH:mm";

            // 设置行高
            gridView2.RowHeight = 40;

            // 设置外观样式
            SetGridView2Appearance();

            // 绑定事件
            gridView2.CustomColumnDisplayText += GridView2_CustomColumnDisplayText;
            gridView2.RowClick += GridView2_RowClick;
            gridView2.CustomDrawCell += GridView2_CustomDrawCell;
            gridView2.MouseMove += GridView2_MouseMove;
        }

        private void SetGridView2Appearance()
        {
            // 设置表头样式
            gridView2.Appearance.HeaderPanel.BackColor = Color.FromArgb(240, 248, 255);
            gridView2.Appearance.HeaderPanel.ForeColor = Color.FromArgb(51, 51, 51);
            gridView2.Appearance.HeaderPanel.Font = new Font("微软雅黑", 9, FontStyle.Bold);
            gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            gridView2.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 设置奇偶行样式
            gridView2.Appearance.EvenRow.BackColor = Color.FromArgb(250, 250, 250);
            gridView2.Appearance.OddRow.BackColor = Color.White;

            // 设置选中行样式
            gridView2.Appearance.FocusedRow.BackColor = Color.FromArgb(173, 216, 230);
            gridView2.Appearance.FocusedRow.ForeColor = Color.Black;

            // 设置悬停行样式
            gridView2.Appearance.HideSelectionRow.BackColor = Color.FromArgb(230, 230, 230);

            // 设置单元格样式
            gridView2.Appearance.Row.Font = new Font("微软雅黑", 9);
            gridView2.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 设置边框
            gridView2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
        }



        private void GridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "CleaningResult")
            {
                bool result = Convert.ToBoolean(e.Value);
                e.DisplayText = result ? "合格" : "不合格";
            }
            else if (e.Column.FieldName == "Operation")
            {
                e.DisplayText = "查看 | 撤销";
            }
        }

        private void GridView2_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                e.DisplayText = "明细 | 删除 | 完成";
            }
            else if (e.Column.FieldName == "CleaningDuration")
            {
                // 如果清洗时长字段有值，添加"分钟"单位
                if (!string.IsNullOrEmpty(e.Value?.ToString()))
                {
                    e.DisplayText = e.Value.ToString() + "分钟";
                }
            }
            else if (e.Column.FieldName == "StartTime")
            {
                // 格式化开始时间显示
                if (e.Value is DateTime dateTime)
                {
                    e.DisplayText = dateTime.ToString("MM-dd HH:mm");
                }
            }
        }

        private void GridView2_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                // 设置操作列的特殊样式
                e.Appearance.BackColor = Color.FromArgb(240, 248, 255);
                e.Appearance.ForeColor = Color.FromArgb(0, 102, 204);
                e.Appearance.Font = new Font("微软雅黑", 9, FontStyle.Regular);
                e.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

                // 添加鼠标悬停效果
                if (e.RowHandle == gridView2.FocusedRowHandle)
                {
                    e.Appearance.BackColor = Color.FromArgb(220, 235, 252);
                    e.Appearance.ForeColor = Color.FromArgb(0, 82, 164);
                }
            }
            else if (e.Column.FieldName == "CleaningBatch")
            {
                // 清洗批次列使用粗体
                e.Appearance.Font = new Font("微软雅黑", 9, FontStyle.Bold);
                e.Appearance.ForeColor = Color.FromArgb(51, 51, 51);
            }
        }

        private void GridView1_MouseMove(object sender, MouseEventArgs e)
        {
            var gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            var hitInfo = gridView.CalcHitInfo(e.Location);

            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                gridView.GridControl.Cursor = Cursors.Hand;
            }
            else
            {
                gridView.GridControl.Cursor = Cursors.Default;
            }
        }

        private void GridView1_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                e.Appearance.ForeColor = Color.Blue;
                e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Underline);
            }
        }

        private void GridView2_MouseMove(object sender, MouseEventArgs e)
        {
            var hitInfo = gridView2.CalcHitInfo(e.Location);
            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                // 设置鼠标指针为手型，表示可点击
                gridControl2.Cursor = Cursors.Hand;
            }
            else
            {
                gridControl2.Cursor = Cursors.Default;
            }
        }
        //已完成列表操作撤销方法
        private void GridView1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            var gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            var hitInfo = gridView.CalcHitInfo(new Point(e.X, e.Y));

            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                // 获取当前行的ID和清洗批次
                var cleaningId = gridView.GetRowCellValue(e.RowHandle, "Id")?.ToString() ?? "0";
                var cleaningBatch = gridView.GetRowCellValue(e.RowHandle, "CleaningBatch")?.ToString() ?? "未知";

                System.Diagnostics.Debug.WriteLine($"🔍 已完成列表点击操作: ID={cleaningId}, Batch={cleaningBatch}");

                // 显示操作选择对话框
                ShowCompletedCleaningOperationDialog(cleaningId, cleaningBatch);
            }
        }

        private void GridView2_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            // 获取点击的列信息
            var hitInfo = gridView2.CalcHitInfo(new Point(e.X, e.Y));

            // 只处理操作列的点击
            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                // 获取当前行的ID和清洗批次
                var cleaningId = gridView2.GetRowCellValue(e.RowHandle, "Id")?.ToString();
                var cleaningBatch = gridView2.GetRowCellValue(e.RowHandle, "CleaningBatch")?.ToString();

                if (!string.IsNullOrEmpty(cleaningId) && !string.IsNullOrEmpty(cleaningBatch))
                {
                    // 显示操作选择对话框，传递ID和批次
                    ShowCleaningOperationDialog(cleaningId, cleaningBatch);
                }
            }
        }
        //撤销清洗状态方法
        //private void ShowCompletedCleaningOperationDialog(string cleaningId, string cleaningBatch)
        //{
        //    // 创建已完成清洗的操作选择对话框
        //    using (var dialog = new CompletedCleaningOperationDialog(cleaningBatch))
        //    {
        //        var result = dialog.ShowDialog(this);

        //        if (result == DialogResult.OK)
        //        {
        //            switch (dialog.Result)
        //            {
        //                case CompletedCleaningOperationDialog.OperationResult.View:
        //                    ShowCleaningDetails(cleaningBatch);
        //                    break;
        //                case CompletedCleaningOperationDialog.OperationResult.Cancel_Operation:
        //                    CancelCleaningState(cleaningId, cleaningBatch);
        //                    break;
        //            }
        //        }
        //    }
        //}
        //撤销清洗状态方法
        private void ShowCompletedCleaningOperationDialog(string cleaningId, string cleaningBatch)
        {
            // 临时实现，使用MessageBox替代对话框
            var result = MessageBox.Show(
                "请选择操作:\n" +
                "是 - 查看详情\n" +
                "否 - 撤销操作\n" +
                "取消 - 关闭",
                $"已完成清洗操作 - {cleaningBatch}",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question);

            switch (result)
            {
                case DialogResult.Yes:
                    ShowCleaningDetails(cleaningBatch);
                    break;
                case DialogResult.No:
                    CancelCleaningState(cleaningId, cleaningBatch);
                    break;
            }
        }

        private void ShowCleaningOperationDialog(string cleaningId, string cleaningBatch)
        {
            // 使用自定义对话框
            using (var operationDialog = new CleaningOperationDialog(cleaningBatch))
            {
                if (operationDialog.ShowDialog() == DialogResult.OK)
                {
                    switch (operationDialog.Result)
                    {
                        case CleaningOperationDialog.OperationResult.Details:
                            ShowCleaningDetails(cleaningBatch);
                            break;
                        case CleaningOperationDialog.OperationResult.Delete:
                            DeleteCleaning(cleaningBatch);
                            break;
                        case CleaningOperationDialog.OperationResult.Complete:
                            CompleteCleaning(cleaningId, cleaningBatch);
                            break;
                    }
                }
                // 如果用户按ESC或关闭对话框，不执行任何操作
            }
        }

        private void CleaningManagement_Load(object sender, EventArgs e)
        {
            // 设置默认日期
            dateEdit1.EditValue = DateTime.Today;

            System.Diagnostics.Debug.WriteLine("CleaningManagement_Load 开始");

            // 先尝试加载API数据，如果失败则加载示例数据
            try
            {
                LoadCleaningListData();
                LoadCompletedCleaningData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"API加载失败，使用示例数据: {ex.Message}");
                LoadSampleCleaningData();
            }

            System.Diagnostics.Debug.WriteLine("CleaningManagement_Load 完成");
        }





        private void LoadSampleCleaningData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始加载示例清洗数据到GridView2");

                // 创建示例清洗数据列表
                var sampleCleaningDataList = new List<object>();

                for (int i = 1; i <= 6; i++)
                {
                    var sampleData = new
                    {
                        CleaningBatch = $"QX{DateTime.Now.ToString("yyyyMMdd")}{i:D2}",
                        StartTime = DateTime.Now.AddHours(-i),
                        CleaningDuration = (20 + i * 5).ToString(),
                        Cleaner = i % 3 == 0 ? "李四" : i % 2 == 0 ? "王五" : "张三",
                        Operation = "" // 操作列将通过CustomColumnDisplayText显示
                    };

                    sampleCleaningDataList.Add(sampleData);
                    System.Diagnostics.Debug.WriteLine($"创建示例清洗数据 {i}: {sampleData.CleaningBatch}");
                }

                // 绑定数据到GridControl2 (清洗列表)
                gridControl2.DataSource = sampleCleaningDataList;

                System.Diagnostics.Debug.WriteLine($"示例清洗数据加载完成，共 {sampleCleaningDataList.Count} 条记录");

                // 刷新GridControl2
                gridView2.RefreshData();
                gridControl2.Refresh();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载示例清洗数据异常: {ex.Message}");
                MessageBox.Show($"加载示例清洗数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private async void LoadCleaningListData()
        {
            try
            {
                // 调用API获取待清洗列表
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetRegistrationCleaningList";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                System.Diagnostics.Debug.WriteLine($"API URL: {apiUrl}");
                System.Diagnostics.Debug.WriteLine($"API Response: {jsonResult}");

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    try
                    {
                        // 解析API响应
                        var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                        if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"API响应成功，数据类型: {apiResponse.data.Type}");

                            var cleaningDataList = new List<object>();

                            // 检查data是否为数组
                            if (apiResponse.data is JArray dataArray)
                            {
                                System.Diagnostics.Debug.WriteLine($"数据为数组，包含 {dataArray.Count} 个项目");

                                // 转换数据为GridControl格式
                                foreach (JToken item in dataArray)
                                {
                                    var cleaningData = new
                                    {
                                        Id = item["id"]?.ToString() ?? "0", // 添加ID字段用于API调用
                                        CleaningBatch = item["cleaningBatch"]?.ToString() ?? item["id"]?.ToString() ?? "未知",
                                        StartTime = DateTime.TryParse(item["startTime"]?.ToString(), out DateTime startTime) ? startTime : DateTime.Now,
                                        CleaningDuration = item["cleaningDuration"]?.ToString() ?? "30",
                                        Cleaner = item["cleaner"]?.ToString() ?? "未知",
                                        Operation = "" // 操作列将通过CustomColumnDisplayText显示
                                    };
                                    cleaningDataList.Add(cleaningData);
                                    System.Diagnostics.Debug.WriteLine($"处理项目: ID={cleaningData.Id}, Batch={cleaningData.CleaningBatch}");
                                }

                                // 绑定数据到GridControl2 (清洗列表)
                                gridControl2.DataSource = cleaningDataList;
                                gridView2.RefreshData();

                                System.Diagnostics.Debug.WriteLine($"成功加载 {cleaningDataList.Count} 条清洗记录到GridControl2");
                                return; // 成功，直接返回
                            }
                            else if (apiResponse.data is JObject singleItem)
                            {
                                System.Diagnostics.Debug.WriteLine("数据为单个对象");
                                var cleaningData = new
                                {
                                    Id = singleItem["id"]?.ToString() ?? "0", // 添加ID字段用于API调用
                                    CleaningBatch = singleItem["cleaningBatch"]?.ToString() ?? singleItem["id"]?.ToString() ?? "未知",
                                    StartTime = DateTime.TryParse(singleItem["startTime"]?.ToString(), out DateTime startTime) ? startTime : DateTime.Now,
                                    CleaningDuration = singleItem["cleaningDuration"]?.ToString() ?? "30",
                                    Cleaner = singleItem["cleaner"]?.ToString() ?? "未知",
                                    Operation = ""
                                };
                                cleaningDataList.Add(cleaningData);
                                gridControl2.DataSource = cleaningDataList;
                                gridView2.RefreshData();
                                System.Diagnostics.Debug.WriteLine("成功加载 1 条清洗记录到GridControl2");
                                return;
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"API响应失败: code={apiResponse?.code}, msg={apiResponse?.msg}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"API响应解析失败: {ex.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("API返回空结果");
                }

                // API失败或无数据时，加载示例数据
                System.Diagnostics.Debug.WriteLine("API加载失败，使用示例数据");
                LoadSampleCleaningData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"API调用异常: {ex.Message}");
                MessageBox.Show($"加载清洗列表失败: {ex.Message}\n将显示示例数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                // 异常时也加载示例数据
                LoadSampleCleaningData();
            }
        }

        private async void LoadCompletedCleaningData()
        {
            try
            {
                // 调用API获取已完成清洗列表
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetOffTheStocksList";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                    // 如果响应成功且包含数据
                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 创建数据表
                        DataTable completedTable = new DataTable();
                        completedTable.Columns.Add("Id", typeof(string)); // 添加ID字段
                        completedTable.Columns.Add("CleaningBatch", typeof(string));
                        completedTable.Columns.Add("Cleaner", typeof(string));
                        completedTable.Columns.Add("FinishTime", typeof(string));
                        completedTable.Columns.Add("Operation", typeof(string));

                        // 填充数据
                        foreach (JToken item in apiResponse.data)
                        {
                            DateTime.TryParse(item["finishTime"]?.ToString(), out DateTime finishTime);
                            completedTable.Rows.Add(
                                item["id"]?.ToString() ?? "1", // 添加ID
                                item["cleaningBatch"]?.ToString() ?? "1001",
                                item["cleaner"]?.ToString() ?? "张三",
                                finishTime != DateTime.MinValue ? finishTime.ToString("yyyy-MM-dd HH:mm:ss") : "2022-05-19 10:00:00",
                                "" // 操作列将通过CustomColumnDisplayText显示
                            );
                        }

                        // 如果没有数据，添加示例数据
                        if (completedTable.Rows.Count == 0)
                        {
                            completedTable.Rows.Add("1", "QX20250723", "李西蒙", "2025-07-27 00:00:00", "");
                            completedTable.Rows.Add("2", "QX20250723", "李实施", "2025-07-26 00:00:00", "");
                            completedTable.Rows.Add("3", "QX20250722", "李思思", "2025-07-23 00:00:00", "");
                        }

                        // 绑定数据到网格
                        gridControl1.DataSource = completedTable;
                    }
                }
                else
                {
                    // 如果API调用失败，显示示例数据
                    LoadSampleCompletedData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载完成清洗列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // 加载示例数据
                LoadSampleCompletedData();
            }
        }

        private void LoadSampleCompletedData()
        {
            // 创建示例数据
            DataTable sampleTable = new DataTable();
            sampleTable.Columns.Add("Id", typeof(string)); // 添加ID字段
            sampleTable.Columns.Add("CleaningBatch", typeof(string));
            sampleTable.Columns.Add("Cleaner", typeof(string));
            sampleTable.Columns.Add("FinishTime", typeof(string));
            sampleTable.Columns.Add("Operation", typeof(string));

            sampleTable.Rows.Add("1", "QX20250723", "李西蒙", "2025-07-27 00:00:00", "");
            sampleTable.Rows.Add("2", "QX20250723", "李实施", "2025-07-26 00:00:00", "");
            sampleTable.Rows.Add("3", "QX20250722", "李思思", "2025-07-23 00:00:00", "");

            gridControl1.DataSource = sampleTable;
        }



        private void ShowCleaningDetails(string cleaningId)
        {
            // 这里实现显示清洗详情的逻辑
            MessageBox.Show($"显示清洗ID为 {cleaningId} 的详情", "清洗详情", MessageBoxButtons.OK, MessageBoxIcon.Information);
            // 可以打开一个详情对话框，并提供标记完成的功能
        }

        private void DeleteCleaning(string cleaningId)
        {
            // 确认是否删除
            DialogResult result = MessageBox.Show($"确定要删除ID为 {cleaningId} 的清洗记录吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                // 实现删除逻辑
                MessageBox.Show($"已删除ID为 {cleaningId} 的清洗记录", "删除成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // 删除后刷新数据
                LoadCleaningListData();
            }
        }

        /// <summary>
        /// 完成清洗的修改状态的方法
        /// </summary>
        /// <param name="cleaningId">清洗编号Id</param>
        /// <param name="cleaningBatch">清洗批次</param>
        private async void CompleteCleaning(string cleaningId, string cleaningBatch)
        {
            try
            {
                // 打开完成清洗对话框
                using (var completionForm = new CleaningCompletionForm(cleaningBatch))
                {
                    if (completionForm.ShowDialog() == DialogResult.OK)
                    {
                        // 获取用户输入的信息
                        var finishPerson = completionForm.FinishPerson;
                        var finishTime = completionForm.FinishTime;
                        var cleaningResult = completionForm.CleaningResult;

                        // 调用API更新清洗完成状态，使用cleaningId
                        bool success = await UpdateCleaningCompletedAsync(cleaningId, finishTime, cleaningResult, finishPerson);

                        if (success)
                        {
                            MessageBox.Show($"清洗批次 {cleaningBatch} 已标记为完成", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 刷新数据
                            LoadCleaningListData();
                            LoadCompletedCleaningData();
                        }
                        else
                        {
                            MessageBox.Show("更新清洗状态失败，请重试", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void 清洗管理_Click(object sender, EventArgs e)
        {
            // 刷新数据
            System.Diagnostics.Debug.WriteLine("手动刷新数据开始");
            LoadCleaningListData();
            LoadCompletedCleaningData();
            System.Diagnostics.Debug.WriteLine("手动刷新数据完成");
        }

        // 添加清洗登记功能
        private void button1_Click(object sender, EventArgs e)
        {
            // 打开清洗登记对话框
            OpenCleaningRegistrationDialog();
        }

        private void OpenCleaningRegistrationDialog()
        {
            try
            {
                using (var registrationForm = new CleaningRegistrationForm())
                {
                    var result = registrationForm.ShowDialog();
                    if (result == DialogResult.OK)
                    {
                        // 刷新清洗列表数据
                        LoadCleaningListData();
                        MessageBox.Show("清洗登记已完成，数据已刷新", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开清洗登记窗口失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 调用API更新清洗完成状态
        /// </summary>
        /// <param name="cleaningId">清洗记录ID</param>
        /// <param name="finishTime">完成时间</param>
        /// <param name="cleaningResult">清洗结果</param>
        /// <param name="finishPerson">完成人</param>
        /// <returns>是否成功</returns>
        private async void CancelCleaningState(string cleaningId, string cleaningBatch)
        {
            try
            {
                // 确认撤销操作
                var confirmResult = MessageBox.Show(
                    $"确定要撤销清洗批次 {cleaningBatch} 的完成状态吗？\n撤销后该记录将重新回到清洗列表中。",
                    "确认撤销",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (confirmResult == DialogResult.Yes)
                {
                    // 调用API撤销清洗状态
                    bool success = await CancelCleaningStateAsync(cleaningId);

                    if (success)
                    {
                        MessageBox.Show($"清洗批次 {cleaningBatch} 的完成状态已撤销", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 刷新数据
                        LoadCleaningListData();
                        LoadCompletedCleaningData();
                    }
                    else
                    {
                        MessageBox.Show("撤销清洗状态失败，请重试", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 撤销清洗状态异常: {ex.Message}");
                MessageBox.Show($"撤销清洗状态时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 调用API撤销清洗完成状态
        /// </summary>
        /// <param name="cleaningId">清洗记录ID</param>
        /// <returns>是否成功</returns>
        private async Task<bool> CancelCleaningStateAsync(string cleaningId)
        {
            try
            {
                // 构建请求数据
                var requestData = new
                {
                    id = int.TryParse(cleaningId, out int id) ? id : 1
                };

                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(requestData);
                System.Diagnostics.Debug.WriteLine($"🚀 调用撤销清洗状态API，请求数据: {jsonData}");

                // 构建API URL
                string apiUrl = $"{ApiWriteUrl}/api/RecyclingCleaning/CancelCleaningState";
                System.Diagnostics.Debug.WriteLine($"🌐 API URL: {apiUrl}");

                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("PUT", apiUrl, true, content);
                System.Diagnostics.Debug.WriteLine($"📥 API返回结果: {jsonResult}");

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    
                   
                        return true;
                   
                }

                System.Diagnostics.Debug.WriteLine("❌ API调用失败，未返回数据");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 撤销清洗状态异常: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> UpdateCleaningCompletedAsync(string cleaningId, DateTime finishTime, bool cleaningResult, string finishPerson)
        {
            try
            {
                // 构建请求数据
                var requestData = new
                {
                    id = int.TryParse(cleaningId, out int id) ? id : 1, // 使用cleaningId
                    finishTime = finishTime.ToString("yyyy-MM-dd"),
                    cleaningResult = cleaningResult,
                    finishPerson = finishPerson
                };

                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(requestData);
                System.Diagnostics.Debug.WriteLine($"🚀 调用完成清洗API，请求数据: {jsonData}");

                // 构建API URL
                string apiUrl = $"{ApiWriteUrl}/api/RecyclingCleaning/UpdateCleaningCompleted";
                System.Diagnostics.Debug.WriteLine($"🌐 API URL: {apiUrl}");

                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("PUT", apiUrl, true, content);
                System.Diagnostics.Debug.WriteLine($"📥 API返回结果: {jsonResult}");

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                   
                        return true;
                   
                }

                System.Diagnostics.Debug.WriteLine("❌ API调用失败，未返回数据");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 更新清洗完成状态异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取对象属性值的辅助方法
        /// </summary>
        /// <param name="obj">对象</param>
        /// <param name="propertyName">属性名</param>
        /// <returns>属性值</returns>
        private object GetPropertyValue(object obj, string propertyName)
        {
            if (obj == null) return null;

            // 如果是DataRow类型
            if (obj is DataRow dataRow)
            {
                return dataRow.Table.Columns.Contains(propertyName) ? dataRow[propertyName] : null;
            }

            // 如果是匿名对象或其他类型，使用反射
            var property = obj.GetType().GetProperty(propertyName);
            return property?.GetValue(obj);
        }

        // API响应模型
        private class ApiResponse
        {
            public int code { get; set; }
            public string msg { get; set; }
            public Newtonsoft.Json.Linq.JToken data { get; set; }
        }

        private void labelControl3_Click(object sender, EventArgs e)
        {

        }

        private void gridLookUpEdit1_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void gridControl2_Click(object sender, EventArgs e)
        {

        }
    }
}

using System;
using System.Drawing;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public partial class DisinfectionOperationDialog : Form
    {
        public enum OperationResult
        {
            Details,    // 明细
            Delete,     // 删除
            Complete,   // 完成
            View,       // 查看
            Cancel_Operation,     // 撤销
            Cancel      // 取消
        }

        public OperationResult Result { get; private set; } = OperationResult.Cancel;

        private Button btnDetails;
        private Button btnDelete;
        private Button btnComplete;
        private Label lblMessage;
        private PictureBox pictureBox;

        public DisinfectionOperationDialog(string disinfectionBatch)
        {
            InitializeComponent();
            lblMessage.Text = $"消毒批次：{disinfectionBatch}\n\n请选择要执行的操作：\n\n【明细】- 查看详细信息\n【删除】- 删除此记录\n【完成】- 标记为完成";
        }

        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DisinfectionOperationDialog));
            this.lblMessage = new System.Windows.Forms.Label();
            this.pictureBox = new System.Windows.Forms.PictureBox();
            this.btnDetails = new System.Windows.Forms.Button();
            this.btnDelete = new System.Windows.Forms.Button();
            this.btnComplete = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox)).BeginInit();
            this.SuspendLayout();
            // 
            // lblMessage
            // 
            this.lblMessage.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblMessage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMessage.Location = new System.Drawing.Point(80, 20);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new System.Drawing.Size(440, 280);
            this.lblMessage.TabIndex = 1;
            // 
            // pictureBox
            // 
            this.pictureBox.Image = SystemIcons.Question.ToBitmap();
            this.pictureBox.Location = new System.Drawing.Point(20, 20);
            this.pictureBox.Name = "pictureBox";
            this.pictureBox.Size = new System.Drawing.Size(48, 48);
            this.pictureBox.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox.TabIndex = 0;
            this.pictureBox.TabStop = false;
            // 
            // btnDetails
            // 
            this.btnDetails.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnDetails.Location = new System.Drawing.Point(122, 324);
            this.btnDetails.Name = "btnDetails";
            this.btnDetails.Size = new System.Drawing.Size(80, 35);
            this.btnDetails.TabIndex = 2;
            this.btnDetails.Text = "明细";
            this.btnDetails.UseVisualStyleBackColor = true;
            this.btnDetails.Click += new System.EventHandler(this.BtnDetails_Click);
            //
            // btnDelete
            //
            this.btnDelete.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnDelete.Location = new System.Drawing.Point(236, 324);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(80, 35);
            this.btnDelete.TabIndex = 3;
            this.btnDelete.Text = "删除";
            this.btnDelete.UseVisualStyleBackColor = true;
            this.btnDelete.Click += new System.EventHandler(this.BtnDelete_Click);
            // 
            // btnComplete
            // 
            this.btnComplete.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnComplete.Location = new System.Drawing.Point(340, 324);
            this.btnComplete.Name = "btnComplete";
            this.btnComplete.Size = new System.Drawing.Size(80, 35);
            this.btnComplete.TabIndex = 4;
            this.btnComplete.Text = "完成";
            this.btnComplete.UseVisualStyleBackColor = true;
            this.btnComplete.Click += new System.EventHandler(this.BtnComplete_Click);
            // 
            // DisinfectionOperationDialog
            // 
            this.AcceptButton = this.btnDetails;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(540, 424);
            this.Controls.Add(this.pictureBox);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.btnDetails);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnComplete);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "DisinfectionOperationDialog";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "消毒管理操作";
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox)).EndInit();
            this.ResumeLayout(false);

        }

        private void BtnDetails_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("🔍 点击了明细按钮");
            Result = OperationResult.Details;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("🗑️ 点击了删除按钮");
            Result = OperationResult.Delete;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnComplete_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("✅ 点击了完成按钮");
            Result = OperationResult.Complete;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        // 重写ProcessCmdKey方法处理ESC键
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                Result = OperationResult.Cancel;
                this.DialogResult = DialogResult.Cancel;
                this.Close();
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }


    }
}

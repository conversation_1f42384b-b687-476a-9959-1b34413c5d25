using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.DepartmentManagement
{

    // API响应模型类
    public class ApiResponse
    {
        public dynamic code { get; set; }
        public string msg { get; set; }
        public Newtonsoft.Json.Linq.JToken data { get; set; }
    }

    public partial class ApplicationRequisition : UserControl
    {
        public ApplicationRequisition()
        {
            InitializeComponent();
            // 注册控件加载事件
            this.Load += ApplicationRequisition_Load;
            // 注册按钮点击事件
            this.arBtnSearch.Click += BtnSearch_Click1;
            // 注册下拉列表选择事件
            this.arComboBoxPageSize.SelectedIndexChanged += ComboBoxPageSize_SelectedIndexChanged;
            // 注册新增领用申请按钮点击事件
            this.arBtnNewApply.Click += ArBtnNewApply_Click;

            // 初始化状态下拉框
            InitStatusComboBox();
        }

        /// <summary>
        /// 新增领用申请按钮点击事件
        /// </summary>
        private void ArBtnNewApply_Click(object sender, EventArgs e)
        {
            ShowAddApplicationDialog();
        }

        /// <summary>
        /// 显示添加领用申请对话框
        /// </summary>
        private void ShowAddApplicationDialog()
        {
            // 创建一个表单作为弹出对话框
            XtraForm dialog = new XtraForm();
            dialog.Text = "新增领用申请";
            dialog.StartPosition = FormStartPosition.CenterParent;
            dialog.Size = new Size(500, 460);
            dialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            dialog.MaximizeBox = false;
            dialog.MinimizeBox = false;

            // 初始化表单控件
            int yPos = 20;
            int labelX = 30;
            int controlX = 150;
            int controlWidth = 280;
            int lineHeight = 30;

            // 申请单位
            LabelControl lblApplyingUnit = new LabelControl();
            lblApplyingUnit.Text = "申请单位:";
            lblApplyingUnit.Location = new Point(labelX, yPos);
            ComboBoxEdit cmbApplyingUnit = new ComboBoxEdit();
            cmbApplyingUnit.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
            cmbApplyingUnit.Location = new Point(controlX, yPos);
            cmbApplyingUnit.Size = new Size(controlWidth, 20);
            // 添加测试数据，实际应从数据库加载
            cmbApplyingUnit.Properties.Items.AddRange(new string[] { "内科", "手术室"});
            dialog.Controls.Add(lblApplyingUnit);
            dialog.Controls.Add(cmbApplyingUnit);
            yPos += lineHeight;

            // 发放单位
            LabelControl lblIssuingUnit = new LabelControl();
            lblIssuingUnit.Text = "发放单位:";
            lblIssuingUnit.Location = new Point(labelX, yPos);
            ComboBoxEdit cmbIssuingUnit = new ComboBoxEdit();
            cmbIssuingUnit.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
            cmbIssuingUnit.Location = new Point(controlX, yPos);
            cmbIssuingUnit.Size = new Size(controlWidth, 20);
            // 添加测试数据，实际应从数据库加载
            cmbIssuingUnit.Properties.Items.AddRange(new string[] { "富士康", "博世", "宝洁" });
            dialog.Controls.Add(lblIssuingUnit);
            dialog.Controls.Add(cmbIssuingUnit);
            yPos += lineHeight;

            // 申请时间
            LabelControl lblApplicationTime = new LabelControl();
            lblApplicationTime.Text = "申请时间:";
            lblApplicationTime.Location = new Point(labelX, yPos);
            DateEdit dateApplicationTime = new DateEdit();
            dateApplicationTime.Location = new Point(controlX, yPos);
            dateApplicationTime.Size = new Size(controlWidth, 20);
            dateApplicationTime.DateTime = DateTime.Now;
            dialog.Controls.Add(lblApplicationTime);
            dialog.Controls.Add(dateApplicationTime);
            yPos += lineHeight;

            // 申请人
            LabelControl lblApplicant = new LabelControl();
            lblApplicant.Text = "申请人:";
            lblApplicant.Location = new Point(labelX, yPos);
            TextEdit txtApplicant = new TextEdit();
            txtApplicant.Location = new Point(controlX, yPos);
            txtApplicant.Size = new Size(controlWidth, 20);
            dialog.Controls.Add(lblApplicant);
            dialog.Controls.Add(txtApplicant);
            yPos += lineHeight;
            // 是否加急
            LabelControl lblUrgent = new LabelControl();
            lblUrgent.Text = "是否加急:";
            lblUrgent.Location = new Point(labelX, yPos);
            RadioGroup radioUrgent = new RadioGroup();
            radioUrgent.Location = new Point(controlX, yPos);
            radioUrgent.Properties.Items.AddRange(new RadioGroupItem[] {
                new RadioGroupItem(1, "是"),
                new RadioGroupItem(0, "否")
            });
            radioUrgent.EditValue = 0; // 默认选择"否"
            radioUrgent.Size = new Size(controlWidth, 20);
            dialog.Controls.Add(lblUrgent);
            dialog.Controls.Add(radioUrgent);
            yPos += lineHeight;

            // 状态
            LabelControl lblTouseStatus = new LabelControl();
            lblTouseStatus.Text = "状态:";
            lblTouseStatus.Location = new Point(labelX, yPos);
            ComboBoxEdit cmbTouseStatus = new ComboBoxEdit();
            cmbTouseStatus.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
            cmbTouseStatus.Location = new Point(controlX, yPos);
            cmbTouseStatus.Size = new Size(controlWidth, 20);
            cmbTouseStatus.Properties.Items.AddRange(new string[] { "新增", "审核", "接收" });
            cmbTouseStatus.SelectedIndex = 0;  // 默认为"新增"
            dialog.Controls.Add(lblTouseStatus);
            dialog.Controls.Add(cmbTouseStatus);
            yPos += lineHeight;

            // 备注
            LabelControl lblRemark = new LabelControl();
            lblRemark.Text = "备注:";
            lblRemark.Location = new Point(labelX, yPos);
            MemoEdit txtRemark = new MemoEdit();
            txtRemark.Location = new Point(controlX, yPos);
            txtRemark.Size = new Size(controlWidth, 100);
            dialog.Controls.Add(lblRemark);
            dialog.Controls.Add(txtRemark);
            yPos += 110;

            // 按钮
            SimpleButton btnCancel = new SimpleButton();
            btnCancel.Text = "取消";
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.Location = new Point(controlX + controlWidth - 75, yPos);
            btnCancel.Size = new Size(75, 30);
            dialog.Controls.Add(btnCancel);

            SimpleButton btnSubmit = new SimpleButton();
            btnSubmit.Text = "提交";
            btnSubmit.Location = new Point(controlX + controlWidth - 160, yPos);
            btnSubmit.Size = new Size(75, 30);
            dialog.Controls.Add(btnSubmit);

            // 提交按钮点击事件
            btnSubmit.Click += async (s, e) => {
                // 验证必填字段
                if (string.IsNullOrWhiteSpace(cmbApplyingUnit.Text))
                {
                    MessageBox.Show("请选择申请单位", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                if (string.IsNullOrWhiteSpace(cmbIssuingUnit.Text))
                {
                    MessageBox.Show("请选择发放单位", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                if (string.IsNullOrWhiteSpace(txtApplicant.Text))
                {
                    MessageBox.Show("请输入申请人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }


                // 构建提交的数据
                var statusValue = ConvertChineseToStatus(cmbTouseStatus.Text);
                var urgentValue = radioUrgent.EditValue != null ? Convert.ToInt32(radioUrgent.EditValue) : 0;

                // 随机生成4位数字作为申请编号
                Random random = new Random();
                int randomNumber = random.Next(1000, 10000); // 生成4位数字

                // 创建JSON对象
                var applicationData = new JObject
                {
                    ["id"] = 0,
                    ["createTime"] = "",
                    ["createUserId"] = 0,
                    ["createUserName"] = "",
                    ["updateTime"] = "",
                    ["updateUserId"] = 0,
                    ["updateUserName"] = "",
                    ["isDeleted"] = false,
                    ["itemId"] = 0, 
                    ["applicationNumber"] = randomNumber,
                    ["applyingUnit"] = cmbApplyingUnit.Text,
                    ["issuingUnit"] = cmbIssuingUnit.Text,
                    ["applicationTime"] = dateApplicationTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["applicant"] = txtApplicant.Text,
                    ["urgent"] = urgentValue,
                    ["touseStatus"] = int.Parse(statusValue),
                    ["remark"] = txtRemark.Text
                };

                // 提交到API
                bool result = await SubmitApplicationAsync(applicationData);
                if (result)
                {
                    dialog.DialogResult = DialogResult.OK;
                    dialog.Close();
                }
            };

            // 显示对话框
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                // 刷新数据列表
                BindGrid();
            }
        }

        /// <summary>
        /// 提交申请数据到API
        /// </summary>
        /// <param name="data">申请数据</param>
        /// <returns>是否提交成功</returns>
        private async Task<bool> SubmitApplicationAsync(JObject data)
        {
                string url = "http://localhost:5192/api/DepartmentManagement/adopted-no-dto";
                
                // 显示等待提示
                arLblRecordCount.Text = "正在提交数据...";
                Application.DoEvents();
                
                // 将对象转换为JSON字符串
                string jsonData = data.ToString();
                System.Diagnostics.Debug.WriteLine($"提交的JSON数据: {jsonData}");
                
                try
                {
                    // 创建HttpClient
                    using (var client = new HttpClient())
                    {
                        // 设置Content-Type
                        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                        
                        // 发送POST请求
                        var response = await client.PostAsync(url, content);
                        
                        // 读取响应内容
                        string responseContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"API响应: {responseContent}");
                        
                        // 检查响应是否成功
                        if (response.IsSuccessStatusCode)
                        {
                            try
                            {
                                // 尝试直接读取JSON对象，不使用强类型反序列化
                                var jsonResponse = JObject.Parse(responseContent);
                                
                                // 显示成功消息
                                MessageBox.Show("提交成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return true;
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"解析API响应异常: {ex}");
                                MessageBox.Show($"处理响应时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            MessageBox.Show($"提交失败: HTTP {(int)response.StatusCode} - {response.ReasonPhrase}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"提交异常: {ex}");
                    MessageBox.Show($"提交过程中出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                
                return false;
        }

        /// <summary>
        /// 将申请单位代码转换为中文名称
        /// </summary>
        /// <param name="unitCode">申请单位代码</param>
        /// <returns>中文名称</returns>
        private string ConvertUnitCodeToChinese(string unitCode)
        {
            if (string.IsNullOrEmpty(unitCode))
                return unitCode;

            // 尝试解析为数字
            if (int.TryParse(unitCode, out int code))
            {
                switch (code)
                {
                    case 0: return "内科";
                    case 5: return "手术室";
                    default: return unitCode; // 未知代码，返回原值
                }
            }
            
            // 如果不是数字，返回原值
            return unitCode;
        }

        /// <summary>
        /// 初始化状态下拉框
        /// </summary>
        private void InitStatusComboBox()
        {
            // 添加状态选项
            arComboBoxStatus.Properties.Items.Clear();
            arComboBoxStatus.Properties.Items.Add("全部");
            arComboBoxStatus.Properties.Items.Add("新增");
            arComboBoxStatus.Properties.Items.Add("审核");
            arComboBoxStatus.Properties.Items.Add("接受");
            arComboBoxStatus.SelectedIndex = 0;
        }

        /// <summary>
        /// 将状态值转换为中文文本
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>中文状态文本</returns>
        private string ConvertStatusToChinese(string status)
        {
            // 尝试将状态解析为数字
            if (int.TryParse(status, out int statusCode))
            {
                switch (statusCode)
                {
                    case 1: return "新增";
                    case 2: return "审核";
                    case 3: return "接收";
                    default: return status; // 未知状态，返回原值
                }
            }
            
            // 如果已经是中文状态，则直接返回
            if (status == "新增" || status == "审核" || status == "接收")
            {
                return status;
            }
            
            // 其他情况，返回原值
            return status;
        }

        /// <summary>
        /// 将中文状态文本转换为状态值
        /// </summary>
        /// <param name="chineseStatus">中文状态文本</param>
        /// <returns>状态值</returns>
        private string ConvertChineseToStatus(string chineseStatus)
        {
            switch (chineseStatus)
            {
                case "新增": return "1";
                case "审核": return "2";
                case "接收": return "3";
                default: return chineseStatus; // 未知状态，返回原值
            }
        }

        /// <summary>
        /// 页面尺寸变化事件处理
        /// </summary>
        private void ComboBoxPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(arComboBoxPageSize.SelectedItem?.ToString(), out int size))
            {
                BindGrid();           // 重新加载数据
            }
        }

        /// <summary>
        /// 控件加载事件处理
        /// </summary>
        private void ApplicationRequisition_Load(object sender, EventArgs e)
        {
            InitGridView();            // 初始化表格视图
            // 清除日期过滤器的初始值
            this.arDateEditStart.EditValue = null;
            this.arDateEditEnd.EditValue = null;
            // 初始化当前月份的时间范围
            SetCurrentMonthDateRange();
            BindGrid();               // 绑定数据到表格
        }

        /// <summary>
        /// 设置当前月份的时间范围
        /// </summary>
        private void SetCurrentMonthDateRange()
        {
            DateTime now = DateTime.Now;
            DateTime firstDay = new DateTime(now.Year, now.Month, 1);
            DateTime lastDay = firstDay.AddMonths(1).AddDays(-1);
            
            this.arDateEditStart.EditValue = firstDay;
            this.arDateEditEnd.EditValue = lastDay;
        }

        /// <summary>
        /// 初始化表格视图设置
        /// </summary>
        private void InitGridView()
        {
            // 设置表头字体和背景色
            this.arGridView.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(221, 235, 247); // 淡蓝色
            this.arGridView.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.arGridView.Appearance.HeaderPanel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.arGridView.Appearance.HeaderPanel.Options.UseFont = true;
            this.arGridView.OptionsView.ShowGroupPanel = false;

            // 清空已有列
            this.arGridView.Columns.Clear();

            // 添加列（根据图片中的表格列）
            this.arGridView.Columns.AddVisible("id", "编号");
            this.arGridView.Columns.AddVisible("applicationNumber", "申请编号");
            this.arGridView.Columns.AddVisible("applyingUnit", "申请单位");
            this.arGridView.Columns.AddVisible("applicant", "申请人");
            this.arGridView.Columns.AddVisible("applicationTime", "申请时间");
            this.arGridView.Columns.AddVisible("touseStatus", "状态");

            // 设置日期格式
            var colApplyTime = this.arGridView.Columns.AddVisible("createTime", "创建时间");
            colApplyTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colApplyTime.DisplayFormat.FormatString = "yyyy-MM-dd";
            
            // 设置状态列样式（蓝色文本）
            var colStatus = this.arGridView.Columns["touseStatus"];
            if (colStatus != null)
            {
                colStatus.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(0, 112, 192); // 蓝色
                colStatus.AppearanceCell.Options.UseForeColor = true;
            }

            // 添加操作列
            AddOperationColumn();

            // 设置表格自动调整列宽
            arGridView.BestFitColumns();
        }

        /// <summary>
        /// 添加操作列
        /// </summary>
        private void AddOperationColumn()
        {
            // 创建按钮编辑器
            RepositoryItemButtonEdit buttonEdit = new RepositoryItemButtonEdit();
            
            // 添加修改按钮
            EditorButton editButton = new EditorButton(ButtonPredefines.Glyph);
            editButton.Caption = "修改";
            editButton.ToolTip = "修改";
            editButton.Kind = ButtonPredefines.Glyph;
            editButton.Appearance.ForeColor = Color.Blue;
            editButton.Tag = "Edit";
            buttonEdit.Buttons.Add(editButton);
            
            // 添加删除按钮
            EditorButton deleteButton = new EditorButton(ButtonPredefines.Glyph);
            deleteButton.Caption = "删除";
            deleteButton.ToolTip = "删除";
            deleteButton.Kind = ButtonPredefines.Delete;
            deleteButton.Appearance.ForeColor = Color.Red;
            deleteButton.Tag = "Delete";
            buttonEdit.Buttons.Add(deleteButton);

            // 配置按钮编辑器
            buttonEdit.TextEditStyle = TextEditStyles.HideTextEditor;
            buttonEdit.ButtonClick += ButtonEdit_ButtonClick;

            // 添加操作列并绑定按钮编辑器
            GridColumn operationColumn = this.arGridView.Columns.AddVisible("operation", "操作");
            operationColumn.ColumnEdit = buttonEdit;
            operationColumn.Width = 120;
            operationColumn.OptionsColumn.AllowEdit = true;
            operationColumn.OptionsColumn.FixedWidth = true;
            operationColumn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            operationColumn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            
            // 注册按钮事件
            this.arGridControl.RepositoryItems.Add(buttonEdit);
        }

        /// <summary>
        /// 操作列按钮点击事件处理
        /// </summary>
        private void ButtonEdit_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            try
            {
                // 获取当前行数据
                int rowHandle = this.arGridView.FocusedRowHandle;
                if (rowHandle < 0)
                {
                    MessageBox.Show("请先选择一行数据！");
                    return;
                }

                // 获取id和申请编号
                int id = Convert.ToInt32(this.arGridView.GetRowCellValue(rowHandle, "id"));
                string applicationNumber = this.arGridView.GetRowCellValue(rowHandle, "applicationNumber")?.ToString();
                
                if (id <= 0)
                {
                    MessageBox.Show("无法获取记录ID！");
                    return;
                }

                // 根据按钮类型执行不同操作
                if (e.Button.Tag.ToString() == "Edit")
                {
                    // 编辑功能（这里只是显示消息，实际开发中可以打开编辑窗口）
                    MessageBox.Show($"即将编辑申请编号为 {applicationNumber} 的记录");
                    // TODO: 实现编辑功能
                }
                else if (e.Button.Tag.ToString() == "Delete")
                {
                    // 删除功能
                    DeleteRecord(id);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"操作按钮错误: {ex}");
            }
        }

        /// <summary>
        /// 删除记录
        /// </summary>
        private async void DeleteRecord(int id)
        {
            // 确认删除
            DialogResult result = MessageBox.Show(
                $"确定要删除编号为 {id} 的记录吗？", 
                "确认删除", 
                MessageBoxButtons.YesNo, 
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
                return;

            try
            {
                // 显示等待状态
                arGridControl.UseWaitCursor = true;
                
                // 构建删除API URL - 修正API路径格式
                // 使用与查询相同的基础URL，确保一致性
                string deleteUrl = $"http://localhost:5192/api/DepartmentManagement/deladoped/adoptedId={id}";
                System.Diagnostics.Debug.WriteLine($"删除URL: {deleteUrl}");

                // 添加消息提示
                arLblRecordCount.Text = "正在删除数据...";
                Application.DoEvents();

                // 发送删除请求
                using (HttpClient client = new HttpClient())
                {
                    // 设置超时时间
                    client.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 添加调试信息
                    System.Diagnostics.Debug.WriteLine("开始发送DELETE请求...");
                    
                    // 发送请求
                    HttpResponseMessage response = await client.DeleteAsync(deleteUrl);
                    
                    // 添加调试信息
                    System.Diagnostics.Debug.WriteLine($"收到响应: 状态码 {response.StatusCode}");
                    
                    // 处理响应
                    if (response.IsSuccessStatusCode)
                    {
                        // 在响应成功时添加断点确认
                        System.Diagnostics.Debug.WriteLine("响应成功，正在读取内容...");
                        
                        string responseContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"响应内容: {responseContent}");
                        
                        var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(responseContent);
                        System.Diagnostics.Debug.WriteLine($"解析API响应: code={apiResponse?.code}, msg={apiResponse?.msg}");
                        
                        if (apiResponse != null && apiResponse.code == 200)
                        {
                            MessageBox.Show("删除成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            // 刷新表格数据
                            BindGrid();
                        }
                        else
                        {
                            MessageBox.Show($"删除失败: {apiResponse?.msg ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"请求失败: {response.StatusCode}");
                        
                        // 尝试获取错误响应内容
                        string errorContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"错误响应: {errorContent}");
                        
                        MessageBox.Show($"删除请求失败: HTTP {(int)response.StatusCode} - {response.ReasonPhrase}\n响应内容: {errorContent}", 
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                // 详细记录异常信息
                System.Diagnostics.Debug.WriteLine($"删除异常详情: {ex}");
                
                MessageBox.Show($"删除过程中出错: {ex.Message}\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复光标
                arGridControl.UseWaitCursor = false;
                // 恢复状态显示
                BindGrid();
            }
        }

        /// <summary>
        /// 加载测试数据（用于开发测试）
        /// </summary>
        private void BindStaticData()
        {
            var table = new System.Data.DataTable();
            table.Columns.Add("id", typeof(int));
            table.Columns.Add("applicationNumber", typeof(string));
            table.Columns.Add("applyingUnit", typeof(string));
            table.Columns.Add("applicant", typeof(string));
            table.Columns.Add("applicationTime", typeof(string));
            table.Columns.Add("touseStatus", typeof(string));
            table.Columns.Add("createTime", typeof(string));

            // 添加测试数据
            table.Rows.Add("SQ001", "外科", "张三", "2023-05-19 10:00:00", "新增", "2023-05-18");
            table.Rows.Add("SQ002", "内科", "李四", "2023-05-19 11:30:00", "审核", "2023-05-18");
            table.Rows.Add("SQ003", "急诊", "王五", "2023-05-19 14:20:00", "接受", "2023-05-18");

            this.arGridControl.DataSource = table;
        }

        /// <summary>
        /// 从API加载数据并绑定到表格
        /// </summary>
        private async void BindGrid()
        {
            try
            {
                //申请Url
                string baseUrl = "http://localhost:5172/api/DepartmentManagement/adopted";
                var queryParams = new List<string>();

                // 添加日期筛选参数 - 修正为使用applicationTime字段
                if (this.arDateEditStart.EditValue != null && DateTime.TryParse(this.arDateEditStart.EditValue.ToString(), out DateTime startDate))
                {
                    // 使用applicationTime字段作为日期筛选条件，并使用简单格式
                    string formattedStartDate = startDate.ToString("yyyy-MM-dd");
                    queryParams.Add($"startTime={Uri.EscapeDataString(formattedStartDate)}");
                    
                    // 记录日志
                    System.Diagnostics.Debug.WriteLine($"起始日期: {formattedStartDate}");
                }
                
                if (this.arDateEditEnd.EditValue != null && DateTime.TryParse(this.arDateEditEnd.EditValue.ToString(), out DateTime endDate))
                {
                    // 确保结束日期包含当天的所有记录
                    endDate = endDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
                    string formattedEndDate = endDate.ToString("yyyy-MM-dd HH:mm:ss");
                    queryParams.Add($"endTime={Uri.EscapeDataString(formattedEndDate)}");
                    
                    // 记录日志
                    System.Diagnostics.Debug.WriteLine($"结束日期: {formattedEndDate}");
                }
                
                // 添加状态筛选参数
                if (arComboBoxStatus.SelectedIndex > 0) // 如果不是"全部"
                {
                    string selectedStatus = arComboBoxStatus.SelectedItem.ToString();
                    // 将中文状态转换为数字状态值
                    string statusValue = ConvertChineseToStatus(selectedStatus);
                    // 使用touseStatus字段进行状态筛选
                    queryParams.Add($"touseStatus={Uri.EscapeDataString(statusValue)}");
                    
                    // 记录日志
                    System.Diagnostics.Debug.WriteLine($"状态值: {statusValue}");
                }

                // 构建完整URL
                string url = baseUrl;
                if (queryParams.Count > 0)
                {
                    url += "?" + string.Join("&", queryParams);
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"请求URL: {url}");
                
                // 显示正在查询的消息
                arLblRecordCount.Text = "正在查询数据...";
                Application.DoEvents();

                // 发送API请求
                var result = await HttpClientHelper.ClientAsync("GET", url, false);
                
                // 调试查看返回结果
                System.Diagnostics.Debug.WriteLine($"API返回数据: {result?.Substring(0, Math.Min(100, result?.Length ?? 0))}...");
                
                // 容错：接口返回内容为空或不是JSON
                if (string.IsNullOrWhiteSpace(result) || !result.TrimStart().StartsWith("{"))
                {
                    MessageBox.Show("接口返回内容不是有效的JSON：" + result);
                    arGridControl.DataSource = null;
                    return;
                }
                
                // 直接尝试解析为JObject，可以更好地检查数据结构
                try
                {
                    var jsonObj = JObject.Parse(result);
                    System.Diagnostics.Debug.WriteLine($"JSON解析成功，数据类型: {jsonObj["data"]?.Type}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"JSON解析错误: {ex.Message}");
                }
                
                // 解析JSON响应
                var jobj = JsonConvert.DeserializeObject<ApiResponse>(result);
                if (jobj != null && jobj.data != null)
                {
                    // 创建DataTable并定义列
                    var table = new System.Data.DataTable();
                    table.Columns.Add("id", typeof(int));
                    table.Columns.Add("applicationNumber", typeof(string));
                    table.Columns.Add("applyingUnit", typeof(string));
                    table.Columns.Add("applicant", typeof(string));
                    table.Columns.Add("applicationTime", typeof(string));
                    table.Columns.Add("touseStatus", typeof(string));
                    table.Columns.Add("createTime", typeof(string));
                    
                    // 填充数据
                    try
                    {
                        JArray dataArray = jobj.data as JArray;
                        if (dataArray != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"成功获取数据数组，条数: {dataArray.Count}");
                            
                            foreach (var item in dataArray)
                            {
                                try
                                {
                                    // 获取状态值并转换为中文
                                    string status = item["touseStatus"]?.ToString() ?? "";
                                    string chineseStatus = ConvertStatusToChinese(status);
                                    
                                    // 获取申请单位并转换为中文
                                    string applyingUnit = item["applyingUnit"]?.ToString() ?? "";
                                    string chineseUnit = ConvertUnitCodeToChinese(applyingUnit);
                                    
                                    table.Rows.Add(
                                        item["id"],
                                        item["applicationNumber"]?.ToString() ?? "",
                                        chineseUnit, // 显示中文申请单位
                                        item["applicant"]?.ToString() ?? "",
                                        item["applicationTime"]?.ToString() ?? "",
                                        chineseStatus, // 显示中文状态
                                        item["createTime"]?.ToString() ?? ""
                                    );
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"处理数据项异常: {ex.Message}");
                                }
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("数据不是数组格式");
                            System.Diagnostics.Debug.WriteLine($"数据格式: {jobj.data?.ToString()}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"处理数据异常: {ex.Message}");
                    }
                    
                    arGridControl.DataSource = table;
                    
                    // 显示记录数量
                    arLblRecordCount.Text = $"共 {table.Rows.Count} 条记录";
                }
                else
                {
                    arGridControl.DataSource = null;
                    arLblRecordCount.Text = "共 0 条记录";
                }
            }
            catch (Exception ex)
            {
                // 显示异常信息
                MessageBox.Show("获取领用申请数据失败：" + ex.Message);
                System.Diagnostics.Debug.WriteLine($"查询异常: {ex}");
                arGridControl.DataSource = null;
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void BtnSearch_Click1(object sender, EventArgs e)
        {
            BindGrid();
        }
    }
}
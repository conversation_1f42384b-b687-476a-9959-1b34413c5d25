﻿// StorageManagement 用户控件，用于存放管理
// 包含控件初始化和相关逻辑
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WinFormsAppDemo2.Common;
using Microsoft.VisualBasic;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    // 货架信息类
    public class ShelfInfo
    {
        public int id { get; set; }
        public string shelfName { get; set; }
    }

    // 待存放物品信息类
    public class WaitStoreItem
    {
        public int id { get; set; }
        public string materialName { get; set; }
        public string materialTypeName { get; set; }
        public int storesNum { get; set; }
    }

    // 存储格信息类
    public class StoreGridInfo
    {
        public int id { get; set; }
        public int floorNum { get; set; }
        public int latticeNum { get; set; }
        public string materialTypeName { get; set; }
        public string materialName { get; set; }
        public int storesNum { get; set; }
    }

    // 存储槽信息类
    public class StorageSlot
    {
        public string SlotId { get; set; }
        public string PackageName { get; set; }
        public string PackageBarcode { get; set; }
        public bool IsOccupied { get; set; }
        public DateTime? StorageTime { get; set; }
    }

    // 存放管理控件
    public partial class StorageManagement : UserControl
    {
        private List<StorageSlot> storageSlots;
        private List<ShelfInfo> shelfList;
        private ShelfInfo selectedShelf;
        private Dictionary<int, WaitStoreItem> waitStoreItemDict = new Dictionary<int, WaitStoreItem>();

        // 注释掉顶部的存放和查询按钮相关代码
        // private Button btnStore;
        // private Button btnQuery;

        // 构造函数，初始化控件
        public StorageManagement()
        {
            InitializeComponent(); // 初始化界面组件
            InitializeData();
            // InitializeEvents(); // 不用恢复全部，只需手动绑定货架选择事件
            // GenerateStorageSlots();
            this.Load += StorageManagement_Load;

            // 恢复货架选择事件绑定
            listBoxShelf.SelectedIndexChanged += ListBoxShelf_SelectedIndexChanged;

            // 绑定数据表格按钮点击事件
            dataGridViewPending.CellClick += DataGridViewPending_CellClick;
        }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        private async void StorageManagement_Load(object sender, EventArgs e)
        {
            await LoadShelvesFromApi();
            await LoadWaitStoresListAsync(); // 加载待存放列表
            // 货架列表加载后自动加载第一个货架详情
            if (shelfList != null && shelfList.Count > 0)
            {
                selectedShelf = shelfList[0]; // 设置默认选中的货架
                // await LoadShelfDetailById(shelfList[0].id); // 移除LoadShelfDetailById
            }
        }

        /// <summary>
        /// 从API加载货架列表
        /// </summary>
        private async Task LoadShelvesFromApi()
        {
            try
            {
                // 调用Common/GetShelvesList接口
                string url = ReadApiConfig.GetApiUrl("Common", "GetShelvesList");
                string result = await HttpClientHelper.ClientAsync("GET", url, false, null);

                if (!string.IsNullOrEmpty(result))
                {
                    try
                    {
                        // 直接解析为数组格式
                        var shelvesData = JsonConvert.DeserializeObject<List<ShelfInfo>>(result);
                        if (shelvesData != null && shelvesData.Count > 0)
                        {
                            // 更新货架列表
                            shelfList = shelvesData;
                            UpdateShelfListBox();

                            // 设置默认选中第一个货架
                            if (listBoxShelf.Items.Count > 0)
                            {
                                listBoxShelf.SelectedIndex = 0;
                                selectedShelf = shelfList[0];
                            }
                        }
                        else
                        {
                            MessageBox.Show("未获取到货架数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (JsonException ex)
                    {
                        // 如果直接解析数组失败，尝试解析为包装对象格式
                        try
                        {
                            var jobj = JObject.Parse(result);
                            if (jobj["code"]?.ToObject<int>() == 200)
                            {
                                var shelvesData = jobj["data"]?.ToObject<List<ShelfInfo>>();
                                if (shelvesData != null && shelvesData.Count > 0)
                                {
                                    shelfList = shelvesData;
                                    UpdateShelfListBox();

                                    if (listBoxShelf.Items.Count > 0)
                                    {
                                        listBoxShelf.SelectedIndex = 0;
                                        selectedShelf = shelfList[0];
                                    }
                                }
                                else
                                {
                                    MessageBox.Show("未获取到货架数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                            }
                            else
                            {
                                MessageBox.Show($"获取货架列表失败: {jobj["msg"]?.ToString() ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        catch
                        {
                            MessageBox.Show($"解析API响应失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("接口返回数据为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取货架列表异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新货架列表框
        /// </summary>
        private void UpdateShelfListBox()
        {
            listBoxShelf.Items.Clear();
            if (shelfList != null)
            {
                foreach (var shelf in shelfList)
                {
                    listBoxShelf.Items.Add(shelf.shelfName);
                }
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化货架列表
            shelfList = new List<ShelfInfo>();
            listBoxShelf.Items.Clear();

            // 初始化存储槽
            storageSlots = new List<StorageSlot>();
            /*
            for (int row = 1; row <= 3; row++)
            {
                for (int col = 1; col <= 4; col++)
                {
                    storageSlots.Add(new StorageSlot
                    {
                        SlotId = $"{row}-{col}",
                        PackageName = "",
                        PackageBarcode = "",
                        IsOccupied = false,
                        StorageTime = null
                    });
                }
            }
            */

            // 注释掉日期控件初始化，因为控件已被注释掉
            /*
            // 设置默认时间
            dateTimePickerStart.Value = DateTime.Today;
            dateTimePickerEnd.Value = DateTime.Today.AddDays(1).AddSeconds(-1);
            dateTimePickerSterilization.Value = DateTime.Today;
            dateTimePickerExpiration.Value = DateTime.Today.AddDays(30);
            dateTimePickerStorageTime.Value = DateTime.Now;
            */
        }

        /// <summary>
        /// 生成存储槽
        /// </summary>
        private void GenerateStorageSlots()
        {
            // tableLayoutPanelSlots.Controls.Clear();
            // for (int row = 0; row < 3; row++)
            // {
            //     for (int col = 0; col < 4; col++)
            //     {
            //         var slotIndex = row * 4 + col;
            //         var slot = storageSlots[slotIndex];
            //         var slotPanel = CreateSlotPanel(slot);
            //         tableLayoutPanelSlots.Controls.Add(slotPanel, col, row);
            //     }
            // }
        }

        /// <summary>
        /// 创建存储槽面板
        /// </summary>
        private Panel CreateSlotPanel(StorageSlot slot)
        {
            // var panel = new Panel
            // {
            //     Dock = DockStyle.Fill,
            //     Margin = new Padding(5),
            //     BackColor = Color.White,
            //     BorderStyle = BorderStyle.FixedSingle
            // };
            // var labelSlotId = new Label
            // {
            //     Text = slot.SlotId,
            //     Font = new Font("微软雅黑", 12, FontStyle.Bold),
            //     ForeColor = Color.FromArgb(0, 122, 204),
            //     AutoSize = true,
            //     Location = new Point(10, 10)
            // };
            // var labelPackage1 = new Label
            // {
            //     Text = $"治疗包[{slot.PackageBarcode}]",
            //     Font = new Font("微软雅黑", 9),
            //     ForeColor = Color.FromArgb(0, 122, 204),
            //     AutoSize = true,
            //     Location = new Point(10, 40),
            //     Cursor = Cursors.Hand
            // };
            // var labelPackage2 = new Label
            // {
            //     Text = $"治疗包[{slot.PackageBarcode}]",
            //     Font = new Font("微软雅黑", 9),
            //     ForeColor = Color.FromArgb(0, 122, 204),
            //     AutoSize = true,
            //     Location = new Point(10, 60),
            //     Cursor = Cursors.Hand
            // };
            // labelPackage1.Click += (s, e) => OnSlotClick(slot);
            // labelPackage2.Click += (s, e) => OnSlotClick(slot);
            // panel.Controls.Add(labelSlotId);
            // panel.Controls.Add(labelPackage1);
            // panel.Controls.Add(labelPackage2);
            // if (!slot.IsOccupied)
            // {
            //     labelPackage1.Text = "治疗包[2000232131]";
            //     labelPackage2.Text = "治疗包[2000232132]";
            //     panel.BackColor = Color.FromArgb(248, 249, 250);
            // }
            // return panel;
            return null;
        }

        /// <summary>
        /// 存储槽点击事件
        /// </summary>
        private void OnSlotClick(StorageSlot slot)
        {
            if (slot.IsOccupied)
            {
                // 显示存储信息
                ShowSlotInfo(slot);
            }
            else
            {
                // 显示存放对话框
                ShowStorageDialog(slot);
            }
        }

        /// <summary>
        /// 显示存储信息
        /// </summary>
        private void ShowSlotInfo(StorageSlot slot)
        {
            MessageBox.Show($"存储槽 {slot.SlotId} 信息：\n" +
                          $"包名称：{slot.PackageName}\n" +
                          $"包条码：{slot.PackageBarcode}\n" +
                          $"存放时间：{slot.StorageTime:yyyy-MM-dd HH:mm:ss}",
                          "存储信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示存放对话框
        /// </summary>
        private void ShowStorageDialog(StorageSlot slot)
        {
            var result = MessageBox.Show($"是否要将当前选择的物品存放到存储槽 {slot.SlotId}？",
                                        "确认存放", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // 执行存放操作
                PerformStorage(slot);
            }
        }

        /// <summary>
        /// 执行存放操作
        /// </summary>
        private void PerformStorage(StorageSlot slot)
        {
            // 从待存放列表中获取第一个物品
            if (dataGridViewPending.Rows.Count > 0)
            {
                var barcode = dataGridViewPending.Rows[0].Cells["columnBarcode"].Value?.ToString();
                var itemName = dataGridViewPending.Rows[0].Cells["columnItemName"].Value?.ToString();
                var pendingCount = dataGridViewPending.Rows[0].Cells["columnPendingCount"].Value?.ToString();

                if (!string.IsNullOrEmpty(barcode) && !string.IsNullOrEmpty(itemName))
                {
                    // 获取物品ID
                    int itemId = 0;
                    if (waitStoreItemDict.ContainsKey(0))
                    {
                        itemId = waitStoreItemDict[0].id;
                    }

                    // 更新存储槽信息
                    slot.PackageName = itemName;
                    slot.PackageBarcode = barcode;
                    slot.IsOccupied = true;
                    slot.StorageTime = DateTime.Now;

                    // 从待存放列表中移除
                    dataGridViewPending.Rows.RemoveAt(0);

                    // 更新字典中的索引
                    var newDict = new Dictionary<int, WaitStoreItem>();
                    foreach (var kvp in waitStoreItemDict)
                    {
                        if (kvp.Key > 0)
                        {
                            newDict[kvp.Key - 1] = kvp.Value;
                        }
                    }
                    waitStoreItemDict = newDict;

                    // 更新显示
                    GenerateStorageSlots();

                    // 注释掉更新存放信息，因为控件已被注释掉
                    // UpdateStorageInfo(slot);

                    MessageBox.Show($"物品 {itemName} (ID: {itemId}) 已成功存放到存储槽 {slot.SlotId}",
                                  "存放成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("待存放列表为空，无法执行存放操作",
                              "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 更新存放信息
        /// </summary>
        // 注释掉存放信息更新方法，因为控件已被注释掉
        /*
        private void UpdateStorageInfo(StorageSlot slot)
        {
            textBoxPackageType.Text = "治疗包";
            textBoxPackageBarcode.Text = slot.PackageBarcode;
            textBoxPackageName.Text = slot.PackageName;
            dateTimePickerSterilization.Value = DateTime.Today;
            textBoxValidDays.Text = "30";
            dateTimePickerExpiration.Value = DateTime.Today.AddDays(30);
            dateTimePickerStorageTime.Value = slot.StorageTime ?? DateTime.Now;
        }
        */

        // 注释掉存放按钮点击事件
        // private async void BtnStore_Click(object sender, EventArgs e)
        // {
        //     // 刷新待存放列表
        //     await LoadWaitStoresListAsync();
        // }

        // 注释掉查询按钮点击事件
        // private void BtnQuery_Click(object sender, EventArgs e)
        // {
        //     // 执行查询操作
        //     PerformQuery();
        // }

        /// <summary>
        /// 货架选择事件
        /// </summary>
        private async void ListBoxShelf_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxShelf.SelectedIndex >= 0 && shelfList != null && listBoxShelf.SelectedIndex < shelfList.Count)
            {
                selectedShelf = shelfList[listBoxShelf.SelectedIndex];
                // 获取货架格子数据并刷新
                string shelfUrl = ReadApiConfig.GetApiUrl("Wms", "GetStoresList") + $"?ShelfId={selectedShelf.id}";
                string shelfResult = await HttpClientHelper.ClientAsync("GET", shelfUrl, false, null);
                List<StoreGridInfo> gridList = new List<StoreGridInfo>();
                int layers = 1, grids = 1;
                if (!string.IsNullOrEmpty(shelfResult))
                {
                    var jobj = Newtonsoft.Json.Linq.JObject.Parse(shelfResult);
                    var dataArr = jobj["data"] as Newtonsoft.Json.Linq.JArray;
                    if (dataArr != null)
                    {
                        gridList = dataArr.ToObject<List<StoreGridInfo>>();
                        if (gridList.Count > 0)
                        {
                            layers = gridList.Max(g => g.floorNum);
                            grids = gridList.Max(g => g.latticeNum);
                        }
                    }
                }
                GenerateDynamicSlots(layers, grids, gridList);
            }
        }

        /// <summary>
        /// 日期时间控件值改变事件
        /// </summary>
        // 注释掉日期控件事件处理方法，因为控件已被注释掉
        /*
        private void DateTimePicker_ValueChanged(object sender, EventArgs e)
        {
            // 确保结束时间不早于开始时间
            if (dateTimePickerEnd.Value <= dateTimePickerStart.Value)
            {
                dateTimePickerEnd.Value = dateTimePickerStart.Value.AddDays(1).AddSeconds(-1);
            }
        }

        /// <summary>
        /// 灭菌日期改变事件
        /// </summary>
        private void DateTimePickerSterilization_ValueChanged(object sender, EventArgs e)
        {
            UpdateExpirationDate();
        }

        /// <summary>
        /// 有效天数改变事件
        /// </summary>
        private void TextBoxValidDays_TextChanged(object sender, EventArgs e)
        {
            UpdateExpirationDate();
        }

        /// <summary>
        /// 更新失效日期
        /// </summary>
        private void UpdateExpirationDate()
        {
            if (int.TryParse(textBoxValidDays.Text, out int validDays))
            {
                dateTimePickerExpiration.Value = dateTimePickerSterilization.Value.AddDays(validDays);
            }
        }
        */

        /// <summary>
        /// 执行查询操作
        /// </summary>
        private void PerformQuery()
        {
            var startTime = dateTimePickerStart.Value;
            var endTime = dateTimePickerEnd.Value;
            var shelfName = selectedShelf?.shelfName ?? "未选择货架";
            var shelfId = selectedShelf?.id ?? 0;

            // 这里可以调用API进行查询
            var message = $"查询条件：\n" +
                         $"货架ID：{shelfId}\n" +
                         $"货架名称：{shelfName}\n" +
                         $"时间范围：{startTime:yyyy-MM-dd HH:mm:ss} ~ {endTime:yyyy-MM-dd HH:mm:ss}";

            //MessageBox.Show(message, "查询信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 加载货架数据
        /// </summary>
        private void LoadShelfData(string shelfName)
        {
            // 这里可以根据选择的货架加载对应的数据
            // 目前只是显示一个提示信息
            var message = $"正在加载 {shelfName} 的数据...";

            // 模拟加载过程
            Task.Delay(500).ContinueWith(t =>
            {
                this.Invoke(new Action(() =>
                {
                    // 显示货架信息，包括ID和名称
                    var shelfId = selectedShelf?.id ?? 0;
                    //MessageBox.Show($"货架ID: {shelfId}\n货架名称: {shelfName}\n数据加载完成", "货架信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }));
            });
        }

        /// <summary>
        /// 加载待存放列表数据
        /// </summary>
        private async Task LoadWaitStoresListAsync()
        {
            try
            {
                dataGridViewPending.Rows.Clear();
                waitStoreItemDict.Clear();

                string url = ReadApiConfig.GetApiUrl("Wms", "GetWaitStoresLisat");
                string result = await HttpClientHelper.ClientAsync("GET", url, false, null);

                if (!string.IsNullOrEmpty(result))
                {
                    var items = JsonConvert.DeserializeObject<List<WaitStoreItem>>(result);
                    if (items != null && items.Count > 0)
                    {
                        foreach (var item in items)
                        {
                            int rowIndex = dataGridViewPending.Rows.Add(item.materialTypeName, item.materialName, item.storesNum);
                            // 记住id
                            waitStoreItemDict[rowIndex] = item;
                        }
                    }
                    else
                    {
                        //MessageBox.Show("暂无待存放物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("接口返回数据为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载待存放列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理数据表格按钮点击事件
        /// </summary>
        private void DataGridViewPending_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex == 3) // 存放按钮列（第4列，索引为3）
            {
                if (waitStoreItemDict.TryGetValue(e.RowIndex, out WaitStoreItem item))
                {
                    currentWaitStoreItem = item;
                    isSelectingGrid = true;
                    MessageBox.Show("请点击中间区域空闲格子进行存放！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        // 移除ShowStoreDialog和相关调用

        // 记录上次货架格子数据
        private List<StoreGridInfo> lastGridList = new List<StoreGridInfo>();

        // 当前待存放物品
        private WaitStoreItem currentWaitStoreItem = null;
        // 是否处于选择格子模式
        private bool isSelectingGrid = false;

        private void GenerateDynamicSlots(int layers, int grids, List<StoreGridInfo> gridList)
        {
            lastGridList = gridList;
            tableLayoutPanelSlots.Controls.Clear();
            tableLayoutPanelSlots.RowCount = layers;
            tableLayoutPanelSlots.ColumnCount = grids;
            tableLayoutPanelSlots.RowStyles.Clear();
            tableLayoutPanelSlots.ColumnStyles.Clear();

            for (int i = 0; i < layers; i++)
                tableLayoutPanelSlots.RowStyles.Add(new RowStyle(SizeType.Percent, 100f / layers));
            for (int j = 0; j < grids; j++)
                tableLayoutPanelSlots.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f / grids));

            for (int row = 0; row < layers; row++)
            {
                for (int col = 0; col < grids; col++)
                {
                    var panel = new Panel
                    {
                        Dock = DockStyle.Fill,
                        Margin = new Padding(5),
                        BackColor = Color.White,
                        BorderStyle = BorderStyle.FixedSingle,
                        Tag = new { FloorNum = row + 1, LatticeNum = col + 1 }
                    };

                    // 查找对应格子数据
                    var gridInfo = gridList?.FirstOrDefault(g => g.floorNum == row + 1 && g.latticeNum == col + 1);

                    var label = new Label
                    {
                        Text = $"{row + 1}-{col + 1}",
                        Font = new Font("微软雅黑", 12, FontStyle.Bold),
                        ForeColor = Color.FromArgb(0, 122, 204),
                        AutoSize = true,
                        Location = new Point(10, 10)
                    };
                    panel.Controls.Add(label);

                    if (gridInfo != null && gridInfo.id > 0)
                    {
                        var infoLabel = new Label
                        {
                            Text = $"{gridInfo.materialTypeName} {gridInfo.materialName} x{gridInfo.storesNum}",
                            Font = new Font("微软雅黑", 10),
                            ForeColor = Color.FromArgb(0, 122, 204),
                            AutoSize = true,
                            Location = new Point(10, 40)
                        };
                        panel.Controls.Add(infoLabel);
                    }
                    else
                    {
                        // 空闲格子可点击
                        panel.Cursor = Cursors.Hand;
                        panel.Click += Panel_Grid_Click;
                    }

                    tableLayoutPanelSlots.Controls.Add(panel, col, row);
                }
            }
        }

        private async void Panel_Grid_Click(object sender, EventArgs e)
        {
            if (!isSelectingGrid || currentWaitStoreItem == null)
                return;
            var panel = sender as Panel;
            dynamic tag = panel.Tag;
            int floorNum = tag.FloorNum;
            int latticeNum = tag.LatticeNum;

            // 直接使用全部待存放数量
            int storesNum = currentWaitStoreItem.storesNum;

            // 调用接口
            var param = new
            {
                id = currentWaitStoreItem.id,
                shelfId = selectedShelf.id,
                floorNum = floorNum,
                latticeNum = latticeNum,
                storesNum = storesNum
            };
            string url = WriteApiConfig.GetApiUrl("Wms", "MaterialStoreShelve");
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(param);
            try
            {
                var content = new System.Net.Http.StringContent(json, System.Text.Encoding.UTF8, "application/json");
                string result = await HttpClientHelper.ClientAsync("POST", url, false, content);
                MessageBox.Show("存放成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                await LoadWaitStoresListAsync();
                // 刷新货架格子数据
                string shelfUrl = ReadApiConfig.GetApiUrl("Wms", "GetStoresList") + $"?ShelfId={selectedShelf.id}";
                string shelfResult = await HttpClientHelper.ClientAsync("GET", shelfUrl, false, null);
                List<StoreGridInfo> gridList = new List<StoreGridInfo>();
                if (!string.IsNullOrEmpty(shelfResult))
                {
                    var jobj = Newtonsoft.Json.Linq.JObject.Parse(shelfResult);
                    var dataArr = jobj["data"] as Newtonsoft.Json.Linq.JArray;
                    if (dataArr != null)
                    {
                        gridList = dataArr.ToObject<List<StoreGridInfo>>();
                    }
                }
                GenerateDynamicSlots(tableLayoutPanelSlots.RowCount, tableLayoutPanelSlots.ColumnCount, gridList);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"存放失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                isSelectingGrid = false;
                currentWaitStoreItem = null;
            }
        }
    }
}
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.UserManagement
{
    public partial class UserListControl : UserControl
    {
        public UserListControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // UserListControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "UserListControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.UserListControl_Load);
            this.ResumeLayout(false);
        }

        private void UserListControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "用户列表";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Create search panel
            Panel searchPanel = new Panel();
            searchPanel.Dock = DockStyle.Top;
            searchPanel.Height = 70;
            searchPanel.Padding = new Padding(10);
            this.Controls.Add(searchPanel);

            // Username search
            Label usernameLabel = new Label();
            usernameLabel.Text = "用户名:";
            usernameLabel.Location = new Point(20, 25);
            usernameLabel.AutoSize = true;
            searchPanel.Controls.Add(usernameLabel);

            TextBox usernameTextBox = new TextBox();
            usernameTextBox.Location = new Point(80, 22);
            usernameTextBox.Width = 150;
            searchPanel.Controls.Add(usernameTextBox);

            // Department filter
            Label deptLabel = new Label();
            deptLabel.Text = "部门:";
            deptLabel.Location = new Point(250, 25);
            deptLabel.AutoSize = true;
            searchPanel.Controls.Add(deptLabel);

            ComboBox deptComboBox = new ComboBox();
            deptComboBox.Location = new Point(300, 22);
            deptComboBox.Width = 150;
            deptComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            deptComboBox.Items.AddRange(new object[] { "全部", "信息科", "外科", "内科", "消毒中心", "手术室" });
            deptComboBox.SelectedIndex = 0;
            searchPanel.Controls.Add(deptComboBox);

            // Search button
            Button searchButton = new Button();
            searchButton.Text = "搜索";
            searchButton.Location = new Point(470, 21);
            searchButton.Width = 80;
            searchButton.Height = 28;
            searchButton.BackColor = Color.FromArgb(0, 120, 215);
            searchButton.ForeColor = Color.White;
            searchPanel.Controls.Add(searchButton);

            // Reset button
            Button resetButton = new Button();
            resetButton.Text = "重置";
            resetButton.Location = new Point(560, 21);
            resetButton.Width = 80;
            resetButton.Height = 28;
            resetButton.BackColor = Color.FromArgb(192, 192, 192);
            resetButton.ForeColor = Color.Black;
            resetButton.Click += (s, ev) => {
                usernameTextBox.Text = "";
                deptComboBox.SelectedIndex = 0;
            };
            searchPanel.Controls.Add(resetButton);

            // Add button
            Button addButton = new Button();
            addButton.Text = "添加用户";
            addButton.Location = new Point(650, 21);
            addButton.Width = 90;
            addButton.Height = 28;
            addButton.BackColor = Color.FromArgb(46, 204, 113);
            addButton.ForeColor = Color.White;
            searchPanel.Controls.Add(addButton);

            // Create user data grid
            DataGridView userGrid = new DataGridView();
            userGrid.Dock = DockStyle.Fill;
            userGrid.BackgroundColor = Color.White;
            userGrid.BorderStyle = BorderStyle.None;
            userGrid.AllowUserToAddRows = false;
            userGrid.AllowUserToDeleteRows = false;
            userGrid.ReadOnly = true;
            userGrid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            userGrid.RowHeadersVisible = false;
            userGrid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            userGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            this.Controls.Add(userGrid);

            // Add columns
            userGrid.Columns.Add("Id", "ID");
            userGrid.Columns.Add("Username", "用户名");
            userGrid.Columns.Add("RealName", "姓名");
            userGrid.Columns.Add("Role", "角色");
            userGrid.Columns.Add("Department", "所属部门");
            userGrid.Columns.Add("Phone", "联系电话");
            userGrid.Columns.Add("Status", "状态");
            userGrid.Columns.Add("CreateTime", "创建时间");
            
            // Add action buttons
            DataGridViewButtonColumn editColumn = new DataGridViewButtonColumn();
            editColumn.HeaderText = "编辑";
            editColumn.Text = "编辑";
            editColumn.UseColumnTextForButtonValue = true;
            editColumn.Width = 60;
            userGrid.Columns.Add(editColumn);

            DataGridViewButtonColumn deleteColumn = new DataGridViewButtonColumn();
            deleteColumn.HeaderText = "删除";
            deleteColumn.Text = "删除";
            deleteColumn.UseColumnTextForButtonValue = true;
            deleteColumn.Width = 60;
            userGrid.Columns.Add(deleteColumn);

            // Add sample data
            userGrid.Rows.Add("1", "admin", "管理员", "系统管理员", "信息科", "13800000000", "正常", "2023-01-10");
            userGrid.Rows.Add("2", "doctor1", "张医生", "医生", "外科", "13811111111", "正常", "2023-02-15");
            userGrid.Rows.Add("3", "nurse1", "李护士", "护士", "内科", "13822222222", "正常", "2023-03-20");
            userGrid.Rows.Add("4", "operator1", "王操作员", "操作员", "消毒中心", "13833333333", "正常", "2023-04-25");
            userGrid.Rows.Add("5", "manager1", "赵经理", "部门经理", "消毒中心", "13844444444", "正常", "2023-05-30");
            userGrid.Rows.Add("6", "doctor2", "刘医生", "医生", "内科", "13855555555", "正常", "2023-06-05");
            userGrid.Rows.Add("7", "nurse2", "孙护士", "护士", "外科", "13866666666", "正常", "2023-07-10");
            userGrid.Rows.Add("8", "doctor3", "钱医生", "医生", "手术室", "13877777777", "正常", "2023-08-15");
            userGrid.Rows.Add("9", "nurse3", "周护士", "护士", "手术室", "13888888888", "正常", "2023-09-20");
            userGrid.Rows.Add("10", "operator2", "吴操作员", "操作员", "消毒中心", "13899999999", "正常", "2023-10-25");

            // Add cell click event for action buttons
            userGrid.CellClick += (s, ev) => {
                // Check if a button column is clicked
                if (ev.RowIndex >= 0)
                {
                    string username = userGrid.Rows[ev.RowIndex].Cells["Username"].Value.ToString();
                    
                    if (ev.ColumnIndex == editColumn.Index)
                    {
                        MessageBox.Show($"编辑用户: {username}", "编辑", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else if (ev.ColumnIndex == deleteColumn.Index)
                    {
                        if (MessageBox.Show($"确定要删除用户 '{username}' 吗？", "确认删除", 
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        {
                            userGrid.Rows.RemoveAt(ev.RowIndex);
                            MessageBox.Show($"用户 '{username}' 已删除", "删除成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            };
        }
    }
} 
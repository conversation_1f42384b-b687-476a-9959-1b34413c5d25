<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- if ShouldIncludeNativeSkiaSharp == False then don't include the native libSkiaSharp -->
    <PropertyGroup>
        <ShouldIncludeNativeSkiaSharp Condition=" '$(ShouldIncludeNativeSkiaSharp)' == '' ">True</ShouldIncludeNativeSkiaSharp>
        <_AppIsFullMac Condition=" '$(XamarinMacFrameworkRoot)' != '' and '$(TargetFrameworkIdentifier)' != 'Xamarin.Mac' and '$(UseXamMacFullFramework)' == 'True' and ('$(OutputType)' == 'Exe' or '$(IsAppExtension)' == 'True') ">True</_AppIsFullMac>
    </PropertyGroup>

    <!-- handle the case where this is a Xamarin.Mac Full app/extension -->
    <ItemGroup Condition=" '$(ShouldIncludeNativeSkiaSharp)' != 'False' and '$(_AppIsFullMac)' == 'True' ">
        <NativeReference Include="$(MSBuildThisFileDirectory)..\..\runtimes\osx\native\libSkiaSharp*.dylib">
            <Kind>Dynamic</Kind>
            <Visible>False</Visible>
        </NativeReference>
    </ItemGroup>

    <!-- copy the native files to the output directory -->
    <ItemGroup Condition=" '$(ShouldIncludeNativeSkiaSharp)' != 'False' and '$(_AppIsFullMac)' != 'True' ">

        <!-- Windows -->
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-x86\native\libSkiaSharp*.dll">
            <Dir>x86\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-x64\native\libSkiaSharp*.dll">
            <Dir>x64\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-arm64\native\libSkiaSharp*.dll">
            <Dir>arm64\</Dir>
        </_NativeSkiaSharpFile>

        <!-- Linux -->
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-x86\native\libSkiaSharp*.so">
            <Dir>x86\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-x64\native\libSkiaSharp*.so">
            <Dir>x64\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-arm\native\libSkiaSharp*.so">
            <Dir>arm\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-arm64\native\libSkiaSharp*.so">
            <Dir>arm64\</Dir>
        </_NativeSkiaSharpFile>

        <!-- Linux: Musl -->
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-musl-x86\native\libSkiaSharp*.so">
            <Dir>musl-x86\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-musl-x64\native\libSkiaSharp*.so">
            <Dir>musl-x64\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-musl-arm\native\libSkiaSharp*.so">
            <Dir>musl-arm\</Dir>
        </_NativeSkiaSharpFile>
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-musl-arm64\native\libSkiaSharp*.so">
            <Dir>musl-arm64\</Dir>
        </_NativeSkiaSharpFile>

        <!-- macOS -->
        <_NativeSkiaSharpFile Include="$(MSBuildThisFileDirectory)..\..\runtimes\osx\native\libSkiaSharp*.dylib" />

        <!-- include everything -->
        <Content Include="@(_NativeSkiaSharpFile)">
            <Link>%(Dir)%(Filename)%(Extension)</Link>
            <Visible>False</Visible>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>

    </ItemGroup>

</Project>
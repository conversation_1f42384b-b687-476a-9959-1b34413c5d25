using System;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public static class TestCleaningRegistration
    {
        public static void TestFormCreation()
        {
            try
            {
                using (var form = new CleaningRegistrationForm())
                {
                    MessageBox.Show("CleaningRegistrationForm 创建成功！", "测试结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"CleaningRegistrationForm 创建失败：{ex.Message}", "测试结果", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

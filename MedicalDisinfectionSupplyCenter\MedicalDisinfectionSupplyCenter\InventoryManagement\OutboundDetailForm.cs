using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json.Linq;
using System;
using System.Data;
using System.Drawing;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    public partial class OutboundDetailForm : DevExpress.XtraEditors.XtraForm
    {
        private readonly int _outboundId;
        private readonly HttpClient _httpClient;

        public OutboundDetailForm(int outboundId)
        {
            InitializeComponent();
            _outboundId = outboundId;
            _httpClient = new HttpClient();
            this.Load += OutboundDetailForm_Load;
        }

        private async void OutboundDetailForm_Load(object sender, EventArgs e)
        {
            await LoadOutboundDetail();
        }

        private async Task LoadOutboundDetail()
        {
            try
            {
                Cursor = Cursors.WaitCursor;

                string url = ReadApiConfig.GetApiUrl("Wms", "GetOutCauseDetailById") + $"?id={_outboundId}";

                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    string jsonResult = await response.Content.ReadAsStringAsync();
                    var result = JObject.Parse(jsonResult);

                    if (result["code"]?.ToString() == "200" && result["data"] != null)
                    {
                        var data = (JObject)result["data"];
                        FillBasicInfo(data);
                        FillDetailList(data);
                    }
                    else
                    {
                        MessageBox.Show($"获取详情失败: {result["msg"]?.ToString()}", "错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"网络请求失败: {response.StatusCode}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载详情失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Cursor = Cursors.Default;
            }
        }

        private void FillBasicInfo(JObject data)
        {
            // 出库编码
            lblOutCode.Text = data["outCode"]?.ToString() ?? "";

            // 出库仓库 - 优先显示名称
            lblOutWarehouse.Text = data["outWarehouseName"]?.ToString() ?? data["outWarehouse"]?.ToString() ?? "";

            // 申请科室 - 优先显示名称
            lblApplyingUnit.Text = data["applyingUnitName"]?.ToString() ?? data["applyingUnit"]?.ToString() ?? "";

            // 出库原因 - 优先显示名称
            lblOutReason.Text = data["outReasonName"]?.ToString() ?? data["outReason"]?.ToString() ?? "";

            // 审批状态 - 优先显示名称
            lblAuditState.Text = data["auditStateName"]?.ToString() ?? data["auditState"]?.ToString() ?? "";

            // 审批人
            lblAuditName.Text = data["auditName"]?.ToString() ?? "";

            // 处理日期格式
            if (data["auditDate"] != null && !string.IsNullOrEmpty(data["auditDate"].ToString()))
            {
                DateTime auditDate = DateTime.Parse(data["auditDate"].ToString());
                lblAuditDate.Text = auditDate.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                lblAuditDate.Text = "未审批";
            }

            if (data["createTime"] != null && !string.IsNullOrEmpty(data["createTime"].ToString()))
            {
                DateTime createTime = DateTime.Parse(data["createTime"].ToString());
                lblCreateTime.Text = createTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                lblCreateTime.Text = "未知";
            }
        }

        private void FillDetailList(JObject data)
        {
            var table = new DataTable();
            table.Columns.Add("materialTypeNmae", typeof(string));
            table.Columns.Add("materialCode", typeof(string));
            table.Columns.Add("materialName", typeof(string));
            table.Columns.Add("outNum", typeof(string));

            var details = data["details"];
            if (details != null && details.HasValues)
            {
                foreach (var item in details)
                {
                    table.Rows.Add(
                        item["materialTypeNmae"]?.ToString() ?? "",
                        item["materialCode"]?.ToString() ?? "",
                        item["materialName"]?.ToString() ?? "",
                        item["outNum"]?.ToString() ?? ""
                    );
                }
            }

            gridControlDetails.DataSource = table;
            SetDetailColumnCaptions();
        }

        private void SetDetailColumnCaptions()
        {
            var view = gridControlDetails.MainView as GridView;
            if (view != null)
            {
                view.BeginUpdate();
                try
                {
                    // 设置表格样式
                    BeautifyGridView(view);

                    foreach (DevExpress.XtraGrid.Columns.GridColumn column in view.Columns)
                    {
                        switch (column.FieldName)
                        {
                            case "materialTypeNmae":
                                column.Caption = "物料类型";
                                column.Width = 120;
                                break;
                            case "materialCode":
                                column.Caption = "物料编码";
                                column.Width = 150;
                                break;
                            case "materialName":
                                column.Caption = "物料名称";
                                column.Width = 200;
                                break;
                            case "outNum":
                                column.Caption = "出库数量";
                                column.Width = 100;
                                break;
                        }

                        // 设置列的对齐方式
                        column.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                        column.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                    }
                }
                finally
                {
                    view.EndUpdate();
                }
            }
        }

        /// <summary>
        /// 美化GridView样式
        /// </summary>
        private void BeautifyGridView(GridView gridView)
        {
            // 表头样式
            gridView.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
            gridView.Appearance.HeaderPanel.ForeColor = System.Drawing.Color.White;
            gridView.Appearance.HeaderPanel.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            gridView.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            gridView.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 行样式
            gridView.Appearance.Row.Font = new System.Drawing.Font("微软雅黑", 9F);
            gridView.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(51, 51, 51);
            gridView.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 奇偶行样式
            gridView.Appearance.EvenRow.BackColor = System.Drawing.Color.White;
            gridView.Appearance.OddRow.BackColor = System.Drawing.Color.FromArgb(248, 249, 250);

            // 选中行样式
            gridView.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(230, 247, 255);
            gridView.Appearance.FocusedRow.ForeColor = System.Drawing.Color.Black;

            // 行高
            gridView.RowHeight = 40;

            // 边框和选项
            gridView.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            gridView.OptionsView.EnableAppearanceEvenRow = true;
            gridView.OptionsView.EnableAppearanceOddRow = true;
            gridView.OptionsView.ShowIndicator = false;
            gridView.OptionsView.ShowGroupPanel = false;
        }

        private void btnClose_Click(object sender, System.EventArgs e)
        {
            this.Close();
        }
    }
}
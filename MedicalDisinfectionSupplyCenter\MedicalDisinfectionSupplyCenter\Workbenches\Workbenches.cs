﻿using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using System.Linq;

// 临时注释 DevExpress 引用以解决编译问题
// using DevExpress.XtraEditors;
// using DevExpress.XtraEditors.Controls;
// using DevExpress.XtraGrid;
// using DevExpress.XtraGrid.Views.Grid;
// using DevExpress.XtraGrid.Columns;
// using DevExpress.Utils;
// using DevExpress.XtraCharts;

namespace MedicalDisinfectionSupplyCenter.Workbenches
{
    public partial class Workbenches : UserControl
    {
        private Panel headerPanel;
        private Panel statsPanel;
        private Panel contentPanel;
        private Panel chartPanel;
        private Panel quickActionsPanel;
        private Panel bottomPanel;
        private Timer timeUpdateTimer;
        private Label dateTimeLabel;
        private Label userNameLabel;
        private Label departmentLabel;
        private Panel workChart; // 临时替换为 Panel

        // 用户信息属性
        public string UserName { get; set; } = "张三";
        public string Department { get; set; } = "消毒供应中心";

        public Workbenches()
        {
            InitializeComponent();
            InitializeWorkbenchUI();
            InitializeTimer();
            LoadUserInfoFromParent();
        }

        /// <summary>
        /// 从父窗体获取用户信息
        /// </summary>
        private void LoadUserInfoFromParent()
        {
            try
            {
                // 尝试从父窗体获取用户信息
                var parentForm = this.FindForm() as FluentDesignForm1;
                if (parentForm != null)
                {
                    // 通过反射获取私有字段
                    var userNameField = parentForm.GetType().GetField("_userName",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    var roleNameField = parentForm.GetType().GetField("_roleName",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (userNameField != null && roleNameField != null)
                    {
                        string userName = userNameField.GetValue(parentForm)?.ToString() ?? "张三";
                        string roleName = roleNameField.GetValue(parentForm)?.ToString() ?? "消毒供应中心";
                        SetUserInfo(userName, roleName);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取用户信息失败: {ex.Message}");
            }
        }

        private void InitializeWorkbenchUI()
        {
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Padding = new Padding(20);

            // 创建主要布局面板
            CreateHeaderPanel();
            CreateStatsPanel();
            CreateContentPanel();
            CreateBottomPanel();
        }

        private void InitializeTimer()
        {
            // 创建定时器，每秒更新时间
            timeUpdateTimer = new Timer();
            timeUpdateTimer.Interval = 1000; // 1秒
            timeUpdateTimer.Tick += UpdateDateTime;
            timeUpdateTimer.Start();
        }

        private void UpdateDateTime(object sender, EventArgs e)
        {
            if (dateTimeLabel != null)
            {
                var now = DateTime.Now;
                string lunarDate = GetLunarDate(now);
                dateTimeLabel.Text = $"{now:yyyy年M月d日}星期{GetDayOfWeekInChinese(now.DayOfWeek)} {now:HH:mm:ss} {lunarDate}";
            }
        }

        /// <summary>
        /// 获取农历日期（简化版本）
        /// </summary>
        private string GetLunarDate(DateTime date)
        {
            // 这里使用简化的农历计算，实际项目中可以使用专门的农历库
            int[] lunarDays = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30 };
            string[] lunarMonths = { "正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊" };
            
            // 简化的农历计算（仅作演示）
            int dayOfYear = date.DayOfYear;
            int lunarMonth = ((dayOfYear - 1) / 30) % 12;
            int lunarDay = ((dayOfYear - 1) % 30) + 1;
            
            return $"农历{lunarMonths[lunarMonth]}月{GetLunarDayText(lunarDay)}";
        }

        private string GetLunarDayText(int day)
        {
            string[] dayNames = { "", "初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
                                 "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十",
                                 "廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十" };
            return dayNames[day];
        }

        /// <summary>
        /// 设置用户信息
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="department">部门</param>
        public void SetUserInfo(string userName, string department = "消毒供应中心")
        {
            UserName = userName;
            Department = department;

            // 更新界面显示
            if (userNameLabel != null)
            {
                userNameLabel.Text = UserName;
            }
            if (departmentLabel != null)
            {
                departmentLabel.Text = Department;
            }
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel();
            headerPanel.Height = 70;
            headerPanel.Dock = DockStyle.Top;
            headerPanel.BackColor = Color.White;
            headerPanel.Padding = new Padding(20, 15, 20, 15);
            headerPanel.BorderStyle = BorderStyle.FixedSingle;

            // 用户头像
            PictureBox avatar = new PictureBox();
            avatar.Size = new Size(45, 45);
            avatar.Location = new Point(20, 12);
            avatar.BackColor = Color.FromArgb(255, 193, 158);
            avatar.BorderStyle = BorderStyle.None;
            // 圆形头像效果
            avatar.Paint += (s, e) => {
                e.Graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (Brush brush = new SolidBrush(Color.FromArgb(255, 193, 158)))
                {
                    e.Graphics.FillEllipse(brush, 0, 0, 44, 44);
                }
                // 绘制用户图标
                using (Brush textBrush = new SolidBrush(Color.White))
                {
                    Font iconFont = new Font("Segoe UI Symbol", 18, FontStyle.Bold);
                    string userIcon = "👤";
                    SizeF textSize = e.Graphics.MeasureString(userIcon, iconFont);
                    float x = (avatar.Width - textSize.Width) / 2;
                    float y = (avatar.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(userIcon, iconFont, textBrush, x, y);
                }
            };

            // 用户名标签
            userNameLabel = new Label();
            userNameLabel.Text = UserName;
            userNameLabel.Font = new Font("微软雅黑", 14, FontStyle.Bold);
            userNameLabel.ForeColor = Color.FromArgb(51, 51, 51);
            userNameLabel.Location = new Point(80, 10);
            userNameLabel.AutoSize = false;
            userNameLabel.Size = new Size(200, 25);

            // 部门标签
            departmentLabel = new Label();
            departmentLabel.Text = Department;
            departmentLabel.Font = new Font("微软雅黑", 11);
            departmentLabel.ForeColor = Color.FromArgb(108, 117, 125);
            departmentLabel.Location = new Point(80, 35);
            departmentLabel.AutoSize = false;
            departmentLabel.Size = new Size(200, 20);

            // 日期时间标签
            dateTimeLabel = new Label();
            dateTimeLabel.Text = DateTime.Now.ToString("yyyy年M月d日星期") +
                               GetDayOfWeekInChinese(DateTime.Now.DayOfWeek) + " " +
                               DateTime.Now.ToString("HH:mm:ss");
            dateTimeLabel.Font = new Font("微软雅黑", 11);
            dateTimeLabel.ForeColor = Color.FromArgb(108, 117, 125);
            dateTimeLabel.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // 在面板Resize事件中调整时间标签位置
            headerPanel.Resize += (s, e) => {
                if (dateTimeLabel != null)
                {
                    dateTimeLabel.Location = new Point(headerPanel.Width - 350, 25);
                }
            };

            headerPanel.Controls.AddRange(new Control[] { avatar, userNameLabel, departmentLabel, dateTimeLabel });
            this.Controls.Add(headerPanel);

            // 初始设置时间标签位置
            this.Load += (s, e) => {
                if (dateTimeLabel != null)
                {
                    dateTimeLabel.Location = new Point(headerPanel.Width - 350, 25);
                }
            };
        }

        private string GetDayOfWeekInChinese(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Monday: return "一";
                case DayOfWeek.Tuesday: return "二";
                case DayOfWeek.Wednesday: return "三";
                case DayOfWeek.Thursday: return "四";
                case DayOfWeek.Friday: return "五";
                case DayOfWeek.Saturday: return "六";
                case DayOfWeek.Sunday: return "日";
                default: return "";
            }
        }

        private void CreateStatsPanel()
        {
            statsPanel = new Panel();
            statsPanel.Height = 120;
            statsPanel.Dock = DockStyle.Top;
            statsPanel.BackColor = Color.Transparent;
            statsPanel.Padding = new Padding(0, 15, 0, 15);
            statsPanel.BorderStyle = BorderStyle.None;

            // 统计卡片数据 - 按照图片顺序和颜色
            var statsData = new[]
            {
                new { Title = "待回收", Value = "156", Color = Color.FromArgb(52, 144, 220), Icon = "🛒" },
                new { Title = "清洗中", Value = "340", Color = Color.FromArgb(116, 185, 255), Icon = "🧽" },
                new { Title = "消毒中", Value = "120", Color = Color.FromArgb(156, 102, 192), Icon = "🧴" },
                new { Title = "灭菌中", Value = "10", Color = Color.FromArgb(220, 53, 69), Icon = "🔥" },
                new { Title = "待发放", Value = "112", Color = Color.FromArgb(40, 167, 69), Icon = "📦" }
            };

            int cardWidth = 200;
            int cardSpacing = 25;
            int startX = 20;

            for (int i = 0; i < statsData.Length; i++)
            {
                Panel card = CreateStatsCard(statsData[i].Title, statsData[i].Value, statsData[i].Color, statsData[i].Icon);
                card.Location = new Point(startX + i * (cardWidth + cardSpacing), 10);
                card.Size = new Size(cardWidth, 100);
                statsPanel.Controls.Add(card);
            }

            this.Controls.Add(statsPanel);
        }

        private Panel CreateStatsCard(string title, string value, Color color, string icon)
        {
            Panel card = new Panel();
            card.BackColor = Color.White;
            card.BorderStyle = BorderStyle.FixedSingle;

            // 图标背景
            Panel iconPanel = new Panel();
            iconPanel.Size = new Size(55, 55);
            iconPanel.Location = new Point(20, 20);
            iconPanel.BackColor = color;
            iconPanel.BorderStyle = BorderStyle.None;
            iconPanel.Paint += (s, e) => {
                e.Graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (Brush brush = new SolidBrush(color))
                {
                    e.Graphics.FillRoundedRectangle(brush, 0, 0, iconPanel.Width, iconPanel.Height, 8);
                }
                // 绘制图标文字
                using (Brush textBrush = new SolidBrush(Color.White))
                {
                    Font iconFont = new Font("Segoe UI Emoji", 20);
                    SizeF textSize = e.Graphics.MeasureString(icon, iconFont);
                    float x = (iconPanel.Width - textSize.Width) / 2;
                    float y = (iconPanel.Height - textSize.Height) / 2;
                    e.Graphics.DrawString(icon, iconFont, textBrush, x, y);
                }
            };

            // 数值标签
            Label valueLabel = new Label();
            valueLabel.Text = value;
            valueLabel.Font = new Font("微软雅黑", 28, FontStyle.Bold);
            valueLabel.ForeColor = Color.FromArgb(51, 51, 51);
            valueLabel.Location = new Point(90, 15);
            valueLabel.AutoSize = false;
            valueLabel.Size = new Size(100, 35);

            // 标题标签
            Label titleLabel = new Label();
            titleLabel.Text = title;
            titleLabel.Font = new Font("微软雅黑", 11);
            titleLabel.ForeColor = Color.FromArgb(108, 117, 125);
            titleLabel.Location = new Point(90, 55);
            titleLabel.AutoSize = false;
            titleLabel.Size = new Size(100, 20);

            card.Controls.AddRange(new Control[] { iconPanel, valueLabel, titleLabel });
            return card;
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.Transparent;
            contentPanel.Padding = new Padding(0, 15, 0, 15);
            contentPanel.BorderStyle = BorderStyle.None;

            // 左侧图表面板
            CreateChartPanel();

            // 右侧快捷操作面板
            CreateQuickActionsPanel();

            this.Controls.Add(contentPanel);
        }

        private void CreateChartPanel()
        {
            chartPanel = new Panel();
            chartPanel.Width = this.Width - 350;
            chartPanel.Dock = DockStyle.Left;
            chartPanel.BackColor = Color.White;
            chartPanel.Margin = new Padding(0, 0, 20, 0);
            chartPanel.BorderStyle = BorderStyle.FixedSingle;

            // 图表标题
            Label chartTitle = new Label();
            chartTitle.Text = "今日工作";
            chartTitle.Font = new Font("微软雅黑", 16, FontStyle.Bold);
            chartTitle.ForeColor = Color.FromArgb(51, 51, 51);
            chartTitle.Location = new Point(25, 25);
            chartTitle.AutoSize = false;
            chartTitle.Size = new Size(200, 30);

            // 创建简单的图表面板（替代DevExpress图表）
            workChart = new Panel();
            workChart.Location = new Point(25, 70);
            workChart.Size = new Size(chartPanel.Width - 50, chartPanel.Height - 120);
            workChart.BackColor = Color.White;
            workChart.BorderStyle = BorderStyle.None;

            // 添加简单的图表说明
            Label chartInfo = new Label();
            chartInfo.Text = "图表功能需要 DevExpress 组件支持\n当前显示简化版本";
            chartInfo.Font = new Font("微软雅黑", 12);
            chartInfo.ForeColor = Color.FromArgb(108, 117, 125);
            chartInfo.Location = new Point(50, 50);
            chartInfo.AutoSize = true;
            workChart.Controls.Add(chartInfo);

            chartPanel.Controls.AddRange(new Control[] { chartTitle, workChart });
            contentPanel.Controls.Add(chartPanel);
        }

        private void ConfigureWorkChart()
        {
            // 简化的图表配置方法
            // 实际的图表功能需要 DevExpress 组件支持
        }

        private void CreateQuickActionsPanel()
        {
            quickActionsPanel = new Panel();
            quickActionsPanel.Width = 300;
            quickActionsPanel.Dock = DockStyle.Right;
            quickActionsPanel.BackColor = Color.Transparent;
            quickActionsPanel.Padding = new Padding(20, 0, 20, 0);
            quickActionsPanel.BorderStyle = BorderStyle.None;

            // 快捷操作标题
            Label quickTitle = new Label();
            quickTitle.Text = "快捷操作";
            quickTitle.Font = new Font("微软雅黑", 16, FontStyle.Bold);
            quickTitle.ForeColor = Color.FromArgb(51, 51, 51);
            quickTitle.Location = new Point(20, 25);
            quickTitle.AutoSize = false;
            quickTitle.Size = new Size(200, 30);

            // 快捷操作按钮
            var quickActions = new[]
            {
                new { Name = "器械回收", Color = Color.FromArgb(52, 144, 220), Icon = "🛒" },
                new { Name = "清洗管理", Color = Color.FromArgb(116, 185, 255), Icon = "🧽" },
                new { Name = "消毒管理", Color = Color.FromArgb(156, 102, 192), Icon = "🧴" },
                new { Name = "灭菌管理", Color = Color.FromArgb(220, 53, 69), Icon = "🔥" },
                new { Name = "发放管理", Color = Color.FromArgb(40, 167, 69), Icon = "📦" }
            };

            int buttonHeight = 60;
            int buttonSpacing = 15;
            int startY = 70;

            for (int i = 0; i < quickActions.Length; i++)
            {
                Button actionBtn = CreateQuickActionButton(quickActions[i].Name, quickActions[i].Color, quickActions[i].Icon);
                actionBtn.Location = new Point(20, startY + i * (buttonHeight + buttonSpacing));
                actionBtn.Size = new Size(240, buttonHeight);
                quickActionsPanel.Controls.Add(actionBtn);
            }

            quickActionsPanel.Controls.Add(quickTitle);
            contentPanel.Controls.Add(quickActionsPanel);
        }

        private Button CreateQuickActionButton(string text, Color color, string icon)
        {
            Button btn = new Button();
            btn.Text = text;
            btn.BackColor = Color.White;
            btn.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            btn.ForeColor = Color.FromArgb(51, 51, 51);
            btn.FlatStyle = FlatStyle.Flat;
            btn.FlatAppearance.BorderColor = Color.FromArgb(230, 230, 230);
            btn.Cursor = Cursors.Hand;
            btn.TextAlign = ContentAlignment.MiddleLeft;

            // 设置按钮图标
            btn.Image = CreateButtonIcon(icon, color);
            btn.ImageAlign = ContentAlignment.MiddleLeft;
            btn.TextImageRelation = TextImageRelation.ImageBeforeText;

            btn.MouseEnter += (s, e) => {
                btn.BackColor = Color.FromArgb(248, 248, 248);
                btn.FlatAppearance.BorderColor = color;
            };

            btn.MouseLeave += (s, e) => {
                btn.BackColor = Color.White;
                btn.FlatAppearance.BorderColor = Color.FromArgb(230, 230, 230);
            };

            btn.Click += (s, e) => {
                NavigateToModulePage(text);
            };

            return btn;
        }

        /// <summary>
        /// 导航到对应的模块页面
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        private void NavigateToModulePage(string moduleName)
        {
            try
            {
                // 获取主窗体实例
                var mainForm = this.FindForm() as FluentDesignForm1;
                if (mainForm == null)
                {
                    MessageBox.Show("无法找到主窗体", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 根据模块名称映射到对应的页面URL
                string pageUrl = GetPageUrlByModuleName(moduleName);
                if (string.IsNullOrEmpty(pageUrl))
                {
                    MessageBox.Show($"未找到 {moduleName} 对应的页面", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 使用反射调用主窗体的NavigateToPage方法
                var navigateMethod = mainForm.GetType().GetMethod("NavigateToPage",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (navigateMethod != null)
                {
                    navigateMethod.Invoke(mainForm, new object[] { moduleName, pageUrl });
                }
                else
                {
                    MessageBox.Show("无法调用页面导航方法", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"页面跳转失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据模块名称获取对应的页面URL
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>页面URL</returns>
        private string GetPageUrlByModuleName(string moduleName)
        {
            switch (moduleName)
            {
                case "器械回收":
                    return "RecyclingCleaning.RecyclingManagement"; // 回收管理页面
                case "清洗管理":
                    return "RecyclingCleaning.CleaningManagement"; // 回收清洗的清洗管理
                case "消毒管理":
                    return "RecyclingCleaning.DisinfectionManagement"; // 回收清洗的消毒管理
                case "灭菌管理":
                    return "PackagingSterilization.PackagingManagement"; // 包装灭菌的包装管理
                case "发放管理":
                    return "InventoryManagement.DistributeUse"; // 库存管理的发放管理
                default:
                    return null;
            }
        }

        private System.Drawing.Image CreateButtonIcon(string icon, Color color)
        {
            Bitmap bitmap = new Bitmap(36, 36);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

                // 绘制背景
                using (Brush brush = new SolidBrush(color))
                {
                    g.FillRoundedRectangle(brush, 0, 0, 36, 36, 6);
                }

                // 绘制图标
                using (Brush textBrush = new SolidBrush(Color.White))
                {
                    Font iconFont = new Font("Segoe UI Emoji", 16);
                    SizeF textSize = g.MeasureString(icon, iconFont);
                    float x = (36 - textSize.Width) / 2;
                    float y = (36 - textSize.Height) / 2;
                    g.DrawString(icon, iconFont, textBrush, x, y);
                }
            }
            return bitmap;
        }

        private void CreateBottomPanel()
        {
            bottomPanel = new Panel();
            bottomPanel.Height = 180;
            bottomPanel.Dock = DockStyle.Bottom;
            bottomPanel.BackColor = Color.Transparent;
            bottomPanel.Padding = new Padding(0, 15, 0, 0);

            // 左侧系统消息面板
            Panel messagePanel = new Panel();
            messagePanel.Width = this.Width / 2 - 30;
            messagePanel.Dock = DockStyle.Left;
            messagePanel.BackColor = Color.White;
            messagePanel.Margin = new Padding(0, 0, 20, 0);
            messagePanel.BorderStyle = BorderStyle.FixedSingle;

            // 系统消息标题栏
            Panel messageTitleBar = new Panel();
            messageTitleBar.Height = 40;
            messageTitleBar.Dock = DockStyle.Top;
            messageTitleBar.BackColor = Color.FromArgb(52, 144, 220);
            messageTitleBar.BorderStyle = BorderStyle.None;

            Label messageTitle = new Label();
            messageTitle.Text = "系统消息";
            messageTitle.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            messageTitle.ForeColor = Color.White;
            messageTitle.Location = new Point(20, 10);
            messageTitle.AutoSize = false;
            messageTitle.Size = new Size(200, 20);

            messageTitleBar.Controls.Add(messageTitle);

            Label messageContent = new Label();
            messageContent.Text = "我院将于5月20日举办职工技能大赛!";
            messageContent.Font = new Font("微软雅黑", 11);
            messageContent.ForeColor = Color.FromArgb(51, 51, 51);
            messageContent.Location = new Point(20, 60);
            messageContent.AutoSize = false;
            messageContent.Size = new Size(messagePanel.Width - 40, 30);

            messagePanel.Controls.AddRange(new Control[] { messageTitleBar, messageContent });

            // 右侧效期预警面板
            Panel alertPanel = new Panel();
            alertPanel.Dock = DockStyle.Fill;
            alertPanel.BackColor = Color.White;
            alertPanel.BorderStyle = BorderStyle.FixedSingle;

            // 效期预警标题栏
            Panel alertTitleBar = new Panel();
            alertTitleBar.Height = 40;
            alertTitleBar.Dock = DockStyle.Top;
            alertTitleBar.BackColor = Color.FromArgb(52, 144, 220);
            alertTitleBar.BorderStyle = BorderStyle.None;

            Label alertTitle = new Label();
            alertTitle.Text = "效期预警";
            alertTitle.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            alertTitle.ForeColor = Color.White;
            alertTitle.Location = new Point(20, 10);
            alertTitle.AutoSize = false;
            alertTitle.Size = new Size(200, 20);

            alertTitleBar.Controls.Add(alertTitle);

            // 预警信息
            Label alertInfo = new Label();
            alertInfo.Text = "共有8种物品效期预警!";
            alertInfo.Font = new Font("微软雅黑", 11);
            alertInfo.ForeColor = Color.FromArgb(51, 51, 51);
            alertInfo.Location = new Point(20, 60);
            alertInfo.AutoSize = false;
            alertInfo.Size = new Size(200, 20);

            // 预警表格标题
            var headers = new[] { "包编码", "包名称", "包类型", "消毒日期", "有效天数", "到期天数" };
            int headerY = 90;
            int headerX = 20;
            int columnWidth = (alertPanel.Width - 40) / headers.Length;

            for (int i = 0; i < headers.Length; i++)
            {
                Label header = new Label();
                header.Text = headers[i];
                header.Font = new Font("微软雅黑", 9, FontStyle.Bold);
                header.Location = new Point(headerX + i * columnWidth, headerY);
                header.Size = new Size(columnWidth - 2, 25);
                header.BackColor = Color.FromArgb(245, 245, 245);
                header.BorderStyle = BorderStyle.FixedSingle;
                header.TextAlign = ContentAlignment.MiddleCenter;
                header.ForeColor = Color.FromArgb(51, 51, 51);
                alertPanel.Controls.Add(header);
            }

            alertPanel.Controls.AddRange(new Control[] { alertTitleBar, alertInfo });
            bottomPanel.Controls.AddRange(new Control[] { messagePanel, alertPanel });
            this.Controls.Add(bottomPanel);
        }
    }

    // 扩展方法：绘制圆角矩形
    public static class GraphicsExtensions
    {
        public static void FillRoundedRectangle(this Graphics g, Brush brush, float x, float y, float width, float height, float radius)
        {
            using (var path = new System.Drawing.Drawing2D.GraphicsPath())
            {
                path.AddArc(x, y, radius * 2, radius * 2, 180, 90);
                path.AddArc(width - radius * 2, y, radius * 2, radius * 2, 270, 90);
                path.AddArc(width - radius * 2, height - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(x, height - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseFigure();

                g.FillPath(brush, path);
            }
        }
    }
}
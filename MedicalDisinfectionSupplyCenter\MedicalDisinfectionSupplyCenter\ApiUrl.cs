﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MedicalDisinfectionSupplyCenter
{
    internal class ApiUrl
    {
        /// <summary>
        /// API读取地址
        /// </summary>
        public static string ApiReadUrl => "http://localhost:5172";

        /// <summary>
        /// API写入地址
        /// </summary>
        public static string ApiWriteUrl => "http://localhost:5192";
    }
    public static class ReadApiConfig
    {
        //public const string BaseUrl = "http://***********:4050/api/";
        public const string BaseUrl = "http://localhost:5172/api/";
        /// <summary>
        /// 获取完整的API地址，格式为 BaseUrl + controller/action
        /// </summary>
        /// <param name="controller">控制器名</param>
        /// <param name="action">方法名</param>
        /// <returns>完整API地址</returns>
        public static string GetApiUrl(string controller, string action)
        {
            if (string.IsNullOrWhiteSpace(controller) || string.IsNullOrWhiteSpace(action))
                throw new ArgumentException("controller和action不能为空");
            return $"{BaseUrl}{controller}/{action}";
        }
    }
    /// <summary>
    /// 仓储管理的写
    /// </summary>
    public static class WriteApiConfig
    {
        //public const string BaseUrl = "http://***********:4060/api/";
        public const string BaseUrl = "http://localhost:5192/api/";
        /// <summary>
        /// 获取完整的API地址，格式为 BaseUrl + controller/action
        /// </summary>
        /// <param name="controller">控制器名</param>
        /// <param name="action">方法名</param>
        /// <returns>完整API地址</returns>
        public static string GetApiUrl(string controller, string action)
        {
            if (string.IsNullOrWhiteSpace(controller) || string.IsNullOrWhiteSpace(action))
                throw new ArgumentException("controller和action不能为空");
            return $"{BaseUrl}{controller}/{action}";
        }
    }
    /// <summary>
    /// 科室的写
    /// </summary>
    public static class WriteDept
    {
        //public const string BaseUrl = "http://***********:4060/api/";
        public const string BaseUrl = "http://localhost:5192/api/";
        /// <summary>
        /// 获取完整的API地址，格式为 BaseUrl + controller/action
        /// </summary>
        /// <param name="controller">控制器名</param>
        /// <param name="action">方法名</param>
        /// <returns>完整API地址</returns>
        public static string GetApiUrl(string controller, string action)
        {
            if (string.IsNullOrWhiteSpace(controller) || string.IsNullOrWhiteSpace(action))
                throw new ArgumentException("controller和action不能为空");
            return $"{BaseUrl}{controller}/{action}";
        }
    }
    /// <summary>
    /// 科室的读取API配置
    /// 负责医院科室相关数据的读取操作
    /// 包括：科室领用申请、科室信息查询等
    /// </summary>
    public static class ReadDept
    {
        //public const string BaseUrl = "http://***********:4050/api/";
        /// <summary>
        /// API基础URL地址
        /// </summary>
        public const string BaseUrl = "http://localhost:5172/api/";

        /// <summary>
        /// 获取完整的科室读取API地址
        /// 格式为 BaseUrl + controller/action
        /// 常用控制器：
        /// - DeptApply: 科室领用申请管理
        /// - DeptInfo: 科室基础信息
        /// </summary>
        /// <param name="controller">控制器名，如DeptApply</param>
        /// <param name="action">方法名，如GetApplicationByPage</param>
        /// <returns>完整API地址</returns>
        public static string GetApiUrl(string controller, string action)
        {
            if (string.IsNullOrWhiteSpace(controller) || string.IsNullOrWhiteSpace(action))
                throw new ArgumentException("controller和action不能为空");
            return $"{BaseUrl}{controller}/{action}";
        }

    }
}

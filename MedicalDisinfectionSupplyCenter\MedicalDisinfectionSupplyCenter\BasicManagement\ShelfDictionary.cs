using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks; // Added for Task
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.BasicManagement
{
    /// <summary>
    /// 货架字典管理界面
    /// </summary>
    public partial class ShelfDictionary : UserControl
    {
        #region API常量与HTTP客户端
        private const string API_BASE_URL = "http://localhost:5172/api/BasicManagement";
        //private const string API_BASE_URL = "http://***********:4050/api/BasicManagement";
        private const string API_WRITE_BASE_URL = "http://localhost:5192/api/BasicManagement";
        //private const string API_WRITE_BASE_URL = "http://***********:4060/api/BasicManagement";
        private static readonly HttpClient httpClient = new HttpClient();
        #endregion

        #region 控件声明
        private LayoutControl layoutControl;
        private LayoutControlGroup rootGroup;
        private PanelControl panelToolbar;
        private LookUpEdit lueParentShelf;
        private CheckEdit chkShowDisabled;
        private SimpleButton btnQuery;
        private SimpleButton btnAdd;
        private SimpleButton btnPrintBarcode;
        private GridControl gridShelf;
        private GridView gridViewShelf;
        private LabelControl lblTotal;
        #endregion

        /// <summary>
        /// 初始化货架字典界面
        /// </summary>
        public ShelfDictionary()
        {
            InitializeComponent();
            InitializeControls();
            SetupLayout();
            InitializeGrid();

            httpClient.Timeout = TimeSpan.FromSeconds(30);
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            this.Load += ShelfDictionary_Load;
        }

        /// <summary>
        /// 窗体加载事件，在此处调用初始数据加载
        /// </summary>
        private async void ShelfDictionary_Load(object sender, EventArgs e)
        {
            await LoadParentShelvesForFilter();
            LoadShelfData();
        }

        /// <summary>
        /// 加载用于筛选的上级货架列表
        /// </summary>
        private async Task LoadParentShelvesForFilter()
        {
            try
            {
                // 使用一个较大的页面大小来获取所有货架作为潜在的上级货架
                var url = $"{API_BASE_URL}/QueryShelves?PageSize=1000";
                var response = await httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var pageResult = JsonConvert.DeserializeObject<ApiPageResult<ShelfDto>>(json);
                    if (pageResult?.Success == true && pageResult.Data?.Items != null)
                    {
                        var shelves = pageResult.Data.Items;
                        // 使用匿名对象列表作为数据源，包含一个“所有”选项
                        var dataSource = new List<object> {
                            new { Id = 0, Name = "所有" },
                            new { Id = 1, Name = "清洗设备" },
                            new { Id = 2, Name = "消毒设备" },
                            new { Id = 3, Name = "灭菌设备" }
                        };

                        lueParentShelf.Properties.DataSource = dataSource;
                    }
                }
            }
            catch (Exception ex)
            {
                // 弹出错误提示，但程序仍可继续运行
                MessageBox.Show($"加载上级货架列表失败: {ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }


        /// <summary>
        /// 初始化所有UI控件
        /// </summary>
        private void InitializeControls()
        {
            layoutControl = new LayoutControl { Dock = DockStyle.Fill, Name = "layoutControl" };
            panelToolbar = new PanelControl { Name = "panelToolbar", Height = 80, Dock = DockStyle.Top, Padding = new Padding(10) };

            // 设备类型标签 (根据图片布局)
            var lblParentShelf = new LabelControl
            {
                Location = new Point(30, 28),
                Text = "设备类型："
            };

            // 上级货架查询下拉框
            lueParentShelf = new LookUpEdit
            {
                Name = "lueParentShelf",
                Location = new Point(120, 25),
                Width = 160
            };
            lueParentShelf.Properties.DisplayMember = "Name";
            lueParentShelf.Properties.ValueMember = "Id";
            lueParentShelf.Properties.NullText = "所有";

            chkShowDisabled = new CheckEdit
            {
                Name = "chkShowDisabled",
                Text = "显示停用",
                Checked = true,
                Location = new Point(300, 28)
            };

            btnQuery = new SimpleButton
            {
                Name = "btnQuery",
                Text = "查询",
                Location = new Point(450, 25),
                Width = 90
            };
            btnQuery.Click += (s, e) => LoadShelfData();

            btnAdd = new SimpleButton
            {
                Name = "btnAdd",
                Text = "新增",
                Location = new Point(560, 25),
                Width = 90
            };
            btnAdd.Click += btnAdd_Click;

            btnPrintBarcode = new SimpleButton
            {
                Name = "btnPrintBarcode",
                Text = "打印条码",
                Location = new Point(670, 25),
                Width = 100
            };
            btnPrintBarcode.Click += (s, e) => MessageBox.Show("打印条码功能待实现。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);


            panelToolbar.Controls.AddRange(new Control[] { lblParentShelf, lueParentShelf, chkShowDisabled, btnQuery, btnAdd, btnPrintBarcode });

            gridShelf = new GridControl { Name = "gridShelf", Dock = DockStyle.Fill };
            gridViewShelf = new GridView(gridShelf) { Name = "gridViewShelf" };
            gridShelf.MainView = gridViewShelf;

            lblTotal = new LabelControl { Name = "lblTotal", Text = "合计：0条", Dock = DockStyle.Bottom, Padding = new Padding(5) };

            this.Controls.Add(layoutControl);
            this.Controls.Add(lblTotal);
        }

        /// <summary>
        /// 设置主布局
        /// </summary>
        private void SetupLayout()
        {
            layoutControl.BeginUpdate();
            rootGroup = new LayoutControlGroup { TextVisible = false };

            var layoutToolbar = new LayoutControlItem(layoutControl, panelToolbar)
            {
                TextVisible = false,
                SizeConstraintsType = SizeConstraintsType.Custom,
                MaxSize = new Size(0, 80),
                MinSize = new Size(0, 80)
            };

            var layoutGrid = new LayoutControlItem(layoutControl, gridShelf) { TextVisible = false };

            rootGroup.Add(layoutToolbar);
            rootGroup.Add(layoutGrid);
            layoutControl.Root = rootGroup;
            layoutControl.EndUpdate();
        }

        /// <summary>
        /// 初始化表格列定义
        /// </summary>
        private void InitializeGrid()
        {
            gridViewShelf.OptionsView.ShowGroupPanel = false;
            gridViewShelf.OptionsBehavior.Editable = true;

            var columns = new DevExpress.XtraGrid.Columns.GridColumn[] {
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "货架编码", FieldName = "ShelfCode", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "货架名称", FieldName = "ShelfName", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "上级货架", FieldName = "ParentShelfName", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "货架条码", FieldName = "Barcode", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "货架层数", FieldName = "Layers", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "货架格数", FieldName = "Grids", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "创建时间", FieldName = "CreateTime", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "状态", FieldName = "Status", Visible = true },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "操作", FieldName = "Operation", Visible = true, Width = 160 }
            };
            gridViewShelf.Columns.AddRange(columns);

            // 设置状态列为ToggleSwitch
            var toggleSwitchRepo = new RepositoryItemToggleSwitch();
            toggleSwitchRepo.Toggled += OnStatusToggled;
            toggleSwitchRepo.OnText = "正常";
            toggleSwitchRepo.OffText = "停用";
            toggleSwitchRepo.ValueOn = 1;
            toggleSwitchRepo.ValueOff = 0;
            gridViewShelf.Columns["Status"].ColumnEdit = toggleSwitchRepo;

            // 设置操作列为按钮
            var buttonRepo = new RepositoryItemButtonEdit();
            buttonRepo.TextEditStyle = TextEditStyles.HideTextEditor;
            buttonRepo.Buttons.Add(new EditorButton(ButtonPredefines.Glyph) { Caption = "编辑" });
            buttonRepo.Buttons.Add(new EditorButton(ButtonPredefines.Glyph) { Caption = "删除" });
            buttonRepo.ButtonClick += OperationButton_Click;
            gridViewShelf.Columns["Operation"].ColumnEdit = buttonRepo;

            // 设置只读和可编辑列
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridViewShelf.Columns)
            {
                col.OptionsColumn.AllowEdit = false;
            }
            gridViewShelf.Columns["Status"].OptionsColumn.AllowEdit = true;
            gridViewShelf.Columns["Operation"].OptionsColumn.AllowEdit = true;

            gridViewShelf.Columns["CreateTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridViewShelf.Columns["CreateTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            gridViewShelf.OptionsView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
        }

        /// <summary>
        /// 获取上级货架数据源
        /// </summary>
        private List<object> GetParentShelfDataSource()
        {
            return new List<object>
            {
                new { Id = 1, Name = "清洗设备" },
                new { Id = 2, Name = "消毒设备" },
                new { Id = 3, Name = "灭菌设备" }
            };
        }

        /// <summary>
        /// 从API异步加载货架数据
        /// </summary>
        private async void LoadShelfData()
        {
            btnQuery.Enabled = false;
            btnQuery.Text = "加载中...";
            string url = string.Empty;
            try
            {
                url = $"{API_BASE_URL}/QueryShelves";
                var parameters = new List<string>();

                // 添加上级货架筛选
                var parentId = lueParentShelf.EditValue;
                if (parentId != null && parentId != DBNull.Value && (int)parentId != 0)
                {
                    parameters.Add($"ParentShelfId={parentId}");
                }

                if (!chkShowDisabled.Checked)
                {
                    parameters.Add("status=1");
                }

                if (parameters.Any())
                {
                    url += "?" + string.Join("&", parameters);
                }

                System.Diagnostics.Debug.WriteLine($"请求API: {url}");
                var response = await httpClient.GetAsync(url);
                var json = await response.Content.ReadAsStringAsync();
                System.Diagnostics.Debug.WriteLine($"API响应: {json}");

                if (!response.IsSuccessStatusCode)
                {
                    MessageBox.Show($"API请求失败 (状态码: {response.StatusCode})\nURL: {url}\n响应: {json}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var pageResult = JsonConvert.DeserializeObject<ApiPageResult<ShelfDto>>(json);

                if (pageResult?.Success == true && pageResult.Data?.Items != null)
                {
                    gridShelf.DataSource = pageResult.Data.Items;
                    lblTotal.Text = $"合计：{pageResult.Data.TotalCount} 条";
                    if (pageResult.Data.Items.Count == 0)
                    {
                        MessageBox.Show("未查询到任何货架数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show(pageResult?.Msg ?? "获取数据失败，但API响应成功。请检查API返回的数据结构。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    gridShelf.DataSource = null;
                    lblTotal.Text = "合计：0 条";
                }
            }
            catch (JsonException jsonEx)
            {
                MessageBox.Show($"解析API响应失败: {jsonEx.Message}\n请检查后端返回的数据格式是否与前端ShelfDto匹配。\n请求URL: {url}", "数据格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (HttpRequestException httpEx)
            {
                MessageBox.Show($"网络请求异常: {httpEx.Message}\n请检查API服务是否正在运行，以及网络连接是否正常。\n请求URL: {url}", "网络错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据时发生未知异常: {ex.Message}\n请求URL: {url}", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                gridShelf.DataSource = null;
                lblTotal.Text = "合计：0 条";
            }
            finally
            {
                btnQuery.Enabled = true;
                btnQuery.Text = "查询";
                gridViewShelf.BestFitColumns();
            }
        }

        /// <summary>
        /// “操作”列按钮（编辑/删除）点击事件
        /// </summary>
        private void OperationButton_Click(object sender, ButtonPressedEventArgs e)
        {
            var rowHandle = gridViewShelf.FocusedRowHandle;
            if (rowHandle < 0) return;
            var shelf = gridViewShelf.GetRow(rowHandle) as ShelfDto;
            if (shelf == null) return;

            if (e.Button.Caption == "编辑")
            {
                OpenEditForm(shelf);
            }
            else if (e.Button.Caption == "删除")
            {
                DeleteShelf(shelf);
            }
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            OpenEditForm(null); // 传入null表示是新增模式
        }

        /// <summary>
        /// 打开新增或编辑对话框
        /// </summary>
        /// <param name="shelf">要编辑的货架对象，如果为null则为新增模式</param>
        private void OpenEditForm(ShelfDto shelf)
        {
            bool isNew = shelf == null;
            using (var form = new XtraForm())
            {
                form.Text = isNew ? "新增货架" : "编辑货架";
                form.FormBorderStyle = FormBorderStyle.FixedDialog;
                form.StartPosition = FormStartPosition.CenterParent;
                form.Width = 450;
                form.Height = 350;
                form.MaximizeBox = false;
                form.MinimizeBox = false;

                var lc = new LayoutControl { Dock = DockStyle.Fill };
                form.Controls.Add(lc);

                var txtCode = new TextEdit { Text = shelf?.ShelfCode };
                var txtName = new TextEdit { Text = shelf?.ShelfName };
                // TODO: 上级货架应为下拉框，数据源为所有货架
                var cmbParent = new LookUpEdit { Text = shelf?.ParentShelfName, Properties = { ValueMember = "Id", DisplayMember = "Name", DataSource = GetParentShelfDataSource() } };
                var txtBarcode = new TextEdit { Text = shelf?.Barcode, Properties = { ReadOnly = true, AppearanceReadOnly = { BackColor = Color.LightGray } }, LookAndFeel = { UseDefaultLookAndFeel = false } };
                var numLayers = new SpinEdit { EditValue = shelf?.Layers ?? 1, Properties = { MinValue = 1 } };
                var numCells = new SpinEdit { EditValue = shelf?.Grids ?? 1, Properties = { MinValue = 1 } };

                var btnSave = new SimpleButton { Text = "保存" };
                var btnCancel = new SimpleButton { Text = "取消" };

                lc.Root.Add(new LayoutControlItem(lc, txtCode) { Text = "货架编码" });
                lc.Root.Add(new LayoutControlItem(lc, txtName) { Text = "货架名称" });
                lc.Root.Add(new LayoutControlItem(lc, cmbParent) { Text = "上级货架" });
                lc.Root.Add(new LayoutControlItem(lc, txtBarcode) { Text = "货架条码" });
                lc.Root.Add(new LayoutControlItem(lc, numLayers) { Text = "货架层数" });
                lc.Root.Add(new LayoutControlItem(lc, numCells) { Text = "货架格数" });
                lc.Root.Add(new EmptySpaceItem());
                lc.Root.Add(new LayoutControlItem(lc, btnSave));
                lc.Root.Add(new LayoutControlItem(lc, btnCancel));
                lc.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new Rectangle(700, 300, 250, 350);

                btnCancel.Click += (s, e) => form.Close();
                btnSave.Click += async (s, e) =>
                {
                    if (string.IsNullOrWhiteSpace(txtCode.Text) || string.IsNullOrWhiteSpace(txtName.Text) || cmbParent.EditValue == null)
                    {
                        MessageBox.Show("货架编码、货架名称和上级货架为必填项。", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var requestDto = new
                    {
                        Id = shelf?.Id ?? 0,
                        ShelfCode = txtCode.Text.Trim(),
                        ShelfName = txtName.Text.Trim(),
                        ParentShelfId = cmbParent.EditValue as int?, // 从下拉框获取选中的上级货架ID
                        Barcode = txtBarcode.Text.Trim(),
                        Layers = (int)numLayers.Value,
                        Grids = (int)numCells.Value,
                        Status = shelf?.Status ?? 1, // 新增默认为1
                    };

                    try
                    {
                        var url = isNew ? $"{API_WRITE_BASE_URL}/AddShelves" : $"{API_WRITE_BASE_URL}/UpdateShelves";
                        var method = isNew ? HttpMethod.Post : HttpMethod.Put;

                        var jsonContent = JsonConvert.SerializeObject(requestDto);
                        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                        var request = new HttpRequestMessage(method, url) { Content = content };
                        var response = await httpClient.SendAsync(request);

                        if (response.IsSuccessStatusCode)
                        {
                            MessageBox.Show("保存成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            form.DialogResult = DialogResult.OK;
                        }
                        else
                        {
                            var error = await response.Content.ReadAsStringAsync();
                            MessageBox.Show($"保存失败: {error}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"请求异常: {ex.Message}", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadShelfData();
                }
            }
        }

        /// <summary>
        /// 删除指定的货架
        /// </summary>
        private async void DeleteShelf(ShelfDto shelf)
        {
            var result = MessageBox.Show($"您确定要停用货架 '{shelf.ShelfName}' 吗？", "确认停用", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (result == DialogResult.Yes)
            {
                try
                {
                    var url = $"{API_WRITE_BASE_URL}/DeleteShelves/{shelf.Id}";
                    var response = await httpClient.DeleteAsync(url);

                    if (response.IsSuccessStatusCode)
                    {
                        MessageBox.Show("货架已停用", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadShelfData();
                    }
                    else
                    {
                        var error = await response.Content.ReadAsStringAsync();

                        // 解析API错误响应
                        try
                        {
                            // 临时使用匿名类型解析错误响应
                            var apiError = JsonConvert.DeserializeObject<dynamic>(error);
                            MessageBox.Show($"停用失败: {apiError?.message ?? apiError?.Message ?? error}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                        catch
                        {
                            MessageBox.Show($"停用失败: {error}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    MessageBox.Show($"网络请求异常: {httpEx.Message}", "网络错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch (JsonException jsonEx)
                {
                    MessageBox.Show($"数据解析异常: {jsonEx.Message}", "数据错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"系统异常: {ex.Message}", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 状态切换开关事件处理
        /// </summary>
        private async void OnStatusToggled(object sender, EventArgs e)
        {
            gridViewShelf.PostEditor();
            var toggle = (ToggleSwitch)sender;
            var rowHandle = gridViewShelf.FocusedRowHandle;
            if (rowHandle < 0) return;
            var shelf = gridViewShelf.GetRow(rowHandle) as ShelfDto;
            if (shelf == null) return;

            int newStatus = toggle.IsOn ? 1 : 0;
            toggle.Enabled = false;

            try
            {
                var url = $"{API_WRITE_BASE_URL}/UpdateShelvesStatus/UpdateShelvesStatus";
                var updateRequest = new { Id = shelf.Id, Status = newStatus };
                var jsonContent = JsonConvert.SerializeObject(updateRequest);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 使用PUT方法
                var response = await httpClient.PutAsync(url, content);

                if (!response.IsSuccessStatusCode)
                {
                    var error = await response.Content.ReadAsStringAsync();

                    // 解析API错误响应
                    try
                    {
                        // 临时使用匿名类型解析错误响应
                        var apiError = JsonConvert.DeserializeObject<dynamic>(error);
                        MessageBox.Show($"状态更新失败: {apiError?.message ?? apiError?.Message ?? error}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    catch
                    {
                        MessageBox.Show($"状态更新失败: {error}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    toggle.IsOn = !toggle.IsOn; // 恢复原状
                }
                else
                {
                    shelf.Status = newStatus; // 本地更新状态
                    MessageBox.Show("状态更新成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"请求异常: {ex.Message}", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                toggle.IsOn = !toggle.IsOn; // 恢复原状
            }
            finally
            {
                toggle.Enabled = true;
            }
        }
    }

    #region DTO和API响应模型
    /// <summary>
    /// 货架数据传输对象
    /// </summary>
    public class ShelfDto
    {
        public int Id { get; set; }
        public string ShelfCode { get; set; }
        public string ShelfName { get; set; }
        public int? ParentShelfId { get; set; }
        public string ParentShelfName { get; set; }
        public string Barcode { get; set; }
        public int Layers { get; set; }
        public int Grids { get; set; }
        public int Status { get; set; }
        public DateTime CreateTime { get; set; }
    }


    #endregion
}

﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.DepartmentManagement
{
    public partial class SignManagement : UserControl
    {
        public SignManagement()
        {
            InitializeComponent();
            // 注册控件加载事件
            this.Load += SignManagement_Load;
            // 注册按钮点击事件
            this.signBtnSearch.Click += BtnSearch_Click;
            // 注册下拉列表选择事件
            this.signComboBoxPageSize.SelectedIndexChanged += ComboBoxPageSize_SelectedIndexChanged;
        }

        // 添加一个辅助方法，支持多种字段名格式的获取
        private string GetJsonValue(JToken item, string propertyName)
        {
            // 1. 尝试直接获取原始字段名
            if (item[propertyName] != null)
            {
                return item[propertyName].ToString();
            }
            
            // 2. 尝试首字母小写
            string lcFirstProperty = char.ToLower(propertyName[0]) + propertyName.Substring(1);
            if (item[lcFirstProperty] != null)
            {
                return item[lcFirstProperty].ToString();
            }
            
            // 3. 尝试全部小写
            string lowerProperty = propertyName.ToLower();
            if (item[lowerProperty] != null)
            {
                return item[lowerProperty].ToString();
            }
            
            // 4. 尝试全部大写
            string upperProperty = propertyName.ToUpper();
            if (item[upperProperty] != null)
            {
                return item[upperProperty].ToString();
            }
            
            // 检查JSON中所有属性名，查找匹配的（忽略大小写）
            if (item is JObject jObj)
            {
                foreach (var prop in jObj.Properties())
                {
                    if (prop.Name.Equals(propertyName, StringComparison.OrdinalIgnoreCase))
                    {
                        return prop.Value.ToString();
                    }
                }
                
                // 打印可用的属性名
                System.Diagnostics.Debug.WriteLine($"在JSON中搜索'{propertyName}'失败。可用属性: " + 
                    string.Join(", ", jObj.Properties().Select(p => p.Name)));
            }
            
            return ""; // 找不到匹配的值，返回空字符串
        }

        /// <summary>
        /// 页面尺寸变化事件处理
        /// </summary>
        private void ComboBoxPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(signComboBoxPageSize.SelectedItem?.ToString(), out int size))
            {
                BindGrid(); // 重新加载数据
            }
        }

        /// <summary>
        /// 控件加载事件处理
        /// </summary>
        private void SignManagement_Load(object sender, EventArgs e)
        {
            InitGridView(); // 初始化表格视图
            // 清除日期过滤器的初始值
            this.signDateEditStart.EditValue = null;
            this.signDateEditEnd.EditValue = null;
            // 初始化当前月份的时间范围
            SetCurrentMonthDateRange();
            BindGrid(); // 绑定数据到表格
        }

        /// <summary>
        /// 设置当前月份的时间范围
        /// </summary>
        private void SetCurrentMonthDateRange()
        {
            DateTime now = DateTime.Now;
            DateTime firstDay = new DateTime(now.Year, now.Month, 1);
            DateTime lastDay = firstDay.AddMonths(1).AddDays(-1);
            
            this.signDateEditStart.EditValue = firstDay;
            this.signDateEditEnd.EditValue = lastDay;
        }

        /// <summary>
        /// 初始化表格视图设置
        /// </summary>
        private void InitGridView()
        {
            // 设置表头字体和背景色
            this.signGridView.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(221, 235, 247); // 淡蓝色
            this.signGridView.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.signGridView.Appearance.HeaderPanel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.signGridView.Appearance.HeaderPanel.Options.UseFont = true;
            this.signGridView.OptionsView.ShowGroupPanel = false;

            // 清空已有列
            this.signGridView.Columns.Clear();

            // 添加列（根据图片中的表格列）
            this.signGridView.Columns.AddVisible("id", "发放表ID");
            this.signGridView.Columns.AddVisible("IssueCode", "发放单号");
            this.signGridView.Columns.AddVisible("IssueName", "发放人");
            this.signGridView.Columns.AddVisible("IssueDate", "发放时间");
            this.signGridView.Columns.AddVisible("acceptName", "接收人");
            this.signGridView.Columns.AddVisible("IssueStateName", "发放状态名称");

            // 设置日期格式
            var colIssueDate = this.signGridView.Columns["IssueDate"];
            if (colIssueDate != null)
            {
                colIssueDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colIssueDate.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            }
            
            // 设置状态列样式（蓝色文本）
            var colStatus = this.signGridView.Columns["IssueStateName"];
            if (colStatus != null)
            {
                colStatus.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(0, 112, 192); // 蓝色
                colStatus.AppearanceCell.Options.UseForeColor = true;
            }

            // 添加操作列
            AddOperationColumn();

            // 设置表格自动调整列宽
            signGridView.BestFitColumns();
        }

        /// <summary>
        /// 添加操作列
        /// </summary>
        private void AddOperationColumn()
        {
            // 创建一个基础的按钮编辑器
            RepositoryItemButtonEdit buttonEdit = new RepositoryItemButtonEdit();
            buttonEdit.TextEditStyle = TextEditStyles.HideTextEditor;
            
            // 先添加一个默认按钮作为占位符，后续在绘制时会替换
            EditorButton defaultButton = new EditorButton(ButtonPredefines.OK);
            defaultButton.Caption = "操作";
            buttonEdit.Buttons.Add(defaultButton);
            
            // 添加操作列并绑定按钮编辑器
            GridColumn operationColumn = this.signGridView.Columns.AddVisible("operation", "操作");
            operationColumn.ColumnEdit = buttonEdit;
            operationColumn.Width = 120;
            operationColumn.OptionsColumn.AllowEdit = true;
            operationColumn.OptionsColumn.FixedWidth = true;
            operationColumn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            operationColumn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            
            // 注册自定义事件以动态显示按钮
            this.signGridView.RowCellClick += SignGridView_RowCellClick;
            this.signGridView.CustomDrawCell += SignGridView_CustomDrawCell;
            
            // 注册按钮事件
            this.signGridControl.RepositoryItems.Add(buttonEdit);
        }

                /// <summary>
        /// 自定义单元格绘制，根据状态显示不同按钮
        /// </summary>
        private void SignGridView_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName != "operation")
                return;

            try
            {
                // 获取当前行的状态
                string issueStateName = Convert.ToString(this.signGridView.GetRowCellValue(e.RowHandle, "IssueStateName"));
                
                // 获取按钮编辑器
                RepositoryItemButtonEdit buttonEdit = e.Column.ColumnEdit as RepositoryItemButtonEdit;
                if (buttonEdit != null && buttonEdit.Buttons.Count > 0)
                {
                    // 清除已有按钮
                    buttonEdit.Buttons.Clear();
                    
                    if (issueStateName == "已签收")
                    {
                        // 添加撤回按钮
                        EditorButton withdrawButton = new EditorButton(ButtonPredefines.Undo);
                        withdrawButton.Caption = "撤回";
                        withdrawButton.ToolTip = "撤回签收";
                        withdrawButton.Kind = ButtonPredefines.Undo;
                        withdrawButton.Appearance.ForeColor = Color.Blue;
                        withdrawButton.Tag = "Withdraw";
                        buttonEdit.Buttons.Add(withdrawButton);
                    }
                    else
                    {
                        // 添加签收按钮
                        EditorButton signButton = new EditorButton(ButtonPredefines.OK);
                        signButton.Caption = "签收";
                        signButton.ToolTip = "签收";
                        signButton.Kind = ButtonPredefines.OK;
                        signButton.Appearance.ForeColor = Color.Green;
                        signButton.Tag = "Sign";
                        buttonEdit.Buttons.Add(signButton);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自定义绘制单元格异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 行单元格点击事件
        /// </summary>
        private void SignGridView_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {
            try
            {
                if (e.Column.FieldName != "operation")
                    return;
                    
                // 获取当前行数据
                int id = Convert.ToInt32(this.signGridView.GetRowCellValue(e.RowHandle, "id"));
                string issueCode = this.signGridView.GetRowCellValue(e.RowHandle, "IssueCode")?.ToString();
                string issueStateName = this.signGridView.GetRowCellValue(e.RowHandle, "IssueStateName")?.ToString();
                
                if (id <= 0)
                {
                    MessageBox.Show("无法获取记录ID！");
                    return;
                }

                // 显示操作确认对话框
                string message = issueStateName == "已签收" ? 
                    $"确定要撤回发放单号为 {issueCode} 的签收吗？" : 
                    $"确定要签收发放单号为 {issueCode} 的记录吗？";
                
                string title = issueStateName == "已签收" ? "确认撤回" : "确认签收";
                
                DialogResult result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    // 根据状态执行不同操作
                    if (issueStateName == "已签收")
                    {
                        // 执行撤回功能
                        PerformWithdrawAction(id, issueCode);
                    }
                    else
                    {
                        // 执行签收功能
                        PerformSignAction(id, issueCode);
                    }
                    
                    // 操作完成后立即更新表格中的状态
                    string newStateName = issueStateName == "已签收" ? "已发放" : "已签收";
                    signGridView.SetRowCellValue(e.RowHandle, "IssueStateName", newStateName);
                    
                    // 刷新当前行的显示
                    signGridView.RefreshRow(e.RowHandle);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"单元格点击异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 操作列按钮点击事件处理
        /// </summary>
        private void ButtonEdit_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            try
            {
                // 获取当前行数据
                int rowHandle = this.signGridView.FocusedRowHandle;
                if (rowHandle < 0)
                {
                    MessageBox.Show("请先选择一行数据！");
                    return;
                }

                // 获取id和发放单号
                int id = Convert.ToInt32(this.signGridView.GetRowCellValue(rowHandle, "id"));
                string issueCode = this.signGridView.GetRowCellValue(rowHandle, "IssueCode")?.ToString();
                string issueStateName = this.signGridView.GetRowCellValue(rowHandle, "IssueStateName")?.ToString();
                
                if (id <= 0)
                {
                    MessageBox.Show("无法获取记录ID！");
                    return;
                }

                // 根据按钮标签和当前状态执行不同操作
                if (e.Button.Tag.ToString() == "Sign" && issueStateName != "已签收")
                {
                    // 执行签收功能
                    PerformSignAction(id, issueCode);
                }
                else if (e.Button.Tag.ToString() == "Withdraw" && issueStateName == "已签收")
                {
                    // 执行撤回功能
                    PerformWithdrawAction(id, issueCode);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作出错: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"操作按钮错误: {ex}");
            }
        }

        /// <summary>
        /// 执行签收操作
        /// </summary>
        private async void PerformSignAction(int id, string issueCode)
        {
            // 不再需要确认对话框，因为已经在点击事件中添加了确认
            // 直接执行签收操作

            try
            {
                // 显示等待状态
                signGridControl.UseWaitCursor = true;
                
                // 构建API URL - 修改为正确的路由
                //string signUrl = $"http://localhost:5192/api/DepartmentManagement/updatesignStatus";
                string signUrl = $"http://***********:4060/api/DepartmentManagement/updatesignStatus";
                // 使用正确的后端路由
                System.Diagnostics.Debug.WriteLine($"签收URL: {signUrl}");

                // 添加消息提示
                signLblRecordCount.Text = "正在提交签收...";
                Application.DoEvents();

                // 创建更新数据，按照后端API要求的格式
                var signData = new JObject
                {
                    ["IssueId"] = id,  // 使用IssueId作为参数名
                    ["IssueState"] = 1   // 表示已签收状态
                };
                
                // 记录发送的JSON数据以便调试
                System.Diagnostics.Debug.WriteLine($"签收请求数据: {signData.ToString()}");

                // 发送签收请求
                using (HttpClient client = new HttpClient())
                {
                    // 设置超时时间
                    client.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 准备请求内容
                    var content = new StringContent(signData.ToString(), Encoding.UTF8, "application/json");
                    
                    // 发送POST请求（改为POST方法，因为服务器不允许PUT）
                    HttpResponseMessage response = await client.PostAsync(signUrl, content);
                    
                    // 处理响应
                    if (response.IsSuccessStatusCode)
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"响应内容: {responseContent}");
                        
                        // 尝试解析响应
                        try
                        {
                            var apiResponse = JsonConvert.DeserializeObject<JObject>(responseContent);
                            
                            // 假设成功响应格式为 {"code": 200, "msg": "成功"}
                            if (apiResponse != null)
                            {
                                MessageBox.Show("签收成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                // 不再刷新整个表格，因为已经在点击事件中更新了状态
                            }
                            else
                            {
                                MessageBox.Show($"签收失败: {apiResponse?["msg"]?.ToString() ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"处理响应时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        // 获取错误响应内容
                        string errorContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"错误响应: {errorContent}");
                        
                        MessageBox.Show($"签收请求失败: HTTP {(int)response.StatusCode} - {response.ReasonPhrase}\n响应内容: {errorContent}", 
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"签收异常详情: {ex}");
                MessageBox.Show($"签收过程中出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复光标
                signGridControl.UseWaitCursor = false;
                // 不再刷新整个表格，避免闪烁和重复刷新
            }
        }

        /// <summary>
        /// 执行撤回操作
        /// </summary>
        private async void PerformWithdrawAction(int id, string issueCode)
        {
            // 不再需要确认对话框，因为已经在点击事件中添加了确认
            // 直接执行撤回操作

            try
            {
                // 显示等待状态
                signGridControl.UseWaitCursor = true;
                
                // 构建API URL - 修改为正确的路由
                //string withdrawUrl = $"http://localhost:5192/api/DepartmentManagement/updatesignStatus";
                string withdrawUrl = $"http://***********:4060/api/DepartmentManagement/updatesignStatus";
                // 使用正确的后端路由
                System.Diagnostics.Debug.WriteLine($"撤回URL: {withdrawUrl}");
                
                // 添加消息提示
                signLblRecordCount.Text = "正在提交撤回...";
                Application.DoEvents();

                // 创建更新数据，按照后端API要求的格式
                var withdrawData = new JObject
                {
                    ["IssueId"] = id,  // 使用IssueId作为参数名
                    ["IssueState"] = 0   // 表示已发放状态
                };
                
                // 记录发送的JSON数据以便调试
                System.Diagnostics.Debug.WriteLine($"撤回请求数据: {withdrawData.ToString()}");

                // 发送撤回请求
                using (HttpClient client = new HttpClient())
                {
                    // 设置超时时间
                    client.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 准备请求内容
                    var content = new StringContent(withdrawData.ToString(), Encoding.UTF8, "application/json");
                    
                    // 发送POST请求（改为POST方法，因为服务器不允许PUT）
                    HttpResponseMessage response = await client.PostAsync(withdrawUrl, content);
                    
                    // 处理响应
                    if (response.IsSuccessStatusCode)
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"响应内容: {responseContent}");
                        
                        // 尝试解析响应
                        try
                        {
                            var apiResponse = JsonConvert.DeserializeObject<JObject>(responseContent);
                            
                            // 假设成功响应格式为 {"code": 200, "msg": "成功"}
                            if (apiResponse != null)
                            {
                                MessageBox.Show("撤回签收成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                // 不再刷新整个表格，因为已经在点击事件中更新了状态
                            }
                            else
                            {
                                MessageBox.Show($"撤回签收失败: {apiResponse?["msg"]?.ToString() ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"处理响应时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        // 获取错误响应内容
                        string errorContent = await response.Content.ReadAsStringAsync();
                        System.Diagnostics.Debug.WriteLine($"错误响应: {errorContent}");
                        
                        MessageBox.Show($"撤回签收请求失败: HTTP {(int)response.StatusCode} - {response.ReasonPhrase}\n响应内容: {errorContent}", 
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"撤回签收异常详情: {ex}");
                MessageBox.Show($"撤回签收过程中出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复光标
                signGridControl.UseWaitCursor = false;
                // 不再刷新整个表格，避免闪烁和重复刷新
            }
        }

        /// <summary>
        /// 从API加载数据并绑定到表格
        /// </summary>
        private async void BindGrid()
        {
            try
            {
                
                // 签收列表API地址
                //string baseUrl = "http://localhost:5172/api/DepartmentManagement/sign";
                string baseUrl = "http://***********:4050/api/DepartmentManagement/sign";
                var queryParams = new List<string>();

                // 添加日期筛选参数
                if (this.signDateEditStart.EditValue != null && DateTime.TryParse(this.signDateEditStart.EditValue.ToString(), out DateTime startDate))
                {
                    string formattedStartDate = startDate.ToString("yyyy-MM-dd");
                    queryParams.Add($"startTime={Uri.EscapeDataString(formattedStartDate)}");
                }
                
                if (this.signDateEditEnd.EditValue != null && DateTime.TryParse(this.signDateEditEnd.EditValue.ToString(), out DateTime endDate))
                {
                    // 确保结束日期包含当天的所有记录
                    endDate = endDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
                    string formattedEndDate = endDate.ToString("yyyy-MM-dd HH:mm:ss");
                    queryParams.Add($"endTime={Uri.EscapeDataString(formattedEndDate)}");
                }

                // 构建完整URL
                string url = baseUrl;
                if (queryParams.Count > 0)
                {
                    url += "?" + string.Join("&", queryParams);
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"请求URL: {url}");
                
                // 显示正在查询的消息
                signLblRecordCount.Text = "正在查询数据...";
                Application.DoEvents();

                // 发送API请求
                using (HttpClient client = new HttpClient())
                {
                    HttpResponseMessage response = await client.GetAsync(url);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        string result = await response.Content.ReadAsStringAsync();
                        
                        // 解析JSON响应
                        var jobj = JsonConvert.DeserializeObject<JObject>(result);
                        
                        // 打印完整的JSON响应以便调试
                        System.Diagnostics.Debug.WriteLine($"完整的API响应: {result}");
                        
                        if (jobj != null && jobj["data"] != null)
                        {
                            // 创建DataTable并定义列
                            var table = new System.Data.DataTable();
                            table.Columns.Add("id", typeof(int));
                            table.Columns.Add("IssueCode", typeof(string));
                            table.Columns.Add("IssueName", typeof(string));
                            table.Columns.Add("IssueDate", typeof(string));
                            table.Columns.Add("acceptName", typeof(string));
                            table.Columns.Add("IssueStateName", typeof(string));
                            table.Columns.Add("IssueState", typeof(int));
                            
                            // 填充数据
                            JArray dataArray = jobj["data"] as JArray;
                            if (dataArray != null)
                            {
                                foreach (var item in dataArray)
                                {
                                    // 调试输出，查看返回的数据结构
                                    System.Diagnostics.Debug.WriteLine($"签收数据项: {item}");
                                    
                                    // 适配字段名，处理可能的JSON字段命名不一致
                                    int itemId = item["id"] != null ? Convert.ToInt32(item["id"]) : 0;
                                    
                                    // 使用辅助方法获取发放单号，尝试多种字段名格式
                                    string issueCode = GetJsonValue(item, "IssueCode");
                                    System.Diagnostics.Debug.WriteLine($"发放单号字段值: {issueCode}");
                                    
                                    // 使用辅助方法获取发放人，尝试多种字段名格式
                                    string issueName = GetJsonValue(item, "IssueName");
                                    System.Diagnostics.Debug.WriteLine($"发放人字段值: {issueName}");
                                    
                                    // 使用辅助方法获取发放时间，尝试多种字段名格式
                                    string issueDate = GetJsonValue(item, "IssueDate");
                                    System.Diagnostics.Debug.WriteLine($"发放时间字段值: {issueDate}");
                                    
                                    // 尝试转换为更友好的日期格式
                                    if (DateTime.TryParse(issueDate, out DateTime dt))
                                    {
                                        issueDate = dt.ToString("yyyy-MM-dd HH:mm:ss");
                                    }
                                    
                                    // 使用辅助方法获取接收人
                                    string acceptName = GetJsonValue(item, "acceptName");
                                    System.Diagnostics.Debug.WriteLine($"接收人字段值: {acceptName}");
                                    
                                    // 使用辅助方法获取发放状态名称
                                    string issueStateName = GetJsonValue(item, "IssueStateName");
                                    if (string.IsNullOrEmpty(issueStateName)) 
                                    {
                                        issueStateName = "已发放"; // 默认值
                                    }
                                    System.Diagnostics.Debug.WriteLine($"发放状态名称字段值: {issueStateName}");
                                    
                                    // 尝试获取发放状态值
                                    int issueState = 0;
                                    string issueStateStr = GetJsonValue(item, "IssueState");
                                    if (!string.IsNullOrEmpty(issueStateStr) && int.TryParse(issueStateStr, out int state))
                                    {
                                        issueState = state;
                                    }
                                    System.Diagnostics.Debug.WriteLine($"发放状态值字段值: {issueState}");
                                    
                                    table.Rows.Add(
                                        itemId,
                                        issueCode,
                                        issueName,
                                        issueDate,
                                        acceptName,
                                        issueStateName,
                                        issueState
                                    );
                                }
                            }
                            
                            signGridControl.DataSource = table;
                            
                            // 显示记录数量
                            signLblRecordCount.Text = $"共 {table.Rows.Count} 条记录";
                        }
                        else
                        {
                            // API返回数据为空，加载测试数据
                            LoadTestData();
                            signLblRecordCount.Text = "共 3 条记录 (测试数据)";
                        }
                    }
                    else
                    {
                        string errorContent = await response.Content.ReadAsStringAsync();
                        throw new Exception($"API响应错误 {(int)response.StatusCode}: {errorContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                // 显示异常信息
                MessageBox.Show("获取签收列表数据失败：" + ex.Message);
                System.Diagnostics.Debug.WriteLine($"查询异常: {ex}");
                
                // 网络请求失败时加载测试数据
                LoadTestData();
            }
        }

        /// <summary>
        /// 加载测试数据
        /// </summary>
        private void LoadTestData()
        {
            try
            {
                // 创建DataTable并定义列
                var table = new DataTable();
                table.Columns.Add("id", typeof(int));
                table.Columns.Add("IssueCode", typeof(string));
                table.Columns.Add("IssueName", typeof(string));
                table.Columns.Add("IssueDate", typeof(string));
                table.Columns.Add("acceptName", typeof(string));
                table.Columns.Add("IssueStateName", typeof(string));
                table.Columns.Add("IssueState", typeof(int));

                // 根据图片中的数据添加测试数据，确保字段名称与后端返回的JSON完全一致
                table.Rows.Add(1, "123", "123", "2025-07-26T14:51:42", "123", "已发放", 0);
                table.Rows.Add(2, "123", "123", "2025-07-26T14:51:42", "123", "已发放", 0);
                table.Rows.Add(3, "123", "123", "2025-07-26T14:51:42", "123", "已发放", 0);

                // 绑定数据源
                signGridControl.DataSource = table;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载测试数据异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            BindGrid();
        }
    }
}
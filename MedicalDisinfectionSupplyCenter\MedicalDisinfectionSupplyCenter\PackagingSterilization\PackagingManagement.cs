﻿// 引用DevExpress控件库，用于高级UI组件
using BarcodeStandard;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Columns;
// 引用JSON处理库
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
// 引用系统基础库
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
// 引用SkiaSharp库用于条码生成
using SkiaSharp;
// 引用项目公共组件
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.PackagingSterilization
{
    /// <summary>
    /// 包装管理用户控件 - 负责医疗器械包装的管理功能
    /// 主要功能：查看包装列表、查询包装记录、删除包装、查看包装详情
    /// </summary>
    public partial class PackagingManagement : UserControl
    {
        #region 数据模型定义

        /// <summary>
        /// API响应通用模型 - 用于解析后端API返回的标准格式数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        public class ApiResponse<T>
        {
            public int code { get; set; }      // 响应状态码
            public string msg { get; set; }    // 响应消息
            public T data { get; set; }        // 响应数据
        }

        /// <summary>
        /// 包装数据模型 - 定义包装记录的数据结构
        /// </summary>
        public class PackagingData
        {
            public int ID { get; set; } = 0;           // 包装记录唯一标识ID
            public string 包条码 { get; set; } = "";    // 包装条码，用于唯一标识包装
            public string 包名称 { get; set; } = "";    // 包装名称，描述包装内容
            public string 打包人 { get; set; } = "";    // 执行打包操作的人员
            public string 检查人 { get; set; } = "";    // 执行检查操作的人员
            public string 打包时间 { get; set; } = "";  // 打包操作的时间
        }

        #endregion

        #region 私有字段

        /// <summary>
        /// 包装数据列表 - 存储从API获取的包装记录
        /// </summary>
        private List<PackagingData> packagingList = new List<PackagingData>();

        /// <summary>
        /// API基础URL - 用于数据查询的后端服务地址
        /// </summary>
        //private const string API_BASE_URL = "http://localhost:5172";
        private const string API_BASE_URL = "http://***********:4050";

        /// <summary>
        /// API写操作基础URL - 用于数据修改操作的后端服务地址
        /// </summary>
        //private const string API_BASE_URLW = "http://localhost:5192";
        private const string API_BASE_URLW = "http://***********:4060";

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 构造函数 - 初始化包装管理控件
        /// 功能：初始化组件、绑定事件处理器
        /// </summary>
        public PackagingManagement()
        {
            InitializeComponent();

            // 绑定控件加载事件
            this.Load += PackagingManagement_Load;

            // 绑定查询按钮点击事件 - 用于按时间范围查询包装记录
            this.btnQuery.Click += BtnQuery_Click;

            // 绑定测试按钮点击事件 - 用于API测试功能
            this.button2.Click += Button2_Click;

            // 绑定测试扫描按钮点击事件 - 用于测试条码扫描功能
            //this.button3.Click += button3_Click;
        }

        /// <summary>
        /// 控件加载事件处理器
        /// 功能：在控件加载时初始化表格并加载数据
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void PackagingManagement_Load(object sender, EventArgs e)
        {
            InitializeGrid();           // 初始化数据表格
            await LoadPackagingData();  // 加载包装数据
        }

        /// <summary>
        /// 初始化数据表格
        /// 功能：设置表格列、添加操作按钮列、配置表格属性
        /// </summary>
        private void InitializeGrid()
        {
            try
            {
                // 清空现有列
                gridView.Columns.Clear();

                // 定义表格列配置
                var columns = new[]
                {
                    new { Caption = "ID", FieldName = "ID", Width = 0, Visible = false },        // ID列（隐藏）
                    new { Caption = "包条码", FieldName = "包条码", Width = 120, Visible = true },    // 包装条码列
                    new { Caption = "包名称", FieldName = "包名称", Width = 150, Visible = true },    // 包装名称列
                    new { Caption = "打包人", FieldName = "打包人", Width = 100, Visible = true },    // 打包人员列
                    new { Caption = "检查人", FieldName = "检查人", Width = 100, Visible = true },    // 检查人员列
                    new { Caption = "打包时间", FieldName = "打包时间", Width = 150, Visible = true }  // 打包时间列
                };

                // 动态创建表格列
                foreach (var col in columns)
                {
                    var gridColumn = new GridColumn()
                    {
                        Caption = col.Caption,      // 列标题
                        FieldName = col.FieldName,  // 字段名
                        Visible = col.Visible,      // 列可见性
                        Width = col.Width           // 列宽度
                    };

                    // 设置数据列为只读
                    gridColumn.OptionsColumn.AllowEdit = false;

                    gridView.Columns.Add(gridColumn);
                }

                // 配置表格显示属性（在添加操作列之前设置）
                gridView.OptionsView.ShowGroupPanel = false;    // 隐藏分组面板
                gridView.OptionsView.ColumnAutoWidth = false;   // 禁用列自动宽度

                // 重要：不能设置为完全不可编辑，否则按钮无法点击
                // gridView.OptionsBehavior.Editable = false;   // 注释掉这行

                // 添加操作按钮列（查看、删除）
                AddActionColumn();

                // 注意：操作按钮已独立处理，无需额外的行点击事件
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化表格失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 表格操作列配置

        /// <summary>
        /// 添加操作列到表格
        /// 功能：在表格中添加包含"查看"和"删除"按钮的操作列，每个按钮绑定独立的事件处理器
        /// </summary>
        private void AddActionColumn()
        {
            // 防止重复添加：先移除已存在的操作列
            var existingActionColumn = gridView.Columns.FirstOrDefault(c => c.FieldName == "Action");
            if (existingActionColumn != null)
            {
                gridView.Columns.Remove(existingActionColumn);
            }

            // 创建操作列
            GridColumn actionColumn = new GridColumn()
            {
                Caption = "操作",                           // 列标题
                FieldName = "Action",                      // 字段名
                Visible = true,                            // 列可见性
                Width = 120,                               // 列宽度
                OptionsColumn = { AllowEdit = true }       // 允许编辑（用于按钮交互）
            };

            // 创建按钮编辑器
            RepositoryItemButtonEdit buttonEdit = new RepositoryItemButtonEdit();
            buttonEdit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor; // 隐藏文本编辑器
            buttonEdit.Buttons.Clear(); // 清空默认按钮

            // 设置按钮编辑器的其他属性
            buttonEdit.ButtonsStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;

            // 添加查看按钮 - 直接绑定到ShowPackageDetails方法
            var viewButton = new DevExpress.XtraEditors.Controls.EditorButton(
                DevExpress.XtraEditors.Controls.ButtonPredefines.Search, "查看");
            viewButton.Tag = "VIEW";  // 设置按钮标识
            buttonEdit.Buttons.Add(viewButton);

            // 添加删除按钮 - 直接绑定到DeletePackage方法
            var deleteButton = new DevExpress.XtraEditors.Controls.EditorButton(
                DevExpress.XtraEditors.Controls.ButtonPredefines.Delete, "删除");
            deleteButton.Tag = "DELETE";  // 设置按钮标识
            buttonEdit.Buttons.Add(deleteButton);

            // 重要：先将按钮编辑器添加到表格控件的仓库中
            gridControl.RepositoryItems.Add(buttonEdit);

            // 绑定按钮点击事件处理器（必须在添加到仓库后绑定）
            buttonEdit.ButtonClick -= ButtonEdit_ButtonClick; // 先移除避免重复绑定
            buttonEdit.ButtonClick += ButtonEdit_ButtonClick;

            // 将按钮编辑器绑定到操作列
            actionColumn.ColumnEdit = buttonEdit;
            gridView.Columns.Add(actionColumn);

            // 添加调试信息
            System.Diagnostics.Debug.WriteLine($"操作列已添加，按钮数量: {buttonEdit.Buttons.Count}");
            System.Diagnostics.Debug.WriteLine("事件处理器已绑定");
        }

        #endregion

        #region 操作按钮事件处理

        /// <summary>
        /// 操作列按钮点击事件处理器（分离式处理）
        /// 功能：根据按钮类型分别调用对应的独立方法
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">按钮点击事件参数</param>
        private async void ButtonEdit_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            try
            {
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"按钮点击事件触发，按钮类型: {e.Button.Kind}");

                // 获取当前选中行的句柄
                var rowHandle = gridView.FocusedRowHandle;
                System.Diagnostics.Debug.WriteLine($"当前行句柄: {rowHandle}");

                if (rowHandle < 0)
                {
                    MessageBox.Show("请选择一行数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 从选中行获取包装信息
                var packageId = Convert.ToInt32(gridView.GetRowCellValue(rowHandle, "ID") ?? 0);
                var packageCode = gridView.GetRowCellValue(rowHandle, "包条码")?.ToString() ?? "";
                var packageName = gridView.GetRowCellValue(rowHandle, "包名称")?.ToString() ?? "";

                System.Diagnostics.Debug.WriteLine($"包装信息 - ID: {packageId}, 条码: {packageCode}, 名称: {packageName}");

                // 根据按钮类型分别调用独立的处理方法
                if (e.Button.Kind == DevExpress.XtraEditors.Controls.ButtonPredefines.Search)
                {
                    System.Diagnostics.Debug.WriteLine("执行查看操作");
                    // 查看按钮：直接调用ShowPackageDetails方法
                    ShowPackageDetails(packageCode, packageName);
                }
                else if (e.Button.Kind == DevExpress.XtraEditors.Controls.ButtonPredefines.Delete)
                {
                    System.Diagnostics.Debug.WriteLine("执行删除操作");
                    // 删除按钮：直接调用DeletePackage方法
                    await DeletePackage(packageId);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未识别的按钮类型: {e.Button.Kind}");
                    MessageBox.Show($"未识别的按钮类型: {e.Button.Kind}", "调试信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"按钮点击事件异常: {ex}");
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示包装详情（独立方法）
        /// 功能：直接显示包装详情，无需额外判断
        /// </summary>
        /// <param name="packageCode">包装条码</param>
        /// <param name="packageName">包装名称</param>
        private void ShowPackageDetails(string packageCode, string packageName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"ShowPackageDetails 方法被调用 - 条码: {packageCode}, 名称: {packageName}");

                // 直接显示包装详情信息
                MessageBox.Show($"查看包装详情\n包条码: {packageCode}\n包名称: {packageName}",
                    "包装详情", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ShowPackageDetails 异常: {ex}");
                MessageBox.Show($"显示详情失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 删除操作

        /// <summary>
        /// 删除包装记录（独立方法，带确认对话框）
        /// 功能：显示确认对话框后删除指定的包装记录
        /// </summary>
        /// <param name="packageId">包装ID</param>
        private async Task DeletePackage(int packageId)
        {
            try
            {
                // 验证包装ID有效性
                if (packageId <= 0)
                {
                    MessageBox.Show("包ID不能为空或无效", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取当前行的包装信息用于确认对话框显示
                var rowHandle = gridView.FocusedRowHandle;
                var packageCode = gridView.GetRowCellValue(rowHandle, "包条码")?.ToString() ?? "";
                var packageName = gridView.GetRowCellValue(rowHandle, "包名称")?.ToString() ?? "";

                // 显示删除确认对话框
                var confirmResult = MessageBox.Show(
                    $"确定要删除以下包装记录吗？\n\n包条码: {packageCode}\n包名称: {packageName}",
                    "确认删除",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                // 用户确认删除后执行删除操作
                if (confirmResult == DialogResult.Yes)
                {
                    // 构建删除API的URL（RESTful风格）
                    string deleteUrl = $"{API_BASE_URLW}/api/PackagingSterilization?id={packageId}";
                    System.Diagnostics.Debug.WriteLine($"删除URL: {deleteUrl}");

                    // 调用删除API
                    string deleteResult = await HttpClientHelper.ClientAsync("GET", deleteUrl, false, null);
                    System.Diagnostics.Debug.WriteLine($"删除响应: {deleteResult}");

                    // 处理API响应结果
                    var success = await ProcessApiResponse(deleteResult, "删除");
                    if (success)
                    {
                        // 删除成功后重新加载数据
                        await LoadPackagingData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"删除异常详情: {ex}");
            }
        }

        /// <summary>
        /// 直接删除包装记录（无确认对话框）
        /// 功能：直接删除指定的包装记录，不显示确认对话框
        /// </summary>
        /// <param name="packageId">包装ID</param>
        private async Task DeletePackageDirectly(int packageId)
        {
            try
            {
                // 验证包装ID有效性
                if (packageId <= 0)
                {
                    MessageBox.Show("包ID不能为空或无效", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 构建删除API的URL（RESTful风格）
                string deleteUrl = $"{API_BASE_URLW}/api/PackagingSterilization?id={packageId}";
                System.Diagnostics.Debug.WriteLine($"删除URL: {deleteUrl}");

                // 调用删除API
                string deleteResult = await HttpClientHelper.ClientAsync("GET", deleteUrl, false, null);
                System.Diagnostics.Debug.WriteLine($"删除响应: {deleteResult}");

                // 处理API响应结果
                var success = await ProcessApiResponse(deleteResult, "删除");
                if (success)
                {
                    // 删除成功后重新加载数据
                    await LoadPackagingData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"删除异常详情: {ex}");
            }
        }

        #endregion

        #region 数据加载

        /// <summary>
        /// 加载所有包装数据
        /// 功能：从API获取所有包装记录并显示在表格中
        /// </summary>
        private async Task LoadPackagingData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 开始加载包装数据...");

                // 清空现有数据
                packagingList.Clear();
                System.Diagnostics.Debug.WriteLine("✅ 已清空现有数据");

                // 构建API查询URL
                string url = $"{API_BASE_URL}/api/PackagingSterilization";
                System.Diagnostics.Debug.WriteLine($"📡 API请求URL: {url}");

                // 调用API获取数据
                string result = await HttpClientHelper.ClientAsync("GET", url, false, null);

                System.Diagnostics.Debug.WriteLine($"📥 API返回数据长度: {result?.Length ?? 0}");

                if (!string.IsNullOrEmpty(result))
                {
                    // 输出API返回的原始数据（前500字符用于调试）
                    int previewLength = Math.Min(500, result.Length);
                    System.Diagnostics.Debug.WriteLine($"📄 API返回原始数据预览: {result.Substring(0, previewLength)}");
                    if (result.Length > previewLength)
                    {
                        System.Diagnostics.Debug.WriteLine($"... (还有 {result.Length - previewLength} 个字符)");
                    }

                    // 解析并加载返回的数据
                    await ParseAndLoadData(result);
                    System.Diagnostics.Debug.WriteLine($"✅ 数据解析完成，共获得 {packagingList.Count} 条记录");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ API返回空数据");
                }

                // 绑定数据到表格并更新统计信息
                BindDataToGrid();
                UpdateTotalCount();

                System.Diagnostics.Debug.WriteLine($"🎯 数据加载完成，最终显示 {packagingList.Count} 条记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 加载数据异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ 异常堆栈: {ex.StackTrace}");

                MessageBox.Show($"加载数据失败: {ex.Message}\n\n请检查：\n1. 网络连接是否正常\n2. API服务是否启动\n3. API地址是否正确: {API_BASE_URL}",
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 出错时清空数据并更新显示
                packagingList.Clear();
                BindDataToGrid();
                UpdateTotalCount();
            }
        }

        /// <summary>
        /// 根据时间范围查询包装数据
        /// 功能：按指定的开始和结束时间查询包装记录
        /// </summary>
        /// <param name="startDate">查询开始时间</param>
        /// <param name="endDate">查询结束时间</param>
        private async Task LoadPackagingDataByDateRange(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // 清空现有数据
                packagingList.Clear();
                System.Diagnostics.Debug.WriteLine($"=== 开始时间范围查询，数据已清空，当前列表数量: {packagingList.Count} ===");

                // 直接使用格式1进行查询，避免多格式重复
                string result = await TryQueryFormat1(startDate, endDate);
                if (!string.IsNullOrEmpty(result))
                {
                    await ParseAndLoadData(result);
                    System.Diagnostics.Debug.WriteLine($"查询成功，获得数据: {packagingList.Count} 条");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("查询未返回数据");
                }

                // 绑定数据到表格并更新统计信息
                BindDataToGrid();
                UpdateTotalCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"查询异常详情: {ex}");
                // 出错时清空数据并更新显示
                packagingList.Clear();
                BindDataToGrid();
                UpdateTotalCount();
            }
        }

        #endregion

        #region 多格式查询支持

        /// <summary>
        /// 尝试多种查询格式，以适应不同的后端API
        /// 功能：依次尝试不同的API参数格式，直到找到有效的查询方式
        /// </summary>
        /// <param name="startDate">查询开始时间</param>
        /// <param name="endDate">查询结束时间</param>
        private async Task TryMultipleQueryFormats(DateTime? startDate, DateTime? endDate)
        {
            System.Diagnostics.Debug.WriteLine("=== 开始尝试多种查询格式 ===");
            int initialCount = packagingList.Count;
            bool dataLoaded = false;

            // 格式1: 使用 startDate, endDate 参数
            if (!dataLoaded)
            {
                System.Diagnostics.Debug.WriteLine("尝试查询格式1: startDate, endDate");
                string result = await TryQueryFormat1(startDate, endDate);
                if (!string.IsNullOrEmpty(result))
                {
                    System.Diagnostics.Debug.WriteLine($"格式1成功返回数据，长度: {result.Length}");
                    await ParseAndLoadData(result);
                    System.Diagnostics.Debug.WriteLine($"格式1解析完成，新增数据: {packagingList.Count - initialCount}");
                    dataLoaded = true;
                }
            }

            // 格式2: 使用 start, end 参数
            if (!dataLoaded)
            {
                System.Diagnostics.Debug.WriteLine("尝试查询格式2: start, end");
                string result = await TryQueryFormat2(startDate, endDate);
                if (!string.IsNullOrEmpty(result))
                {
                    System.Diagnostics.Debug.WriteLine($"格式2成功返回数据，长度: {result.Length}");
                    await ParseAndLoadData(result);
                    System.Diagnostics.Debug.WriteLine($"格式2解析完成，新增数据: {packagingList.Count - initialCount}");
                    dataLoaded = true;
                }
            }

            // 格式3: 使用 packingTimeStart, packingTimeEnd 参数
            if (!dataLoaded)
            {
                System.Diagnostics.Debug.WriteLine("尝试查询格式3: packingTimeStart, packingTimeEnd");
                string result = await TryQueryFormat3(startDate, endDate);
                if (!string.IsNullOrEmpty(result))
                {
                    System.Diagnostics.Debug.WriteLine($"格式3成功返回数据，长度: {result.Length}");
                    await ParseAndLoadData(result);
                    System.Diagnostics.Debug.WriteLine($"格式3解析完成，新增数据: {packagingList.Count - initialCount}");
                    dataLoaded = true;
                }
            }

            if (!dataLoaded)
            {
                System.Diagnostics.Debug.WriteLine("所有查询格式都未返回数据");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"查询成功，最终数据数量: {packagingList.Count}");
            }
        }

        #endregion

        #region 查询格式实现

        /// <summary>
        /// 查询格式1: 使用 StartTime, EndTime 参数（中介者模式）
        /// 功能：构建使用后端中介者模式的日期参数的查询URL
        /// </summary>
        /// <param name="startDate">查询开始时间</param>
        /// <param name="endDate">查询结束时间</param>
        /// <returns>API响应结果</returns>
        private async Task<string> TryQueryFormat1(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== TryQueryFormat1 开始 ===");
                System.Diagnostics.Debug.WriteLine($"传入参数 - startDate: {startDate}, endDate: {endDate}");

                string url = $"{API_BASE_URL}/api/PackagingSterilization";
                var queryParams = new List<string>();

                // 构建查询参数 - 使用后端中介者模式的参数名
                if (startDate.HasValue)
                {
                    string startDateStr = Uri.EscapeDataString(startDate.Value.ToString("yyyy-MM-dd"));
                    queryParams.Add($"StartTime={startDateStr}");
                    System.Diagnostics.Debug.WriteLine($"添加开始时间参数: StartTime={startDateStr}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("开始时间为空，不添加StartTime参数");
                }

                if (endDate.HasValue)
                {
                    string endDateStr = Uri.EscapeDataString(endDate.Value.ToString("yyyy-MM-dd"));
                    queryParams.Add($"EndTime={endDateStr}");
                    System.Diagnostics.Debug.WriteLine($"添加结束时间参数: EndTime={endDateStr}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("结束时间为空，不添加EndTime参数");
                }

                // 拼接查询参数到URL
                if (queryParams.Count > 0)
                {
                    url += "?" + string.Join("&", queryParams);
                    System.Diagnostics.Debug.WriteLine($"查询参数数量: {queryParams.Count}");
                    System.Diagnostics.Debug.WriteLine($"查询参数列表: {string.Join(", ", queryParams)}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("没有查询参数，使用基础URL");
                }

                System.Diagnostics.Debug.WriteLine($"最终查询URL: {url}");

                // 验证URL格式是否正确
                if (url.Contains("StartTime=") || url.Contains("EndTime="))
                {
                    System.Diagnostics.Debug.WriteLine("✅ URL包含正确的中介者模式参数");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ URL不包含预期的参数，可能会导致后端无法识别");
                }

                // 调用API
                string result = await HttpClientHelper.ClientAsync("GET", url, false, null);
                System.Diagnostics.Debug.WriteLine($"API返回数据长度: {result?.Length ?? 0}");
                if (!string.IsNullOrEmpty(result))
                {
                    System.Diagnostics.Debug.WriteLine($"API返回数据前100字符: {result.Substring(0, Math.Min(100, result.Length))}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"格式1查询异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// 查询格式2: 使用 start, end 参数
        /// 功能：构建使用简化日期参数的查询URL
        /// </summary>
        /// <param name="startDate">查询开始时间</param>
        /// <param name="endDate">查询结束时间</param>
        /// <returns>API响应结果</returns>
        private async Task<string> TryQueryFormat2(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                string url = $"{API_BASE_URL}/api/PackagingSterilization";
                var queryParams = new List<string>();

                // 构建查询参数（使用简化参数名）
                if (startDate.HasValue)
                {
                    string startDateStr = Uri.EscapeDataString(startDate.Value.ToString("yyyy-MM-dd"));
                    queryParams.Add($"start={startDateStr}");
                }
                if (endDate.HasValue)
                {
                    string endDateStr = Uri.EscapeDataString(endDate.Value.ToString("yyyy-MM-dd"));
                    queryParams.Add($"end={endDateStr}");
                }

                // 拼接查询参数到URL
                if (queryParams.Count > 0)
                {
                    url += "?" + string.Join("&", queryParams);
                }

                System.Diagnostics.Debug.WriteLine($"格式2查询URL: {url}");

                // 调用API
                string result = await HttpClientHelper.ClientAsync("GET", url, false, null);
                System.Diagnostics.Debug.WriteLine($"格式2返回数据长度: {result?.Length ?? 0}");

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"格式2查询失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查询格式3: 使用 packingTimeStart, packingTimeEnd 参数
        /// 功能：构建使用打包时间相关参数的查询URL
        /// </summary>
        /// <param name="startDate">查询开始时间</param>
        /// <param name="endDate">查询结束时间</param>
        /// <returns>API响应结果</returns>
        private async Task<string> TryQueryFormat3(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                string url = $"{API_BASE_URL}/api/PackagingSterilization";
                var queryParams = new List<string>();

                // 构建查询参数（使用打包时间相关参数名）
                if (startDate.HasValue)
                {
                    string startDateStr = Uri.EscapeDataString(startDate.Value.ToString("yyyy-MM-dd"));
                    queryParams.Add($"packingTimeStart={startDateStr}");
                }
                if (endDate.HasValue)
                {
                    string endDateStr = Uri.EscapeDataString(endDate.Value.ToString("yyyy-MM-dd"));
                    queryParams.Add($"packingTimeEnd={endDateStr}");
                }

                // 拼接查询参数到URL
                if (queryParams.Count > 0)
                {
                    url += "?" + string.Join("&", queryParams);
                }

                System.Diagnostics.Debug.WriteLine($"格式3查询URL: {url}");

                // 调用API
                string result = await HttpClientHelper.ClientAsync("GET", url, false, null);
                System.Diagnostics.Debug.WriteLine($"格式3返回数据长度: {result?.Length ?? 0}");

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"格式3查询失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region JSON数据解析

        /// <summary>
        /// 解析并加载JSON数据
        /// 功能：解析API返回的JSON数据，支持数组和对象两种格式
        /// </summary>
        /// <param name="jsonResult">API返回的JSON字符串</param>
        private async Task ParseAndLoadData(string jsonResult)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 开始解析JSON数据...");

                // 检查JSON数据是否为空
                if (string.IsNullOrWhiteSpace(jsonResult))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ JSON数据为空，跳过解析");
                    return;
                }

                // 尝试解析JSON数据
                JToken jsonToken;
                try
                {
                    jsonToken = JToken.Parse(jsonResult);
                    System.Diagnostics.Debug.WriteLine($"✅ JSON解析成功，类型: {jsonToken.Type}");
                }
                catch (Exception parseEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ JSON解析失败: {parseEx.Message}");
                    System.Diagnostics.Debug.WriteLine($"❌ 原始JSON数据: {jsonResult}");
                    throw new Exception($"JSON格式错误: {parseEx.Message}");
                }

                if (jsonToken is JArray jsonArray)
                {
                    System.Diagnostics.Debug.WriteLine($"📋 处理数组格式响应，包含 {jsonArray.Count} 个元素");
                    // 处理直接数组格式的响应
                    ProcessJsonArray(jsonArray);
                }
                else if (jsonToken is JObject jsonObject)
                {
                    System.Diagnostics.Debug.WriteLine("📦 处理对象格式响应");
                    // 处理包装对象格式的响应
                    await ProcessJsonObject(jsonObject);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 未识别的JSON类型: {jsonToken.Type}");
                    throw new Exception($"不支持的JSON格式: {jsonToken.Type}");
                }

                System.Diagnostics.Debug.WriteLine("✅ JSON数据解析完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ JSON解析异常: {ex.Message}");
                throw new Exception($"JSON解析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理JSON对象格式的响应
        /// 功能：解析包装对象格式的API响应，提取数据字段
        /// </summary>
        /// <param name="jsonObject">JSON对象</param>
        private async Task ProcessJsonObject(JObject jsonObject)
        {
            // 打印所有属性名用于调试
            var properties = jsonObject.Properties().Select(p => p.Name).ToArray();
            System.Diagnostics.Debug.WriteLine($"JSON对象属性: {string.Join(", ", properties)}");

            // 检查API响应状态
            var code = GetIntValue(jsonObject, "code");
            var msg = GetStringValue(jsonObject, "msg");

            // 验证响应状态码
            if (code.HasValue && code.Value != 200 && code.Value != 0)
            {
                throw new Exception($"API返回错误 (code: {code.Value}): {msg}");
            }

            // 尝试获取数据字段（支持多种可能的字段名）
            var dataToken = jsonObject["data"] ??
                           jsonObject["Data"] ??
                           jsonObject["result"] ??
                           jsonObject["Result"] ??
                           jsonObject["items"] ??
                           jsonObject["Items"] ??
                           jsonObject["list"] ??
                           jsonObject["List"];

            if (dataToken != null)
            {
                System.Diagnostics.Debug.WriteLine($"找到数据字段，类型: {dataToken.Type}");

                if (dataToken is JArray dataArray)
                {
                    // 数据字段是数组，直接处理
                    ProcessJsonArray(dataArray);
                }
                else if (dataToken is JObject dataObject)
                {
                    // 数据字段是对象，包装成数组处理
                    ProcessJsonArray(new JArray { dataObject });
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("未找到数据字段，尝试将整个对象作为数据处理");
                // 如果没有找到数据字段，尝试将整个对象作为单个数据项
                ProcessJsonArray(new JArray { jsonObject });
            }
        }

        /// <summary>
        /// 处理JSON数组格式的数据
        /// 功能：遍历JSON数组，将每个对象转换为PackagingData并添加到列表中
        /// </summary>
        /// <param name="jsonArray">JSON数组</param>
        private void ProcessJsonArray(JArray jsonArray)
        {
            // 检查数组是否为空
            if (jsonArray == null || jsonArray.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("数组为空或null");
                return;
            }

            int beforeCount = packagingList.Count;
            System.Diagnostics.Debug.WriteLine($"处理数组，包含 {jsonArray.Count} 个项目，当前列表已有 {beforeCount} 条数据");

            // 遍历数组中的每个项目
            for (int i = 0; i < jsonArray.Count; i++)
            {
                var item = jsonArray[i];
                if (item == null || !(item is JObject obj))
                {
                    System.Diagnostics.Debug.WriteLine($"项目 {i} 为null或不是JObject，跳过");
                    continue;
                }

                try
                {
                    // 打印每个对象的属性用于调试
                    var itemProperties = obj.Properties().Select(p => $"{p.Name}:{p.Value}").ToArray();
                    System.Diagnostics.Debug.WriteLine($"项目 {i} 属性: {string.Join(", ", itemProperties)}");

                    // 创建包装数据对象，支持更多字段名映射
                    var packageData = new PackagingData
                    {
                        ID = GetJsonIntValue(obj, "id", "ID", "Id", "包ID", "packageId", "pkgId"),                                    // 包装ID
                        包条码 = GetJsonValue(obj, "包条码", "itemCode", "barcode", "code", "packageCode", "pkgCode", "barcodeNo"),    // 包装条码
                        包名称 = GetJsonValue(obj, "包名称", "itemName", "name", "title", "packageName", "pkgName", "description"),   // 包装名称
                        打包人 = GetJsonValue(obj, "打包人", "packer", "packUser", "operator", "packOperator", "packBy"),              // 打包人员
                        检查人 = GetJsonValue(obj, "检查人", "examiner", "checkUser", "inspector", "checker", "checkedBy"),           // 检查人员
                        打包时间 = GetJsonValue(obj, "打包时间", "packingTime", "createTime", "time", "date", "packTime", "createdAt") // 打包时间
                    };

                    // 放宽数据有效性检查：只要有ID或任何一个字段有值就添加到列表
                    bool hasValidData = packageData.ID > 0 ||
                                       !string.IsNullOrWhiteSpace(packageData.包条码) ||
                                       !string.IsNullOrWhiteSpace(packageData.包名称) ||
                                       !string.IsNullOrWhiteSpace(packageData.打包人) ||
                                       !string.IsNullOrWhiteSpace(packageData.检查人) ||
                                       !string.IsNullOrWhiteSpace(packageData.打包时间);

                    if (hasValidData)
                    {
                        // 改进去重逻辑：基于ID和包条码的组合去重，更加宽松
                        bool isDuplicate = false;

                        // 如果有ID，优先基于ID去重
                        if (packageData.ID > 0)
                        {
                            isDuplicate = packagingList.Any(p => p.ID == packageData.ID && p.ID > 0);
                        }
                        // 如果没有ID但有包条码，基于包条码去重
                        else if (!string.IsNullOrWhiteSpace(packageData.包条码))
                        {
                            isDuplicate = packagingList.Any(p => p.包条码 == packageData.包条码 && !string.IsNullOrWhiteSpace(p.包条码));
                        }

                        if (!isDuplicate)
                        {
                            packagingList.Add(packageData);
                            System.Diagnostics.Debug.WriteLine($"✅ 添加数据: ID={packageData.ID}, 条码={packageData.包条码}, 名称={packageData.包名称}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ 跳过重复数据: ID={packageData.ID}, 条码={packageData.包条码}, 名称={packageData.包名称}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 跳过无效数据: 项目 {i} 所有字段都为空");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 处理项目 {i} 时出错: {ex.Message}");
                    // 即使出错也继续处理下一个项目，不中断整个过程
                }
            }

            int afterCount = packagingList.Count;
            System.Diagnostics.Debug.WriteLine($"🎯 数组处理完成，新增 {afterCount - beforeCount} 条数据，总计 {afterCount} 条数据");

            // 如果没有新增任何数据，输出警告
            if (afterCount == beforeCount)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ 警告：处理了 {jsonArray.Count} 个项目但没有新增任何数据，可能存在数据格式问题或过滤过于严格");
            }
        }

        #endregion

        #region 数据绑定和显示

        /// <summary>
        /// 将数据绑定到表格控件
        /// 功能：将包装数据列表转换为DataTable并绑定到表格显示
        /// </summary>
        private void BindDataToGrid()
        {
            try
            {
                // 创建数据表结构
                DataTable dt = new DataTable();
                dt.Columns.Add("ID", typeof(int));           // 包装ID列（隐藏列，用于操作）
                dt.Columns.Add("包条码", typeof(string));     // 包装条码列
                dt.Columns.Add("包名称", typeof(string));     // 包装名称列
                dt.Columns.Add("打包人", typeof(string));     // 打包人员列
                dt.Columns.Add("检查人", typeof(string));     // 检查人员列
                dt.Columns.Add("打包时间", typeof(string));   // 打包时间列

                // 将包装数据添加到数据表
                foreach (var item in packagingList)
                {
                    var row = dt.NewRow();
                    row["ID"] = item.ID;           // 包装ID
                    row["包条码"] = item.包条码;     // 包装条码
                    row["包名称"] = item.包名称;     // 包装名称
                    row["打包人"] = item.打包人;     // 打包人员
                    row["检查人"] = item.检查人;     // 检查人员
                    row["打包时间"] = item.打包时间; // 打包时间
                    dt.Rows.Add(row);
                }

                // 绑定数据到表格控件
                gridControl.DataSource = dt;
                System.Diagnostics.Debug.WriteLine($"绑定到表格: {dt.Rows.Count} 行数据");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"绑定数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新总数显示标签
        /// 功能：在界面上显示当前包装记录的总数量
        /// </summary>
        private void UpdateTotalCount()
        {
            try
            {
                lblTotal.Text = $"合计：{packagingList.Count}条";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新总数失败: {ex.Message}");
                lblTotal.Text = "合计：0条";
            }
        }

        #endregion

        #region 查询功能

        /// <summary>
        /// 查询按钮点击事件处理器
        /// 功能：根据用户选择的时间范围查询包装记录
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void BtnQuery_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 查询按钮点击事件开始 ===");

                // 检查控件是否存在
                if (dateEditStart == null)
                {
                    System.Diagnostics.Debug.WriteLine("错误：dateEditStart 控件为 null");
                    MessageBox.Show("开始时间控件未初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (dateEditEnd == null)
                {
                    System.Diagnostics.Debug.WriteLine("错误：dateEditEnd 控件为 null");
                    MessageBox.Show("结束时间控件未初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 从日期控件获取查询时间范围
                System.Diagnostics.Debug.WriteLine($"dateEditStart.EditValue 类型: {dateEditStart.EditValue?.GetType()}");
                System.Diagnostics.Debug.WriteLine($"dateEditEnd.EditValue 类型: {dateEditEnd.EditValue?.GetType()}");

                DateTime? startDate = dateEditStart.EditValue as DateTime?;
                DateTime? endDate = dateEditEnd.EditValue as DateTime?;

                // 调试输出原始控件值
                System.Diagnostics.Debug.WriteLine($"原始开始时间控件值: {dateEditStart.EditValue}");
                System.Diagnostics.Debug.WriteLine($"原始结束时间控件值: {dateEditEnd.EditValue}");
                System.Diagnostics.Debug.WriteLine($"解析后开始时间: {startDate}");
                System.Diagnostics.Debug.WriteLine($"解析后结束时间: {endDate}");

                // 尝试其他方式获取日期值
                if (!startDate.HasValue && dateEditStart.EditValue != null)
                {
                    System.Diagnostics.Debug.WriteLine("尝试其他方式解析开始时间...");
                    if (DateTime.TryParse(dateEditStart.EditValue.ToString(), out DateTime parsedStart))
                    {
                        startDate = parsedStart;
                        System.Diagnostics.Debug.WriteLine($"重新解析的开始时间: {startDate}");
                    }
                }

                if (!endDate.HasValue && dateEditEnd.EditValue != null)
                {
                    System.Diagnostics.Debug.WriteLine("尝试其他方式解析结束时间...");
                    if (DateTime.TryParse(dateEditEnd.EditValue.ToString(), out DateTime parsedEnd))
                    {
                        endDate = parsedEnd;
                        System.Diagnostics.Debug.WriteLine($"重新解析的结束时间: {endDate}");
                    }
                }

                // 验证时间范围的有效性
                if (startDate.HasValue && endDate.HasValue && startDate.Value > endDate.Value)
                {
                    MessageBox.Show("开始时间不能大于结束时间", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 构建查询信息用于显示和调试
                string queryInfo = "查询条件：";
                if (startDate.HasValue)
                {
                    queryInfo += $"开始时间：{startDate.Value:yyyy-MM-dd}";
                }
                if (endDate.HasValue)
                {
                    if (startDate.HasValue) queryInfo += "，";
                    queryInfo += $"结束时间：{endDate.Value:yyyy-MM-dd}";
                }
                if (!startDate.HasValue && !endDate.HasValue)
                {
                    queryInfo += "查询全部数据";
                }

                System.Diagnostics.Debug.WriteLine($"=== 开始查询 ===");
                System.Diagnostics.Debug.WriteLine(queryInfo);

                // 强制清空数据，确保没有残留
                packagingList.Clear();
                BindDataToGrid();
                System.Diagnostics.Debug.WriteLine("查询前强制清空数据完成");

                // 根据是否有时间范围选择不同的查询方式
                if (!startDate.HasValue && !endDate.HasValue)
                {
                    // 没有时间限制，查询全部数据
                    System.Diagnostics.Debug.WriteLine("执行全部数据查询");
                    await LoadPackagingData();
                }
                else
                {
                    // 有时间限制，按时间范围查询
                    System.Diagnostics.Debug.WriteLine("执行时间范围查询");
                    await LoadPackagingDataByDateRange(startDate, endDate);
                }

                System.Diagnostics.Debug.WriteLine($"=== 查询完成，结果数量: {packagingList.Count} ===");

                // 显示查询结果统计
                MessageBox.Show($"查询完成，共找到 {packagingList.Count} 条记录", "查询结果",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"查询异常详情: {ex}");
            }
        }

        #endregion

        #region API响应处理

        /// <summary>
        /// 处理API响应结果
        /// 功能：解析API响应，判断操作是否成功并显示相应消息
        /// </summary>
        /// <param name="response">API响应字符串</param>
        /// <param name="operation">操作名称（用于消息显示）</param>
        /// <returns>操作是否成功</returns>
        private async Task<bool> ProcessApiResponse(string response, string operation)
        {
            try
            {
                // 如果响应为空，默认认为操作成功
                if (string.IsNullOrEmpty(response))
                {
                    MessageBox.Show($"{operation}成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return true;
                }

                // 解析JSON响应
                var jsonResult = JObject.Parse(response);
                var code = jsonResult["code"]?.ToObject<int>() ?? 0;
                var msg = jsonResult["msg"]?.ToString() ?? "";

                // 根据状态码判断操作结果
                if (code == 200)
                {
                    MessageBox.Show($"{operation}成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return true;
                }
                else
                {
                    MessageBox.Show($"{operation}失败: {msg}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch
            {
                // 解析失败时默认认为操作成功
                MessageBox.Show($"{operation}成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return true;
            }
        }

        #endregion

        #region JSON数据提取辅助方法

        /// <summary>
        /// 从JSON对象中获取字符串值
        /// 功能：支持多个可能的字段名，返回第一个非空值
        /// </summary>
        /// <param name="obj">JSON对象</param>
        /// <param name="keys">可能的字段名数组</param>
        /// <returns>字符串值，如果都为空则返回空字符串</returns>
        private string GetJsonValue(JObject obj, params string[] keys)
        {
            foreach (var key in keys)
            {
                var value = obj[key]?.ToString();
                if (!string.IsNullOrEmpty(value)) return value;
            }
            return "";
        }

        /// <summary>
        /// 从JSON对象中获取整数值（可为空）
        /// 功能：安全地解析整数值，失败时返回null
        /// </summary>
        /// <param name="obj">JSON对象</param>
        /// <param name="key">字段名</param>
        /// <returns>整数值或null</returns>
        private int? GetIntValue(JObject obj, string key)
        {
            try
            {
                var token = obj[key];
                if (token != null && int.TryParse(token.ToString(), out int result))
                {
                    return result;
                }
            }
            catch { }
            return null;
        }

        /// <summary>
        /// 从JSON对象中获取字符串值（单个字段）
        /// 功能：安全地获取字符串值，失败时返回空字符串
        /// </summary>
        /// <param name="obj">JSON对象</param>
        /// <param name="key">字段名</param>
        /// <returns>字符串值</returns>
        private string GetStringValue(JObject obj, string key)
        {
            try
            {
                return obj[key]?.ToString() ?? "";
            }
            catch { }
            return "";
        }

        /// <summary>
        /// 从JSON对象中获取整数值（多字段支持）
        /// 功能：支持多个可能的字段名，返回第一个有效的整数值
        /// </summary>
        /// <param name="obj">JSON对象</param>
        /// <param name="keys">可能的字段名数组</param>
        /// <returns>整数值，如果都无效则返回0</returns>
        private int GetJsonIntValue(JObject obj, params string[] keys)
        {
            foreach (var key in keys)
            {
                var value = obj[key]?.ToString();
                if (!string.IsNullOrEmpty(value) && int.TryParse(value, out int result))
                {
                    return result;
                }
            }
            return 0; // 默认值
        }

        #endregion

        #region 测试和调试方法

        /// <summary>
        /// 测试按钮事件绑定
        /// 功能：手动测试按钮点击事件是否正常工作
        /// </summary>
        public void TestButtonEvents()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始测试按钮事件...");

                // 测试查看功能
                ShowPackageDetails("TEST001", "测试包装");

                System.Diagnostics.Debug.WriteLine("按钮事件测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"按钮事件测试失败: {ex}");
                MessageBox.Show($"按钮事件测试失败: {ex.Message}", "测试错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 诊断数据显示问题
        /// 功能：详细分析数据加载和显示过程中的问题
        /// </summary>
        public async Task DiagnoseDataDisplayIssues()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 开始诊断数据显示问题...");

                // 1. 检查API连接
                System.Diagnostics.Debug.WriteLine("1️⃣ 检查API连接...");
                string testUrl = $"{API_BASE_URL}/api/PackagingSterilization";
                string apiResult = await HttpClientHelper.ClientAsync("GET", testUrl, false, null);

                if (string.IsNullOrEmpty(apiResult))
                {
                    System.Diagnostics.Debug.WriteLine("❌ API返回空数据");
                    MessageBox.Show("API返回空数据，请检查：\n1. API服务是否启动\n2. 网络连接是否正常\n3. API地址是否正确",
                        "诊断结果", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"✅ API连接正常，返回数据长度: {apiResult.Length}");

                // 2. 分析JSON结构
                System.Diagnostics.Debug.WriteLine("2️⃣ 分析JSON结构...");
                try
                {
                    JToken token = JToken.Parse(apiResult);
                    System.Diagnostics.Debug.WriteLine($"JSON根类型: {token.Type}");

                    if (token is JObject obj)
                    {
                        var properties = obj.Properties().Select(p => p.Name).ToArray();
                        System.Diagnostics.Debug.WriteLine($"对象属性: {string.Join(", ", properties)}");
                    }
                    else if (token is JArray arr)
                    {
                        System.Diagnostics.Debug.WriteLine($"数组长度: {arr.Count}");
                        if (arr.Count > 0 && arr[0] is JObject firstItem)
                        {
                            var firstProperties = firstItem.Properties().Select(p => p.Name).ToArray();
                            System.Diagnostics.Debug.WriteLine($"第一个元素属性: {string.Join(", ", firstProperties)}");
                        }
                    }
                }
                catch (Exception jsonEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ JSON解析失败: {jsonEx.Message}");
                }

                // 3. 测试数据解析
                System.Diagnostics.Debug.WriteLine("3️⃣ 测试数据解析...");
                int originalCount = packagingList.Count;
                await ParseAndLoadData(apiResult);
                int newCount = packagingList.Count;

                System.Diagnostics.Debug.WriteLine($"解析前数据量: {originalCount}");
                System.Diagnostics.Debug.WriteLine($"解析后数据量: {newCount}");
                System.Diagnostics.Debug.WriteLine($"新增数据量: {newCount - originalCount}");

                // 4. 检查表格绑定
                System.Diagnostics.Debug.WriteLine("4️⃣ 检查表格绑定...");
                BindDataToGrid();
                int gridRowCount = gridView.RowCount;
                System.Diagnostics.Debug.WriteLine($"表格行数: {gridRowCount}");

                // 5. 生成诊断报告
                string report = $"📊 数据显示诊断报告:\n\n" +
                               $"API数据长度: {apiResult.Length} 字符\n" +
                               $"解析后数据量: {packagingList.Count} 条\n" +
                               $"表格显示行数: {gridRowCount} 行\n\n";

                if (packagingList.Count == 0)
                {
                    report += "❌ 问题：没有解析到任何数据\n可能原因：\n1. JSON格式不匹配\n2. 字段名映射错误\n3. 数据过滤过于严格";
                }
                else if (gridRowCount != packagingList.Count)
                {
                    report += "❌ 问题：数据绑定异常\n可能原因：\n1. 表格绑定失败\n2. 数据格式错误";
                }
                else
                {
                    report += "✅ 数据显示正常";
                }

                MessageBox.Show(report, "诊断报告", MessageBoxButtons.OK, MessageBoxIcon.Information);
                System.Diagnostics.Debug.WriteLine("🔧 诊断完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 诊断过程异常: {ex.Message}");
                MessageBox.Show($"诊断过程出错: {ex.Message}", "诊断错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 按钮事件和公共方法

        /// <summary>
        /// 包装注册按钮点击事件处理器
        /// 功能：打开包装注册窗口，注册完成后刷新数据
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void button1_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建并显示包装注册窗口
                PackagingRegistration regForm = new PackagingRegistration();
                var result = regForm.ShowDialog();

                // 如果注册成功，刷新数据列表
                if (result == DialogResult.OK)
                {
                    await LoadPackagingData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开注册窗口失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// API测试按钮点击事件处理器
        /// 功能：测试API连接和数据获取功能，同时测试按钮事件绑定
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void Button2_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示测试选项对话框
                var result = MessageBox.Show("选择测试类型：\n\n是(Y) - 运行数据显示诊断\n否(N) - 运行基础API测试\n取消 - 退出测试",
                    "测试选项", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Cancel)
                {
                    return;
                }
                else if (result == DialogResult.Yes)
                {
                    // 运行数据显示诊断
                    await DiagnoseDataDisplayIssues();
                    return;
                }

                // 运行基础API测试
                System.Diagnostics.Debug.WriteLine("🧪 开始基础API测试...");

                // 首先测试按钮事件绑定
                TestButtonEvents();

                // 测试表格操作列按钮
                var rowCount = gridView.RowCount;
                var columnCount = gridView.Columns.Count;

                // 检查操作列是否存在
                var actionColumn = gridView.Columns.FirstOrDefault(c => c.FieldName == "Action");
                var hasActionColumn = actionColumn != null;

                System.Diagnostics.Debug.WriteLine($"表格状态: 行数={rowCount}, 列数={columnCount}, 操作列存在={hasActionColumn}");

                // 构建测试URL（使用固定的时间范围）
                string testUrl = $"{API_BASE_URL}/api/PackagingSterilization?startDate=2024-01-01&endDate=2024-12-31";
                System.Diagnostics.Debug.WriteLine($"测试URL: {testUrl}");

                // 调用API进行测试
                string apiResult = await HttpClientHelper.ClientAsync("GET", testUrl, false, null);
                System.Diagnostics.Debug.WriteLine($"测试结果长度: {apiResult?.Length ?? 0}");

                if (!string.IsNullOrEmpty(apiResult))
                {
                    int previewLength = Math.Min(200, apiResult.Length);
                    System.Diagnostics.Debug.WriteLine($"测试结果预览: {apiResult.Substring(0, previewLength)}");
                }

                // 显示测试结果
                string testReport = $"📊 基础API测试结果:\n\n" +
                                   $"表格行数: {rowCount}\n" +
                                   $"表格列数: {columnCount}\n" +
                                   $"操作列存在: {hasActionColumn}\n" +
                                   $"API返回数据长度: {apiResult?.Length ?? 0}\n\n" +
                                   $"测试URL: {testUrl}\n\n" +
                                   $"如果有数据但显示不完整，请点击'是'运行详细诊断";

                MessageBox.Show(testReport, "API测试结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"测试异常: {ex}");
            }
        }

        /// <summary>
        /// 刷新数据方法
        /// 功能：重新加载包装数据，供外部调用
        /// </summary>
        public async Task RefreshData()
        {
            await LoadPackagingData();
        }

        /// <summary>
        /// 获取包装数据列表
        /// 功能：返回当前加载的包装数据列表，供外部访问
        /// </summary>
        /// <returns>包装数据列表</returns>
        public List<PackagingData> GetPackagingList()
        {
            return packagingList;
        }

        #endregion

        #region 已废弃的表格交互事件（保留作为参考）

        /*
        /// <summary>
        /// 表格行点击事件处理器（已废弃）
        /// 功能：原来用于处理用户点击表格行时的操作选择，现在操作按钮已独立处理
        /// 注意：此方法已被独立的按钮事件处理器替代
        /// </summary>
        private async void GridView_Click(object sender, EventArgs e)
        {
            // 此方法已废弃，操作按钮现在独立处理
            // 查看功能：直接调用 ShowPackageDetails 方法
            // 删除功能：直接调用 DeletePackage 方法
        }

        /// <summary>
        /// 显示包装详情对话框（已废弃）
        /// 功能：此方法已被 ShowPackageDetails 方法替代
        /// </summary>
        private void ShowPackageDetailsDialog(string packageCode, string packageName)
        {
            // 此方法已废弃，请使用 ShowPackageDetails 方法
        }

        /// <summary>
        /// 显示删除确认对话框（已废弃）
        /// 功能：此方法已被 DeletePackage 方法中的简化确认对话框替代
        /// </summary>
        private async Task ShowDeleteConfirmDialog(int packageId, string packageCode, string packageName)
        {
            // 此方法已废弃，DeletePackage 方法现在直接处理确认对话框
        }
        */

        /// <summary>
        /// 测试打印功能 - 简单版本
        /// </summary>
        private void TestSimplePrint()
        {
            try
            {
                PrintDocument pd = new PrintDocument();
                pd.PrintPage += (sender, e) =>
                {
                    // 绘制页面标题
                    Font titleFont = new Font("宋体", 18, FontStyle.Bold);
                    e.Graphics.DrawString("打印功能测试页", titleFont, Brushes.Black, new Point(300, 50));

                    // 绘制分隔线
                    e.Graphics.DrawLine(Pens.Black, 50, 90, 750, 90);

                    // 绘制基本图形测试
                    e.Graphics.DrawString("1. 基本图形测试:", new Font("宋体", 14), Brushes.Black, 50, 120);
                    e.Graphics.DrawRectangle(Pens.Blue, 50, 150, 200, 80);
                    e.Graphics.FillRectangle(Brushes.LightBlue, 60, 160, 50, 30);
                    e.Graphics.DrawString("图形绘制正常", new Font("宋体", 12), Brushes.Black, 270, 170);

                    // 绘制文字测试
                    e.Graphics.DrawString("2. 文字显示测试:", new Font("宋体", 14), Brushes.Black, 50, 260);
                    e.Graphics.DrawString("中文显示: 医疗消毒供应中心", new Font("宋体", 12), Brushes.Black, 50, 290);
                    e.Graphics.DrawString("英文显示: Medical Disinfection Supply Center", new Font("Arial", 12), Brushes.Black, 50, 320);
                    e.Graphics.DrawString("数字显示: **********", new Font("宋体", 12), Brushes.Black, 50, 350);

                    // 尝试生成条码
                    e.Graphics.DrawString("3. 条码生成测试:", new Font("宋体", 14), Brushes.Black, 50, 400);
                    try
                    {
                        Barcode barcode = new Barcode();
                        barcode.IncludeLabel = true;
                        SKImage skImage = barcode.Encode(BarcodeStandard.Type.Code128, "TEST123456",
                            SKColors.Black, SKColors.White, 300, 60);

                        using (var data = skImage.Encode())
                        using (var stream = data.AsStream())
                        {
                            Image img = Image.FromStream(stream);
                            e.Graphics.DrawImage(img, new Rectangle(50, 430, 300, 60));
                        }

                        e.Graphics.DrawString("✓ 条码生成成功", new Font("宋体", 12), Brushes.Green, 370, 450);
                    }
                    catch (Exception ex)
                    {
                        e.Graphics.DrawRectangle(Pens.Red, 50, 430, 300, 60);
                        e.Graphics.DrawString("✗ 条码生成失败", new Font("宋体", 12), Brushes.Red, 370, 450);
                        e.Graphics.DrawString($"错误: {ex.Message}", new Font("宋体", 10), Brushes.Red, 50, 500);
                    }

                    // 绘制测试结果
                    e.Graphics.DrawLine(Pens.Black, 50, 550, 750, 550);
                    e.Graphics.DrawString("测试结果:", new Font("宋体", 14, FontStyle.Bold), Brushes.Black, 50, 570);
                    e.Graphics.DrawString("如果您能清楚地看到以上所有内容，说明打印功能正常工作。",
                        new Font("宋体", 12), Brushes.DarkGreen, 50, 600);

                    // 绘制页脚
                    string footerText = $"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                    e.Graphics.DrawString(footerText, new Font("宋体", 9), Brushes.Gray, new Point(50, 750));
                };

                PrintDialog printDialog = new PrintDialog();
                printDialog.Document = pd;

                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    pd.Print();
                    MessageBox.Show("测试打印已发送到打印机。\n\n请检查打印结果：\n• 文字是否清晰\n• 图形是否正常\n• 条码是否生成",
                        "测试打印", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试打印失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        ///// <summary>
        ///// 测试扫描按钮点击事件处理器
        ///// 功能：测试条码扫描和解析功能
        ///// </summary>
        ///// <param name="sender">事件发送者</param>
        ///// <param name="e">事件参数</param>
        //private void button3_Click(object sender, EventArgs e)
        //{
        //    // 添加测试打印选项
        //    var result = MessageBox.Show("选择测试类型：\n\n是(Y) = 测试简单打印\n否(N) = 测试条码扫描",
        //        "测试选择", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

        //    if (result == DialogResult.Yes)
        //    {
        //        TestSimplePrint();
        //    }
        //    else if (result == DialogResult.No)
        //    {
        //        TestBarcodeScanning();
        //    }
        //}

        #endregion

        #region 条码打印功能

        /// <summary>
        /// 打印条码按钮点击事件处理器
        /// 功能：为表格中的每一行数据生成并打印条码
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void button2_Click_1(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有数据
                if (packagingList == null || packagingList.Count == 0)
                {
                    MessageBox.Show("没有数据可以打印条码，请先查询数据。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 显示确认对话框
                var result = MessageBox.Show($"将为 {packagingList.Count} 条记录打印条码，每条记录一个条码。\n\n是否继续？",
                    "确认打印", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 创建条码打印器并打印所有数据
                    BarcodePrinter printer = new BarcodePrinter();
                    printer.PrintAllBarcodes(packagingList);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印条码失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"打印条码异常: {ex}");
            }
        }

        /// <summary>
        /// 条码打印器类
        /// 功能：负责生成和打印条码，支持单个条码和批量条码打印
        /// </summary>
        public class BarcodePrinter
        {
            /// <summary>
            /// 打印单个条码
            /// </summary>
            /// <param name="text">条码内容</param>
            public void PrintBarcode(string text)
            {
                try
                {
                    PrintDocument pd = new PrintDocument();
                    pd.PrintPage += (sender, e) =>
                    {
                        try
                        {
                            Barcode barcode = new Barcode();
                            barcode.IncludeLabel = true; // 显示条码下方文字
                            SKImage skImage = barcode.Encode(BarcodeStandard.Type.Code128, text, SKColors.Black, SKColors.White, 300, 100);

                            // 将SKImage转换为System.Drawing.Image
                            using (var data = skImage.Encode())
                            using (var stream = data.AsStream())
                            {
                                Image img = Image.FromStream(stream);
                                e.Graphics.DrawImage(img, new Point(50, 50));
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"生成条码图像失败: {ex.Message}");
                        }
                    };

                    PrintDialog printDialog = new PrintDialog();
                    printDialog.Document = pd;

                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        pd.Print();
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"打印条码失败: {ex.Message}");
                }
            }

            /// <summary>
            /// 批量打印条码 - 为每行数据生成一个条码
            /// </summary>
            /// <param name="dataList">包装数据列表</param>
            public void PrintAllBarcodes(List<PackagingData> dataList)
            {
                try
                {
                    if (dataList == null || dataList.Count == 0)
                    {
                        throw new Exception("没有数据可以打印");
                    }

                    PrintDocument pd = new PrintDocument();
                    int currentIndex = 0;
                    int itemsPerPage = 4; // 每页打印4个条码

                    pd.PrintPage += (sender, e) =>
                    {
                        try
                        {
                            // 添加调试信息
                            System.Diagnostics.Debug.WriteLine($"开始打印页面，当前索引: {currentIndex}, 总数据: {dataList.Count}");

                            int yPosition = 80; // 起始Y位置，留出页面顶部空间
                            int itemHeight = 180; // 每个条码项的高度，增加间距
                            int itemsOnThisPage = 0;
                            int maxItemsPerPage = 3; // 减少每页项目数，避免重叠

                            // 绘制页面标题
                            Font titleFont = new Font("宋体", 16, FontStyle.Bold);
                            e.Graphics.DrawString("包装条码打印单", titleFont, Brushes.Black, new Point(300, 20));

                            // 绘制分隔线
                            e.Graphics.DrawLine(Pens.Black, 50, 60, 750, 60);

                            // 在当前页面上打印条码
                            while (currentIndex < dataList.Count && itemsOnThisPage < maxItemsPerPage)
                            {
                                var item = dataList[currentIndex];
                                System.Diagnostics.Debug.WriteLine($"正在处理第 {currentIndex} 项: {item.包名称}");

                                // 生成条码内容 - 使用简化格式
                                string barcodeContent = $"PKG{item.ID:D6}";
                                System.Diagnostics.Debug.WriteLine($"条码内容: {barcodeContent}");

                                try
                                {
                                    // 生成条码图像
                                    Barcode barcode = new Barcode();
                                    barcode.IncludeLabel = true;
                                    SKImage skImage = barcode.Encode(BarcodeStandard.Type.Code128, barcodeContent,
                                        SKColors.Black, SKColors.White, 350, 70);

                                    // 将SKImage转换为System.Drawing.Image并绘制条码
                                    using (var data = skImage.Encode())
                                    using (var stream = data.AsStream())
                                    {
                                        Image img = Image.FromStream(stream);
                                        e.Graphics.DrawImage(img, new Rectangle(50, yPosition, 350, 70));
                                        System.Diagnostics.Debug.WriteLine($"成功绘制条码在位置: (50, {yPosition})");
                                    }
                                }
                                catch (Exception barcodeEx)
                                {
                                    System.Diagnostics.Debug.WriteLine($"条码生成失败: {barcodeEx.Message}");
                                    // 如果条码生成失败，绘制替代文本
                                    e.Graphics.DrawRectangle(Pens.Red, 50, yPosition, 350, 70);
                                    e.Graphics.DrawString($"条码: {barcodeContent}",
                                        new Font("宋体", 12), Brushes.Black, new Point(60, yPosition + 25));
                                }

                                // 在条码右侧添加详细信息文本
                                string detailText = $"包名称: {item.包名称 ?? "未知"}\n" +
                                                  $"包条码: {item.包条码 ?? "N/A"}\n" +
                                                  $"打包人: {item.打包人 ?? "未知"}\n" +
                                                  $"检查人: {item.检查人 ?? "未知"}\n" +
                                                  $"打包时间: {item.打包时间 ?? "未知"}";

                                Font detailFont = new Font("宋体", 10);
                                e.Graphics.DrawString(detailText, detailFont, Brushes.Black,
                                    new Rectangle(420, yPosition, 300, 120));

                                // 绘制分隔线
                                e.Graphics.DrawLine(Pens.LightGray, 50, yPosition + itemHeight - 10, 750, yPosition + itemHeight - 10);

                                yPosition += itemHeight;
                                itemsOnThisPage++;
                                currentIndex++;
                            }

                            // 绘制页脚信息
                            Font footerFont = new Font("宋体", 9);
                            string footerText = $"第 {(currentIndex - 1) / maxItemsPerPage + 1} 页 | 打印时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                            e.Graphics.DrawString(footerText, footerFont, Brushes.Gray, new Point(50, 750));

                            // 如果还有更多数据，设置打印下一页
                            e.HasMorePages = currentIndex < dataList.Count;
                            System.Diagnostics.Debug.WriteLine($"页面完成，是否有更多页面: {e.HasMorePages}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"打印页面失败: {ex.Message}");
                            // 在打印页面上显示错误信息
                            e.Graphics.DrawString($"打印错误: {ex.Message}",
                                new Font("宋体", 12), Brushes.Red, new Point(50, 100));
                        }
                    };

                    // 显示打印对话框
                    PrintDialog printDialog = new PrintDialog();
                    printDialog.Document = pd;

                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        pd.Print();
                        MessageBox.Show($"成功打印 {dataList.Count} 个条码", "打印完成",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"批量打印条码失败: {ex.Message}");
                }
            }

            /// <summary>
            /// 生成条码内容
            /// 功能：将包装数据转换为条码字符串，便于扫描时获取完整信息
            /// </summary>
            /// <param name="data">包装数据</param>
            /// <returns>条码内容字符串</returns>
            private string GenerateBarcodeContent(PackagingData data)
            {
                try
                {
                    // 添加调试信息
                    System.Diagnostics.Debug.WriteLine($"开始生成条码内容，数据: ID={data.ID}, 包名称={data.包名称}");

                    // 首先尝试使用简单格式，避免JSON序列化问题
                    string simpleContent = $"{data.包条码 ?? "N/A"}|{data.包名称 ?? "N/A"}|{data.打包人 ?? "N/A"}|{data.检查人 ?? "N/A"}|{data.打包时间 ?? "N/A"}";

                    // 确保条码内容不为空且不超过合理长度
                    if (string.IsNullOrWhiteSpace(simpleContent) || simpleContent.Length < 5)
                    {
                        // 如果数据不完整，使用ID作为备用
                        simpleContent = $"PKG{data.ID:D6}|{DateTime.Now:yyyyMMdd}";
                    }

                    // 限制长度，避免条码过长
                    if (simpleContent.Length > 80)
                    {
                        simpleContent = simpleContent.Substring(0, 80);
                    }

                    System.Diagnostics.Debug.WriteLine($"生成的条码内容: {simpleContent}");
                    return simpleContent;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"生成条码内容失败: {ex.Message}");
                    // 如果所有方法都失败，使用最基本的格式
                    string fallbackContent = $"PKG{data.ID:D6}";
                    System.Diagnostics.Debug.WriteLine($"使用备用条码内容: {fallbackContent}");
                    return fallbackContent;
                }
            }
        }

        /// <summary>
        /// 扫描条码获取信息的辅助方法
        /// 功能：解析条码内容，还原包装信息
        /// </summary>
        /// <param name="barcodeContent">扫描到的条码内容</param>
        /// <returns>解析后的包装数据，如果解析失败返回null</returns>
        public static PackagingData ParseBarcodeContent(string barcodeContent)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(barcodeContent))
                    return null;

                // 尝试解析JSON格式
                if (barcodeContent.StartsWith("{") && barcodeContent.EndsWith("}"))
                {
                    var data = JsonConvert.DeserializeObject<PackagingData>(barcodeContent);
                    return data;
                }

                // 尝试解析分隔符格式
                if (barcodeContent.Contains("|"))
                {
                    var parts = barcodeContent.Split('|');
                    if (parts.Length >= 5)
                    {
                        return new PackagingData
                        {
                            包条码 = parts[0],
                            包名称 = parts[1],
                            打包人 = parts[2],
                            检查人 = parts[3],
                            打包时间 = parts[4]
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析条码内容失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 条码扫描功能示例

        /// <summary>
        /// 条码扫描处理示例方法
        /// 功能：演示如何处理扫描到的条码并获取包装信息
        /// 使用方法：当扫描设备扫描到条码时，调用此方法处理条码内容
        /// </summary>
        /// <param name="scannedBarcode">扫描到的条码内容</param>
        public void HandleScannedBarcode(string scannedBarcode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"处理扫描条码: {scannedBarcode}");

                // 解析条码内容
                var packageData = ParseBarcodeContent(scannedBarcode);

                if (packageData != null)
                {
                    // 成功解析条码，显示包装信息
                    string message = $"扫描成功！获取到包装信息：\n\n" +
                                   $"包条码: {packageData.包条码}\n" +
                                   $"包名称: {packageData.包名称}\n" +
                                   $"打包人: {packageData.打包人}\n" +
                                   $"检查人: {packageData.检查人}\n" +
                                   $"打包时间: {packageData.打包时间}";

                    MessageBox.Show(message, "条码扫描结果",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 可以在这里添加更多业务逻辑，比如：
                    // 1. 将扫描到的信息保存到数据库
                    // 2. 更新包装状态
                    // 3. 触发其他业务流程
                    // 4. 在表格中高亮显示对应的行
                    HighlightScannedItem(packageData);
                }
                else
                {
                    MessageBox.Show($"无法解析条码内容：{scannedBarcode}\n\n请确认条码格式是否正确。",
                        "条码解析失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理扫描条码时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"处理扫描条码异常: {ex}");
            }
        }

        /// <summary>
        /// 在表格中高亮显示扫描到的包装项
        /// </summary>
        /// <param name="scannedData">扫描到的包装数据</param>
        private void HighlightScannedItem(PackagingData scannedData)
        {
            try
            {
                // 在当前显示的数据中查找匹配项
                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var rowBarcode = gridView.GetRowCellValue(i, "包条码")?.ToString();
                    var rowName = gridView.GetRowCellValue(i, "包名称")?.ToString();

                    // 根据条码或名称匹配
                    if ((!string.IsNullOrEmpty(rowBarcode) && rowBarcode == scannedData.包条码) ||
                        (!string.IsNullOrEmpty(rowName) && rowName == scannedData.包名称))
                    {
                        // 选中并聚焦到匹配的行
                        gridView.FocusedRowHandle = i;
                        gridView.SelectRow(i);

                        System.Diagnostics.Debug.WriteLine($"在表格中找到并高亮显示了扫描项: 行{i}");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示扫描项失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试条码扫描功能的方法
        /// 功能：用于测试条码扫描和解析功能
        /// </summary>
        public void TestBarcodeScanning()
        {
            try
            {
                // 模拟扫描一个条码
                if (packagingList != null && packagingList.Count > 0)
                {
                    var testData = packagingList[0];
                    var printer = new BarcodePrinter();

                    // 生成测试条码内容
                    string testBarcodeContent;
                    try
                    {
                        var barcodeData = new
                        {
                            ID = testData.ID,
                            包条码 = testData.包条码,
                            包名称 = testData.包名称,
                            打包人 = testData.打包人,
                            检查人 = testData.检查人,
                            打包时间 = testData.打包时间
                        };
                        testBarcodeContent = JsonConvert.SerializeObject(barcodeData, Formatting.None);
                    }
                    catch
                    {
                        testBarcodeContent = $"{testData.包条码}|{testData.包名称}|{testData.打包人}|{testData.检查人}|{testData.打包时间}";
                    }

                    MessageBox.Show($"将测试扫描以下条码内容：\n\n{testBarcodeContent}",
                        "测试条码扫描", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 模拟扫描处理
                    HandleScannedBarcode(testBarcodeContent);
                }
                else
                {
                    MessageBox.Show("没有数据可以测试，请先查询数据。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试条码扫描失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}

/*
 * 文件功能总结：
 *
 * 这个PackagingManagement类是医疗器械消毒供应中心系统中的包装管理模块，主要功能包括：
 *
 * 1. 数据管理功能：
 *    - 从API加载包装数据列表
 *    - 支持按时间范围查询包装记录
 *    - 实时显示包装记录总数
 *
 * 2. 用户界面功能：
 *    - 使用DevExpress表格控件显示包装数据
 *    - 支持查看包装详情
 *    - 支持删除包装记录（带确认对话框）
 *    - 提供包装注册入口
 *
 * 3. API交互功能：
 *    - 支持多种API参数格式以适应不同后端
 *    - 灵活的JSON数据解析，支持多种响应格式
 *    - 完善的错误处理和调试信息输出
 *
 * 4. 用户体验功能：
 *    - 自定义删除确认对话框
 *    - 操作结果提示
 *    - API测试功能
 *
 * 技术特点：
 * - 使用异步编程模式提高响应性能
 * - 支持多种JSON字段名映射，提高API兼容性
 * - 完善的异常处理机制
 * - 模块化的代码结构，便于维护和扩展
 */

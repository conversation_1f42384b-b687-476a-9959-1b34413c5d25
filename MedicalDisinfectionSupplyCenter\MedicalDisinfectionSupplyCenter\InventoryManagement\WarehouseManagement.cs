// WarehouseManagement 用户控件，用于仓库管理
// 包含控件初始化和相关逻辑
using DevExpress.XtraEditors;
using MedicalDisinfectionSupplyCenter.DepartmentManagement;
using Newtonsoft.Json;
using MedicalDisinfectionSupplyCenter;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    // API响应模型类
    public class Apiresponse
    {
        public string code { get; set; }
        public string msg { get; set; }
        public Newtonsoft.Json.Linq.JToken data { get; set; }
    }
    // 仓库管理控件
    public partial class WarehouseManagement : UserControl
    {
        private int _pageIndex = 1;
        private int _pageSize = 10;
        private int _totalPage = 1;
        private int _totalCount = 0;
        // 构造函数，初始化控件
        public WarehouseManagement()
        {
            InitializeComponent(); // 初始化界面组件
            this.Load += WarehouseManagement_Load;
            this.gridView1.CustomDrawCell += gridView1_CustomDrawCell;
            this.gridView1.CustomRowCellEdit += (s, e) =>
            {
                if (e.Column.FieldName == "action")
                {
                    var view = (DevExpress.XtraGrid.Views.Grid.GridView)s;
                    var row = view.GetDataRow(e.RowHandle);
                    if (row == null) return;
                    string auditState = row["auditState"]?.ToString();
                    var btn = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
                    btn.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
                    btn.Buttons.Clear();
                    btn.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "查看" });
                    if (auditState == "已审批")
                    {
                        btn.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "撤销" });
                        btn.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "入库" });
                    }
                    else
                    {
                        btn.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "审批" });
                    }
                    // btn.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "编辑" });
                    btn.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "删除" });
                    btn.ButtonClick += OnActionButtonClick;
                    e.RepositoryItem = btn;
                }
            };
            // 日期控件默认值为空
            this.dateTimePickerStart.Format = DateTimePickerFormat.Custom;
            this.dateTimePickerStart.CustomFormat = " ";
            this.dateTimePickerStart.ValueChanged += (s, e) =>
            {
                this.dateTimePickerStart.Format = DateTimePickerFormat.Custom;
                this.dateTimePickerStart.CustomFormat = "yyyy-MM-dd";
            };
            this.dateTimePickerEnd.Format = DateTimePickerFormat.Custom;
            this.dateTimePickerEnd.CustomFormat = " ";
            this.dateTimePickerEnd.ValueChanged += (s, e) =>
            {
                this.dateTimePickerEnd.Format = DateTimePickerFormat.Custom;
                this.dateTimePickerEnd.CustomFormat = "yyyy-MM-dd";
            };
            // 查询按钮
            var btnSearch = new Button();
            btnSearch.Text = "查询";
            btnSearch.Location = new System.Drawing.Point(800, 15); // 向后移动，避免遮挡
            btnSearch.Size = new System.Drawing.Size(80, 28);
            btnSearch.Click += async (s, e) => { _pageIndex = 1; await BindGrid(); };
            this.Controls.Add(btnSearch);
            // 新增按钮
            var btnAdd = new Button();
            btnAdd.Text = "新增";
            btnAdd.Location = new System.Drawing.Point(890, 15); // 紧挨查询按钮
            btnAdd.Size = new System.Drawing.Size(80, 28);
            btnAdd.Click += (s, e) =>
            {
                // 简单弹窗选择类型
                var dlg = new Form();
                dlg.Text = "入库种类";
                dlg.FormBorderStyle = FormBorderStyle.FixedDialog;
                dlg.StartPosition = FormStartPosition.CenterParent;
                dlg.Size = new System.Drawing.Size(400, 250);
                var label = new Label() { Text = "请选择本次入库种类:", Left = 30, Top = 30, Width = 300, Font = new System.Drawing.Font("微软雅黑", 12) };
                var btnPack = new Button() { Text = "包入库", Left = 40, Top = 100, Width = 130, Height = 80, BackColor = System.Drawing.Color.SkyBlue, ForeColor = System.Drawing.Color.White, Font = new System.Drawing.Font("微软雅黑", 14, System.Drawing.FontStyle.Bold) };
                var btnDevice = new Button() { Text = "器械入库", Left = 210, Top = 100, Width = 130, Height = 80, BackColor = System.Drawing.Color.SteelBlue, ForeColor = System.Drawing.Color.White, Font = new System.Drawing.Font("微软雅黑", 14, System.Drawing.FontStyle.Bold) };
                btnPack.Click += (s1, e1) =>
                {
                    dlg.DialogResult = DialogResult.OK;
                    dlg.Close();
                    ShowPackStorageDialog();
                };
                btnDevice.Click += (s2, e2) =>
                {
                    dlg.DialogResult = DialogResult.OK;
                    dlg.Close();
                    OnAddStorage(0); // 0=器械入库
                };
                dlg.Controls.Add(label);
                dlg.Controls.Add(btnPack);
                dlg.Controls.Add(btnDevice);
                dlg.ShowDialog();
            };
            this.Controls.Add(btnAdd);
            // 分页控件事件
            this.btnPrevPage.Click += async (s, e) => { if (_pageIndex > 1) { _pageIndex--; await BindGrid(); } };
            this.btnNextPage.Click += async (s, e) => { if (_pageIndex < _totalPage) { _pageIndex++; await BindGrid(); } };
            this.comboBoxPageSize.SelectedIndexChanged += async (s, e) =>
            {
                if (int.TryParse(comboBoxPageSize.SelectedItem?.ToString(), out int size))
                {
                    _pageSize = size;
                    _pageIndex = 1;
                    await BindGrid();
                }
            };
            this.comboBoxPageSize.SelectedIndex = 0;
        }

        private async void WarehouseManagement_Load(object sender, EventArgs e)
        {
            InitGridView();
            await BindGrid();
        }

        private void InitGridView()
        {
            this.gridView1.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(221, 235, 247);
            this.gridView1.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.gridView1.Appearance.HeaderPanel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.gridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.Columns.Clear();
            var colStorageCode = this.gridView1.Columns.AddVisible("storageCode", "入库编码");
            var colTypeName = this.gridView1.Columns.AddVisible("storageTypeName", "物品类型");
            var colWarehouse = this.gridView1.Columns.AddVisible("warehouseName", "入库库房");
            var colSupplier = this.gridView1.Columns.AddVisible("supplierName", "供应商");
            var colExplain = this.gridView1.Columns.AddVisible("storageExplain", "说明");
            var colAuditState = this.gridView1.Columns.AddVisible("auditState", "状态");
            var colAuditName = this.gridView1.Columns.AddVisible("auditName", "审核人");
            var colAuditDate = this.gridView1.Columns.AddVisible("auditDate", "审核时间");
            colAuditDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colAuditDate.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            // 设置列宽
            colStorageCode.Width = 180;
            colTypeName.Width = 120;
            colWarehouse.Width = 140;
            colSupplier.Width = 140;
            colExplain.Width = 200;
            colAuditState.Width = 80;
            colAuditName.Width = 80;
            colAuditDate.Width = 160;
            // 操作列
            var colAction = this.gridView1.Columns.AddVisible("action", "操作");
            colAction.Width = 400;
            var btnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            btnEdit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
            btnEdit.Buttons.Clear();
            // 先添加所有按钮，后续在CustomRowCellEdit事件中动态控制显示
            btnEdit.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "查看" });
            btnEdit.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "审批" });
            btnEdit.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "撤销" });
            btnEdit.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "编辑" });
            btnEdit.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "删除" });
            this.gridControl1.RepositoryItems.Add(btnEdit);
            colAction.ColumnEdit = btnEdit;
            colAction.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            // 1. 定义事件处理方法
            btnEdit.ButtonClick += OnActionButtonClick;
        }

        private async Task BindGrid()
        {
            try
            {
                string url = ReadApiConfig.GetApiUrl("Wms", "QueryEquipment");
                var paramList = new List<string>();
                // 判断日期控件是否有值
                if (this.dateTimePickerStart.CustomFormat != " " && this.dateTimePickerStart.Value != DateTimePicker.MinimumDateTime)
                {
                    paramList.Add($"StartTime={Uri.EscapeDataString(this.dateTimePickerStart.Value.ToString("yyyy-MM-dd"))}");
                }
                if (this.dateTimePickerEnd.CustomFormat != " " && this.dateTimePickerEnd.Value != DateTimePicker.MinimumDateTime)
                {
                    paramList.Add($"EndTime={Uri.EscapeDataString(this.dateTimePickerEnd.Value.ToString("yyyy-MM-dd"))}");
                }
                // 入库编码
                if (!string.IsNullOrWhiteSpace(this.textBoxStorageCode.Text))
                {
                    paramList.Add($"StorageCode={Uri.EscapeDataString(this.textBoxStorageCode.Text.Trim())}");
                }
                // 分页参数
                paramList.Add($"PageIndex={_pageIndex}");
                paramList.Add($"PageSize={_pageSize}");
                if (paramList.Count > 0)
                {
                    url += (url.Contains("?") ? "&" : "?") + string.Join("&", paramList);
                }
                var client = new System.Net.Http.HttpClient();
                var response = await client.GetStringAsync(url);
                if (string.IsNullOrWhiteSpace(response) || !response.TrimStart().StartsWith("{"))
                {
                    MessageBox.Show("接口返回内容不是有效的JSON: " + response);
                    gridControl1.DataSource = null;
                    return;
                }
                var jobj = Newtonsoft.Json.Linq.JObject.Parse(response);
                if (jobj["code"]?.ToObject<int>() == 200)
                {
                    var pageData = jobj["data"]?["pageData"]?.ToObject<List<Newtonsoft.Json.Linq.JObject>>();
                    _totalCount = jobj["data"]?["totalCount"]?.ToObject<int>() ?? 0;
                    _totalPage = (_totalCount + _pageSize - 1) / _pageSize;
                    if (_totalPage == 0) _totalPage = 1;
                    lblPageInfo.Text = $"第{_pageIndex}页/共{_totalPage}页";
                    if (pageData != null)
                    {
                        var table = new System.Data.DataTable();
                        table.Columns.Add("id", typeof(string));
                        table.Columns.Add("storageCode", typeof(string));
                        table.Columns.Add("storageTypeName", typeof(string));
                        table.Columns.Add("warehouseName", typeof(string));
                        table.Columns.Add("supplierName", typeof(string));
                        table.Columns.Add("storageExplain", typeof(string));
                        table.Columns.Add("auditState", typeof(string));
                        table.Columns.Add("auditName", typeof(string));
                        table.Columns.Add("auditDate", typeof(string));
                        table.Columns.Add("action", typeof(string)); // 新增操作列
                        foreach (var item in pageData)
                        {
                            string auditStateText = "";
                            if (item["auditState"] != null && bool.TryParse(item["auditState"].ToString(), out bool state))
                            {
                                auditStateText = state ? "已审批" : "待审批";
                            }
                            table.Rows.Add(
                                item["id"]?.ToString() ?? "",
                                item["storageCode"]?.ToString() ?? "",
                                item["storageTypeName"]?.ToString() ?? "",
                                item["warehouseName"]?.ToString() ?? "",
                                item["supplierName"]?.ToString() ?? "",
                                item["storageExplain"]?.ToString() ?? "",
                                auditStateText,
                                item["auditName"]?.ToString() ?? "",
                                item["auditDate"]?.ToString() ?? "",
                                "操作" // 新增操作列内容
                            );
                        }
                        gridControl1.DataSource = table;
                    }
                    else
                    {
                        gridControl1.DataSource = null;
                    }
                }
                else
                {
                    MessageBox.Show("接口返回错误: " + (jobj["msg"]?.ToString() ?? "未知错误"));
                    gridControl1.DataSource = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("获取入库数据失败: " + ex.Message);
                gridControl1.DataSource = null;
            }
        }

        private void gridView1_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "auditState")
            {
                var value = e.DisplayText;
                if (value == "已审批")
                {
                    e.Appearance.ForeColor = System.Drawing.Color.Green;
                    e.Appearance.Font = new System.Drawing.Font(e.Appearance.Font, System.Drawing.FontStyle.Bold);
                }
                else if (value == "待审批")
                {
                    e.Appearance.ForeColor = System.Drawing.Color.FromArgb(0, 112, 192); // 蓝色
                    e.Appearance.Font = new System.Drawing.Font(e.Appearance.Font, System.Drawing.FontStyle.Bold);
                }
            }
        }

        // 1. 定义事件处理方法
        private async void OnActionButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            var view = this.gridView1;
            var row = view.GetDataRow(view.FocusedRowHandle);
            if (row == null) return;
            string storageCode = row["storageCode"]?.ToString();
            string auditState = row["auditState"]?.ToString();
            string id = row.Table.Columns.Contains("id") ? row["id"]?.ToString() : null;
            switch (e.Button.Caption)
            {
                case "查看":
                    // 调用API获取入库记录详情
                    try
                    {
                        if (string.IsNullOrEmpty(id))
                        {
                            MessageBox.Show("无法获取记录ID", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        // 调用API获取详细信息
                        string apiUrl = ReadApiConfig.GetApiUrl("Wms", "GetStorageById") + $"?id={id}";
                        using (var client = new System.Net.Http.HttpClient())
                        {
                            client.Timeout = TimeSpan.FromSeconds(30);
                            var response = await client.GetStringAsync(apiUrl);

                            if (!string.IsNullOrEmpty(response))
                            {
                                var jobj = Newtonsoft.Json.Linq.JObject.Parse(response);
                                if (jobj["code"]?.ToObject<int>() == 200)
                                {
                                    var data = jobj["data"];
                                    if (data != null)
                                    {
                                        var detailMessage = new System.Text.StringBuilder();
                                        detailMessage.AppendLine($"入库单号：{data["storageCode"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"入库类型：{data["storageTypeName"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"库房：{data["warehouseName"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"供应商：{data["supplierName"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"说明：{data["storageExplain"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"审核状态：{(data["auditState"]?.ToObject<bool>() == true ? "已审批" : "待审批")}");
                                        detailMessage.AppendLine($"审核人：{data["auditName"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"审核时间：{data["auditDate"]?.ToString() ?? ""}");
                                        detailMessage.AppendLine($"创建时间：{data["createTime"]?.ToString() ?? ""}");

                                        // 添加物品详情
                                        var details = data["details"];
                                        if (details != null && details.HasValues)
                                        {
                                            detailMessage.AppendLine();
                                            detailMessage.AppendLine("=== 物品详情 ===");
                                            int itemIndex = 1;
                                            foreach (var item in details)
                                            {
                                                detailMessage.AppendLine($"物品 {itemIndex}：");
                                                detailMessage.AppendLine($"  物品编码：{item["materialCode"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  物品名称：{item["materialName"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  物品类型：{item["materialTypeName"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  数量：{item["storageNum"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  规格：{item["materialSpecs"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  价格：{item["materialPrice"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  注册号：{item["registerNo"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  生产日期：{item["produceDate"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  灭菌日期：{item["sterilizaDate"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine($"  过期日期：{item["lapseDate"]?.ToString() ?? ""}");
                                                detailMessage.AppendLine();
                                                itemIndex++;
                                            }
                                        }

                                        ShowDetailDialog(data);
                                    }
                                    else
                                    {
                                        MessageBox.Show("未找到记录详情", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                    }
                                }
                                else
                                {
                                    MessageBox.Show($"查询失败：{jobj["msg"]?.ToString() ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                MessageBox.Show("API响应为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"获取详情失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    break;
                case "审批":
                    try
                    {
                        // 获取当前登录人姓名
                        string currentUserName = "";
                        var parentForm = this.FindForm() as MedicalDisinfectionSupplyCenter.FluentDesignForm1;
                        if (parentForm != null)
                        {
                            var userNameField = parentForm.GetType().GetField("_userName", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                            if (userNameField != null)
                            {
                                currentUserName = userNameField.GetValue(parentForm)?.ToString() ?? "";
                            }
                        }
                        var auditData = new { id = id, auditState = true, auditName = currentUserName };
                        string apiUrl = MedicalDisinfectionSupplyCenter.WriteApiConfig.GetApiUrl("Wms", "AuditStorageState");
                        string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(auditData);
                        var httpContent = new System.Net.Http.StringContent(postJson, System.Text.Encoding.UTF8, "application/json");
                        string result = await WinFormsAppDemo2.Common.HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                        if (!string.IsNullOrEmpty(result))
                        {
                            var apiResponse = JsonConvert.DeserializeObject<Apiresponse>(result);
                            if (apiResponse != null && apiResponse.code == "Success")
                            {
                                XtraMessageBox.Show(apiResponse.msg ?? "审批成功！", "操作提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                await BindGrid();
                            }
                            else
                            {
                                XtraMessageBox.Show(apiResponse?.msg ?? "审批失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show("审批异常：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    break;
                case "撤销":
                    // 调用撤销接口
                    try
                    {
                        var undoData = new { id = id, auditState = false };
                        string apiUrl = MedicalDisinfectionSupplyCenter.WriteApiConfig.GetApiUrl("Wms", "UndoStorageState");
                        string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(undoData);
                        var httpContent = new System.Net.Http.StringContent(postJson, System.Text.Encoding.UTF8, "application/json");
                        string result = await WinFormsAppDemo2.Common.HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                        if (!string.IsNullOrEmpty(result))
                        {


                            var apiResponse = JsonConvert.DeserializeObject<Apiresponse>(result);
                            if (apiResponse != null && apiResponse.code == "Success")
                            {
                                XtraMessageBox.Show(apiResponse.msg ?? "撤销成功！", "操作提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                await BindGrid();
                            }
                            else
                            {
                                XtraMessageBox.Show(apiResponse?.msg ?? "撤销失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("撤销异常：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    break;
                case "入库":
                    // 调用入库接口
                    try
                    {
                        // 获取选中行的物品ID
                        string materialId = row["id"]?.ToString();
                        if (string.IsNullOrEmpty(materialId))
                        {
                            XtraMessageBox.Show("无法获取物品ID", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        var inboundData = new { id = materialId };
                        string apiUrl = MedicalDisinfectionSupplyCenter.WriteApiConfig.GetApiUrl("Wms", "BatchAddStoresFromStorage");
                        string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(inboundData);
                        var httpContent = new System.Net.Http.StringContent(postJson, System.Text.Encoding.UTF8, "application/json");
                        string result = await WinFormsAppDemo2.Common.HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                        if (!string.IsNullOrEmpty(result))
                        {
                            var apiResponse = JsonConvert.DeserializeObject<Apiresponse>(result);
                            if (apiResponse != null && apiResponse.code == "Success")
                            {
                                XtraMessageBox.Show(apiResponse.msg ?? "入库成功！", "操作提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                await BindGrid();
                            }
                            else
                            {
                                XtraMessageBox.Show(apiResponse?.msg ?? "入库失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show("入库异常：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    break;
                // case "编辑":
                //     MessageBox.Show($"编辑：{storageCode}");
                //     break;
                case "删除":
                    if (XtraMessageBox.Show("确定要删除该记录吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        try
                        {
                            var delData = new { id = id };
                            string apiUrl = MedicalDisinfectionSupplyCenter.WriteApiConfig.GetApiUrl("Wms", "DelBatchStorage");
                            string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(delData);
                            var httpContent = new System.Net.Http.StringContent(postJson, System.Text.Encoding.UTF8, "application/json");
                            string result = await WinFormsAppDemo2.Common.HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                            if (!string.IsNullOrEmpty(result))
                            {
                                var apiResponse = JsonConvert.DeserializeObject<Apiresponse>(result);
                                if (apiResponse != null && apiResponse.code == "Success")
                                {
                                    XtraMessageBox.Show(apiResponse.msg ?? "删除成功！", "操作提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    await BindGrid();
                                }
                                else
                                {
                                    XtraMessageBox.Show(apiResponse?.msg ?? "删除失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show("删除异常：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    break;
            }
        }

        // 2. 初始化时绑定
        // btnEdit.ButtonClick += OnActionButtonClick; // This line is now redundant as the event is handled in InitGridView

        // 3. CustomRowCellEdit中也绑定
        // btn.ButtonClick += OnActionButtonClick; // This line is now redundant as the event is handled in InitGridView

        // 新增入库类型处理方法
        private void OnAddStorage(int type)
        {
            // 根据type弹出对应新增界面或逻辑
            // type==5: 包入库，type==0: 器械入库
            if (type == 0)
            {
                ShowDeviceStorageDialog(); // 器械入库
            }
            else
            {
                ShowPackStorageDialog(); // 包入库
            }
        }

        // 包入库弹窗
        private void ShowPackStorageDialog()
        {
            var form = new Form();
            form.Text = "包入库";
            form.FormBorderStyle = FormBorderStyle.Sizable;
            form.StartPosition = FormStartPosition.CenterParent;
            form.Size = new System.Drawing.Size(700, 520);
            form.MaximizeBox = false;
            form.MinimizeBox = false;

            // 使用TableLayoutPanel优化布局
            var table = new TableLayoutPanel();
            table.Dock = DockStyle.Fill;
            table.ColumnCount = 6;
            table.RowCount = 3;
            // 上半部分控件
            var lblWarehouse = new Label() { Text = "库房：", Font = new System.Drawing.Font("微软雅黑", 10) };
            var cmbWarehouse = new System.Windows.Forms.ComboBox() { Font = new System.Drawing.Font("微软雅黑", 10) };
            cmbWarehouse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbWarehouse.Items.AddRange(new object[] { "消毒供应室", "手术供应室", "清洁供应室" });
            cmbWarehouse.SelectedIndex = 0;

            var lblSupplier = new Label() { Text = "供应商：", Font = new System.Drawing.Font("微软雅黑", 10) };
            var cmbSupplier = new System.Windows.Forms.ComboBox() { Font = new System.Drawing.Font("微软雅黑", 10) };
            cmbSupplier.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbSupplier.Items.AddRange(new object[] { "富士康", "博世", "宝洁" });
            cmbSupplier.SelectedIndex = 0;

            var lblExplain = new Label() { Text = "说明：", Font = new System.Drawing.Font("微软雅黑", 10) };
            var txtExplain = new TextBox() { Font = new System.Drawing.Font("微软雅黑", 10), Multiline = true, Height = 60, Width = 400 };

            // 调整列宽，增加间距
            table.ColumnStyles.Clear();
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 标签加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200)); // 下拉框加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 30)); // 间距
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 标签加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200)); // 下拉框加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 剩余空间
            table.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            table.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            table.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // 已选物品表格
            var dgv = new DataGridView()
            {
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font("微软雅黑", 10),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = false,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };

            // 初始化已选物品表格列
            dgv.Columns.Add("ItemId", "物品ID"); // 添加物品ID列（隐藏）
            dgv.Columns.Add("ItemName", "物品名称");
            dgv.Columns.Add("ItemCode", "物品编码");
            dgv.Columns.Add("ItemType", "物品类型");
            dgv.Columns.Add("Quantity", "数量");

            // 添加操作列，用于显示删除按钮
            var actionColumn = new DataGridViewButtonColumn()
            {
                Name = "Action",
                HeaderText = "操作",
                Text = "删除",
                UseColumnTextForButtonValue = true,
                Width = 80
            };
            dgv.Columns.Add(actionColumn);

            // 选择物品按钮
            var btnSelectItems = new Button()
            {
                Text = "选择物品",
                Font = new System.Drawing.Font("微软雅黑", 10),
                Width = 100,
                Height = 30,
                BackColor = System.Drawing.Color.LightBlue,
                ForeColor = System.Drawing.Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // 为选择物品按钮添加点击事件
            btnSelectItems.Click += (s, e) =>
            {
                ShowItemSelectionDialog("pack", dgv); // 包入库类型，传递目标表格
            };

            // 添加价格列
            dgv.Columns.Add("Price", "价格");

            // 隐藏物品ID列
            dgv.Columns["ItemId"].Visible = false;

            // 设置数量列为可编辑
            dgv.Columns["Quantity"].ReadOnly = false;

            // 添加数量验证事件
            dgv.CellValidating += (s, e) =>
            {
                if (e.ColumnIndex == dgv.Columns["Quantity"].Index)
                {
                    if (int.TryParse(e.FormattedValue.ToString(), out int quantity))
                    {
                        if (quantity < 1)
                        {
                            e.Cancel = true;
                            MessageBox.Show("数量不能小于1", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    else
                    {
                        e.Cancel = true;
                        MessageBox.Show("请输入有效的数字", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            };

            // 第一行：库房、供应商
            table.Controls.Add(lblWarehouse, 0, 0);
            table.Controls.Add(cmbWarehouse, 1, 0);
            table.Controls.Add(new Label(), 2, 0); // 间隔
            table.Controls.Add(lblSupplier, 3, 0);
            table.Controls.Add(cmbSupplier, 4, 0);
            // 第二行：说明、选择物品按钮
            table.Controls.Add(lblExplain, 0, 1);
            table.SetColumnSpan(lblExplain, 1);
            table.Controls.Add(txtExplain, 1, 1);
            table.SetColumnSpan(txtExplain, 3);
            table.Controls.Add(btnSelectItems, 4, 1);
            // 第三行：表格
            table.Controls.Add(dgv, 0, 2);
            table.SetColumnSpan(dgv, 6);

            // 第四行：入库按钮
            var btnInbound = new Button()
            {
                Text = "入库",
                Font = new System.Drawing.Font("微软雅黑", 12, FontStyle.Bold),
                Width = 120,
                Height = 40,
                BackColor = System.Drawing.Color.Green,
                ForeColor = System.Drawing.Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnInbound.Click += async (s, e) =>
            {
                if (dgv.Rows.Count == 0)
                {
                    MessageBox.Show("请先选择要入库的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                bool hasInvalidQuantity = false;
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    if (row.Cells["Quantity"].Value == null || !int.TryParse(row.Cells["Quantity"].Value.ToString(), out int quantity) || quantity < 1)
                    {
                        hasInvalidQuantity = true;
                        break;
                    }
                }
                if (hasInvalidQuantity)
                {
                    MessageBox.Show("请确保所有物品的数量都大于0", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                var inboundData = new
                {
                    Warehouse = cmbWarehouse.SelectedItem?.ToString(),
                    Supplier = cmbSupplier.SelectedItem?.ToString(),
                    Description = txtExplain.Text.Trim(),
                    Items = new List<object>()
                };
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    inboundData.Items.Add(new
                    {
                        ItemName = row.Cells["ItemName"].Value?.ToString(),
                        ItemCode = row.Cells["ItemCode"].Value?.ToString(),
                        ItemType = row.Cells["ItemType"].Value?.ToString(),
                        Quantity = int.Parse(row.Cells["Quantity"].Value?.ToString() ?? "1"),
                        Price = row.Cells["Price"].Value?.ToString()
                    });
                }
                string confirmMessage = $"确认入库以下物品？\n\n库房：{inboundData.Warehouse}\n供应商：{inboundData.Supplier}\n物品数量：{inboundData.Items.Count}项\n\n说明：{inboundData.Description}";
                if (MessageBox.Show(confirmMessage, "确认入库", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // 调用入库API
                    try
                    {
                        var requestData = new
                        {
                            storageType = 5, // 包入库类型为5
                            warehouse = cmbWarehouse.SelectedItem?.ToString(),
                            supplier = cmbSupplier.SelectedItem?.ToString(),
                            storageExplain = txtExplain.Text.Trim(),
                            storageDetails = new List<object>()
                        };

                        foreach (DataGridViewRow row in dgv.Rows)
                        {
                            requestData.storageDetails.Add(new
                            {
                                materialId = int.Parse(row.Cells["ItemId"].Value?.ToString() ?? "0"), // 使用保存的物品ID
                                materialType = 5, // 包入库类型为5
                                materialCode = row.Cells["ItemCode"].Value?.ToString(),
                                materialName = row.Cells["ItemName"].Value?.ToString(),
                                storageNum = int.Parse(row.Cells["Quantity"].Value?.ToString() ?? "1"),
                                materialSpecs = row.Cells["ItemType"].Value?.ToString(),
                                materialPrice = row.Cells["Price"].Value?.ToString(),
                                registerNo = "",
                                produceDate = "",
                                sterilizaDate = "",
                                lapseDate = ""
                            });
                        }

                        // 调用API
                        var apiUrl = WriteApiConfig.GetApiUrl("Wms", "AddWmsStorage");
                        //string apiUrl2 = WriteApiConfig.GetApiUrl("Wms", "DelBatchStorage");
                        var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);
                        var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
                        /*
                         string apiUrl = MedicalDisinfectionSupplyCenter.WriteApiConfig.GetApiUrl("Wms", "DelBatchStorage");
                         string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(delData);
                         var httpContent = new System.Net.Http.StringContent(postJson, System.Text.Encoding.UTF8, "application/json");
                         string result = await WinFormsAppDemo2.Common.HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                         */


                        using (var client = new System.Net.Http.HttpClient())
                        {
                            client.Timeout = TimeSpan.FromSeconds(30);
                            var response = await client.PostAsync(apiUrl, content);
                            var responseContent = await response.Content.ReadAsStringAsync();

                            if (response.IsSuccessStatusCode)
                            {
                                //var result = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(responseContent);
                                var apiResponse = JsonConvert.DeserializeObject<Apiresponse>(responseContent);
                                if (apiResponse != null && apiResponse.code == "Success")
                                {
                                    MessageBox.Show("入库操作成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    form.Close();
                                    // 刷新页面数据
                                    await BindGrid();
                                }
                                else
                                {
                                    MessageBox.Show($"入库失败：{apiResponse.msg}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                MessageBox.Show($"API调用失败：{response.StatusCode}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"入库操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            };
            btnInbound.Anchor = AnchorStyles.Right;
            table.Controls.Add(btnInbound, 5, 3);

            // 处理删除按钮点击事件
            dgv.CellClick += (s, e) =>
            {
                if (e.ColumnIndex == dgv.Columns["Action"].Index && e.RowIndex >= 0)
                {
                    dgv.Rows.RemoveAt(e.RowIndex);
                }
            };

            form.Controls.Add(table);
            form.ShowDialog();
        }

        // 器械入库弹窗
        private void ShowDeviceStorageDialog()
        {
            var form = new Form();
            form.Text = "器械入库";
            form.FormBorderStyle = FormBorderStyle.Sizable;
            form.StartPosition = FormStartPosition.CenterParent;
            form.Size = new System.Drawing.Size(700, 520);
            form.MaximizeBox = false;
            form.MinimizeBox = false;

            // 使用TableLayoutPanel优化布局
            var table = new TableLayoutPanel();
            table.Dock = DockStyle.Fill;
            table.ColumnCount = 6;
            table.RowCount = 3;
            // 上半部分控件
            var lblWarehouse = new Label() { Text = "库房：", Font = new System.Drawing.Font("微软雅黑", 10) };
            var cmbWarehouse = new System.Windows.Forms.ComboBox() { Font = new System.Drawing.Font("微软雅黑", 10) };
            cmbWarehouse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbWarehouse.Items.AddRange(new object[] { "消毒供应室", "手术供应室", "清洁供应室" });
            cmbWarehouse.SelectedIndex = 0;

            var lblSupplier = new Label() { Text = "供应商：", Font = new System.Drawing.Font("微软雅黑", 10) };
            var cmbSupplier = new System.Windows.Forms.ComboBox() { Font = new System.Drawing.Font("微软雅黑", 10) };
            cmbSupplier.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbSupplier.Items.AddRange(new object[] { "富士康", "博世", "宝洁" });
            cmbSupplier.SelectedIndex = 0;

            var lblExplain = new Label() { Text = "说明：", Font = new System.Drawing.Font("微软雅黑", 10) };
            var txtExplain = new TextBox() { Font = new System.Drawing.Font("微软雅黑", 10), Multiline = true, Height = 60, Width = 400 };

            // 调整列宽，增加间距
            table.ColumnStyles.Clear();
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 标签加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200)); // 下拉框加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 30)); // 间距
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 80)); // 标签加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 200)); // 下拉框加宽
            table.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // 剩余空间
            table.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            table.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            table.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // 已选物品表格
            var dgv = new DataGridView()
            {
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font("微软雅黑", 10),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = false,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize
            };

            // 初始化已选物品表格列
            dgv.Columns.Add("ItemId", "物品ID"); // 添加物品ID列（隐藏）
            dgv.Columns.Add("ItemName", "物品名称");
            dgv.Columns.Add("ItemCode", "物品编码");
            dgv.Columns.Add("ItemType", "物品类型");
            dgv.Columns.Add("Quantity", "数量");

            // 添加操作列，用于显示删除按钮
            var actionColumn = new DataGridViewButtonColumn()
            {
                Name = "Action",
                HeaderText = "操作",
                Text = "删除",
                UseColumnTextForButtonValue = true,
                Width = 80
            };
            dgv.Columns.Add(actionColumn);

            // 选择物品按钮
            var btnSelectItems = new Button()
            {
                Text = "选择物品",
                Font = new System.Drawing.Font("微软雅黑", 10),
                Width = 100,
                Height = 30,
                BackColor = System.Drawing.Color.LightBlue
            };

            // 为选择物品按钮添加点击事件
            btnSelectItems.Click += (s, e) =>
            {
                ShowItemSelectionDialog("device", dgv); // 器械入库类型，传递目标表格
            };

            // 添加价格列
            dgv.Columns.Add("Price", "价格");

            // 隐藏物品ID列
            dgv.Columns["ItemId"].Visible = false;

            // 设置数量列为可编辑
            dgv.Columns["Quantity"].ReadOnly = false;

            // 添加数量验证事件
            dgv.CellValidating += (s, e) =>
            {
                if (e.ColumnIndex == dgv.Columns["Quantity"].Index)
                {
                    if (int.TryParse(e.FormattedValue.ToString(), out int quantity))
                    {
                        if (quantity < 1)
                        {
                            e.Cancel = true;
                            MessageBox.Show("数量不能小于1", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    else
                    {
                        e.Cancel = true;
                        MessageBox.Show("请输入有效的数字", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            };

            // 第一行：库房、供应商
            table.Controls.Add(lblWarehouse, 0, 0);
            table.Controls.Add(cmbWarehouse, 1, 0);
            table.Controls.Add(new Label(), 2, 0); // 间隔
            table.Controls.Add(lblSupplier, 3, 0);
            table.Controls.Add(cmbSupplier, 4, 0);
            // 第二行：说明、选择物品按钮
            table.Controls.Add(lblExplain, 0, 1);
            table.SetColumnSpan(lblExplain, 1);
            table.Controls.Add(txtExplain, 1, 1);
            table.SetColumnSpan(txtExplain, 3);
            table.Controls.Add(btnSelectItems, 4, 1);
            // 第三行：表格
            table.Controls.Add(dgv, 0, 2);
            table.SetColumnSpan(dgv, 6);

            // 第四行：入库按钮
            var btnInbound = new Button()
            {
                Text = "入库",
                Font = new System.Drawing.Font("微软雅黑", 12, FontStyle.Bold),
                Width = 120,
                Height = 40,
                BackColor = System.Drawing.Color.Green,
                ForeColor = System.Drawing.Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnInbound.Click += async (s, e) =>
            {
                if (dgv.Rows.Count == 0)
                {
                    MessageBox.Show("请先选择要入库的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                bool hasInvalidQuantity = false;
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    if (row.Cells["Quantity"].Value == null || !int.TryParse(row.Cells["Quantity"].Value.ToString(), out int quantity) || quantity < 1)
                    {
                        hasInvalidQuantity = true;
                        break;
                    }
                }
                if (hasInvalidQuantity)
                {
                    MessageBox.Show("请确保所有物品的数量都大于0", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                var inboundData = new
                {
                    Warehouse = cmbWarehouse.SelectedItem?.ToString(),
                    Supplier = cmbSupplier.SelectedItem?.ToString(),
                    Description = txtExplain.Text.Trim(),
                    Items = new List<object>()
                };
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    inboundData.Items.Add(new
                    {
                        ItemName = row.Cells["ItemName"].Value?.ToString(),
                        ItemCode = row.Cells["ItemCode"].Value?.ToString(),
                        ItemType = row.Cells["ItemType"].Value?.ToString(),
                        Quantity = int.Parse(row.Cells["Quantity"].Value?.ToString() ?? "1"),
                        Price = row.Cells["Price"].Value?.ToString()
                    });
                }
                string confirmMessage = $"确认入库以下物品？\n\n库房：{inboundData.Warehouse}\n供应商：{inboundData.Supplier}\n物品数量：{inboundData.Items.Count}项\n\n说明：{inboundData.Description}";
                if (MessageBox.Show(confirmMessage, "确认入库", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // 调用入库API
                    try
                    {
                        var requestData = new
                        {
                            storageType = 0, // 器械入库类型为0
                            warehouse = cmbWarehouse.SelectedItem?.ToString(),
                            supplier = cmbSupplier.SelectedItem?.ToString(),
                            storageExplain = txtExplain.Text.Trim(),
                            storageDetails = new List<object>()
                        };

                        foreach (DataGridViewRow row in dgv.Rows)
                        {
                            requestData.storageDetails.Add(new
                            {
                                materialId = int.Parse(row.Cells["ItemId"].Value?.ToString() ?? "0"), // 使用保存的物品ID
                                materialType = 0, // 器械入库类型为0
                                materialCode = row.Cells["ItemCode"].Value?.ToString(),
                                materialName = row.Cells["ItemName"].Value?.ToString(),
                                storageNum = int.Parse(row.Cells["Quantity"].Value?.ToString() ?? "1"),
                                materialSpecs = row.Cells["ItemType"].Value?.ToString(),
                                materialPrice = row.Cells["Price"].Value?.ToString(),
                                registerNo = "",
                                produceDate = "",
                                sterilizaDate = "",
                                lapseDate = ""
                            });
                        }

                        // 调用API
                        var apiUrl = WriteApiConfig.GetApiUrl("Wms", "AddWmsStorage");
                        var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);
                        var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                        using (var client = new System.Net.Http.HttpClient())
                        {
                            client.Timeout = TimeSpan.FromSeconds(30);
                            var response = await client.PostAsync(apiUrl, content);
                            var responseContent = await response.Content.ReadAsStringAsync();

                            if (response.IsSuccessStatusCode)
                            {
                                //var result = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(responseContent);
                                var apiResponse = JsonConvert.DeserializeObject<Apiresponse>(responseContent);
                                if (apiResponse != null && apiResponse.code == "Success")
                                {
                                    MessageBox.Show("入库操作成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    form.Close();
                                    // 刷新页面数据
                                    await BindGrid();
                                }
                                else
                                {
                                    MessageBox.Show($"入库失败：{apiResponse.msg}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                MessageBox.Show($"API调用失败：{response.StatusCode}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"入库操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            };
            btnInbound.Anchor = AnchorStyles.Right;
            table.Controls.Add(btnInbound, 5, 3);

            // 处理删除按钮点击事件
            dgv.CellClick += (s, e) =>
            {
                if (e.ColumnIndex == dgv.Columns["Action"].Index && e.RowIndex >= 0)
                {
                    dgv.Rows.RemoveAt(e.RowIndex);
                }
            };

            form.Controls.Add(table);
            form.ShowDialog();
        }

        // 物品选择对话框
        private void ShowItemSelectionDialog(string storageType, DataGridView targetDgv)
        {
            var dialog = new Form();
            dialog.Text = storageType == "pack" ? "选择包物品" : "选择器械物品";
            dialog.FormBorderStyle = FormBorderStyle.FixedDialog;
            dialog.StartPosition = FormStartPosition.CenterParent;
            dialog.Size = new System.Drawing.Size(800, 600);
            dialog.MaximizeBox = false;
            dialog.MinimizeBox = false;

            // 创建搜索面板
            var searchPanel = new Panel();
            searchPanel.Dock = DockStyle.Top;
            searchPanel.Height = 60;
            searchPanel.Padding = new Padding(10);

            var lblSearch = new Label() { Text = "搜索：", Location = new Point(10, 20), AutoSize = true };
            var txtSearch = new TextBox() { Location = new Point(60, 17), Width = 200 };
            var btnSearch = new Button() { Text = "搜索", Location = new Point(270, 15), Width = 80, Height = 30 };

            searchPanel.Controls.AddRange(new Control[] { lblSearch, txtSearch, btnSearch });

            // 创建数据表格
            var dgv = new DataGridView();
            dgv.Dock = DockStyle.Fill;
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.MultiSelect = false; // 改为单选，点击即可选择

            // 根据入库类型设置不同的列
            if (storageType == "pack")
            {
                dgv.Columns.Add("ItemId", "物品ID"); // 添加物品ID列（隐藏）
                dgv.Columns.Add("PackageName", "包名称");
                dgv.Columns.Add("PackageCode", "包编码");
                dgv.Columns.Add("Model", "规格");
                dgv.Columns.Add("ServicePrice", "服务价格");
                dgv.Columns["ItemId"].Visible = false; // 隐藏物品ID列
            }
            else
            {
                dgv.Columns.Add("ItemId", "物品ID"); // 添加物品ID列（隐藏）
                dgv.Columns.Add("DeviceName", "器械名称");
                dgv.Columns.Add("DeviceCode", "器械编码");
                dgv.Columns.Add("Model", "规格");
                dgv.Columns.Add("Price", "价格");
                dgv.Columns["ItemId"].Visible = false; // 隐藏物品ID列
            }

            // 创建按钮面板
            var buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.Height = 50;
            buttonPanel.Padding = new Padding(10);

            var btnConfirm = new Button() { Text = "确认", Location = new Point(600, 10), Width = 80, Height = 30 };
            var btnCancel = new Button() { Text = "取消", Location = new Point(690, 10), Width = 80, Height = 30 };

            buttonPanel.Controls.AddRange(new Control[] { btnConfirm, btnCancel });

            // 添加控件到对话框
            dialog.Controls.Add(dgv);
            dialog.Controls.Add(searchPanel);
            dialog.Controls.Add(buttonPanel);

            // 自动加载数据
            async void LoadItemData()
            {
                try
                {
                    // 根据入库类型调用不同的API接口
                    string apiUrl = "";
                    if (storageType == "pack")
                    {
                        // 包入库接口
                        apiUrl = $"{ReadApiConfig.GetApiUrl("Common", "GetEquipmentPackageList")}";
                    }
                    else
                    {
                        // 器械入库接口
                        apiUrl = $"{ReadApiConfig.GetApiUrl("Common", "GetApparatusList")}";
                    }

                    var client = new System.Net.Http.HttpClient();
                    client.Timeout = TimeSpan.FromSeconds(30);
                    var response = await client.GetStringAsync(apiUrl);

                    if (!string.IsNullOrEmpty(response))
                    {
                        var jobj = Newtonsoft.Json.Linq.JObject.Parse(response);
                        if (jobj["code"]?.ToString() == "200")
                        {
                            dgv.Rows.Clear();
                            var data = jobj["data"];
                            if (data != null && data.HasValues)
                            {
                                var pageData = data["pageData"];
                                if (pageData != null && pageData.HasValues)
                                {
                                    int itemCount = 0;
                                    foreach (var item in pageData)
                                    {
                                        if (storageType == "pack")
                                        {
                                            dgv.Rows.Add(
                                                item["id"]?.ToString() ?? "", // 物品ID
                                                item["packageName"]?.ToString() ?? "",
                                                item["packageCode"]?.ToString() ?? "",
                                                item["model"]?.ToString() ?? "",
                                                item["servicePrice"]?.ToString() ?? "0"
                                            );
                                            itemCount++;
                                        }
                                        else
                                        {
                                            dgv.Rows.Add(
                                                item["id"]?.ToString() ?? "", // 物品ID
                                                item["apparatusName"]?.ToString() ?? "",
                                                item["apparatusCode"]?.ToString() ?? "",
                                                item["model"]?.ToString() ?? "",
                                                item["price"]?.ToString() ?? "0"
                                            );
                                            itemCount++;
                                        }
                                    }
                                    //MessageBox.Show($"成功加载 {itemCount} 条数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                                else
                                {
                                    MessageBox.Show("没有找到数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                            }
                            else
                            {
                                MessageBox.Show("数据为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }
                        else
                        {
                            MessageBox.Show($"加载数据失败: {jobj["msg"]?.ToString() ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("API响应为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }

            LoadItemData();

            // 移除双击事件，改为使用确认按钮选择物品

            // 搜索按钮点击事件
            btnSearch.Click += async (s, e) =>
            {
                try
                {
                    string searchText = txtSearch.Text.Trim();
                    if (string.IsNullOrEmpty(searchText))
                    {
                        MessageBox.Show("请输入搜索关键词", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 根据入库类型调用不同的API接口
                    string apiUrl = "";
                    if (storageType == "pack")
                    {
                        // 包入库接口
                        apiUrl = $"{ReadApiConfig.GetApiUrl("Common", "GetEquipmentPackageList")}?keyword={Uri.EscapeDataString(searchText)}";
                    }
                    else
                    {
                        // 器械入库接口
                        apiUrl = $"{ReadApiConfig.GetApiUrl("Common", "GetApparatusList")}?keyword={Uri.EscapeDataString(searchText)}";
                    }

                    var client = new System.Net.Http.HttpClient();
                    client.Timeout = TimeSpan.FromSeconds(30);
                    var response = await client.GetStringAsync(apiUrl);

                    if (!string.IsNullOrEmpty(response))
                    {
                        var jobj = Newtonsoft.Json.Linq.JObject.Parse(response);
                        if (jobj["code"]?.ToString() == "200")
                        {
                            dgv.Rows.Clear();
                            var data = jobj["data"];
                            if (data != null && data.HasValues)
                            {
                                var pageData = data["pageData"];
                                if (pageData != null && pageData.HasValues)
                                {
                                    int itemCount = 0;
                                    foreach (var item in pageData)
                                    {
                                        if (storageType == "pack")
                                        {
                                            dgv.Rows.Add(
                                                item["id"]?.ToString() ?? "", // 物品ID
                                                item["packageName"]?.ToString() ?? "",
                                                item["packageCode"]?.ToString() ?? "",
                                                item["model"]?.ToString() ?? "",
                                                item["servicePrice"]?.ToString() ?? "0"
                                            );
                                            itemCount++;
                                        }
                                        else
                                        {
                                            dgv.Rows.Add(
                                                item["id"]?.ToString() ?? "", // 物品ID
                                                item["apparatusName"]?.ToString() ?? "",
                                                item["apparatusCode"]?.ToString() ?? "",
                                                item["model"]?.ToString() ?? "",
                                                item["price"]?.ToString() ?? "0"
                                            );
                                            itemCount++;
                                        }
                                    }
                                    //MessageBox.Show($"搜索到 {itemCount} 条数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                                else
                                {
                                    MessageBox.Show("没有找到匹配的数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                            }
                            else
                            {
                                MessageBox.Show("搜索结果为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }
                        else
                        {
                            MessageBox.Show($"搜索失败: {jobj["msg"]?.ToString() ?? "未知错误"}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("搜索响应为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"搜索失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            // 确认按钮点击事件
            btnConfirm.Click += (s, e) =>
            {
                // 获取选中的行
                var selectedRows = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    if (row.Selected)
                    {
                        selectedRows.Add(row);
                    }
                }

                if (selectedRows.Count == 0)
                {
                    MessageBox.Show("请至少选择一项物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 将选中的物品添加到目标表格
                foreach (var row in selectedRows)
                {
                    string itemId, itemName, itemCode, itemType, price;

                    if (storageType == "pack")
                    {
                        itemId = row.Cells["ItemId"].Value?.ToString() ?? "";
                        itemName = row.Cells["PackageName"].Value?.ToString() ?? "";
                        itemCode = row.Cells["PackageCode"].Value?.ToString() ?? "";
                        itemType = row.Cells["Model"].Value?.ToString() ?? "";
                        price = row.Cells["ServicePrice"].Value?.ToString() ?? "0";
                    }
                    else
                    {
                        itemId = row.Cells["ItemId"].Value?.ToString() ?? "";
                        itemName = row.Cells["DeviceName"].Value?.ToString() ?? "";
                        itemCode = row.Cells["DeviceCode"].Value?.ToString() ?? "";
                        itemType = row.Cells["Model"].Value?.ToString() ?? "";
                        price = row.Cells["Price"].Value?.ToString() ?? "0";
                    }

                    // 检查是否已经添加过相同的物品
                    bool alreadyExists = false;
                    foreach (DataGridViewRow targetRow in targetDgv.Rows)
                    {
                        if (targetRow.Cells["ItemCode"].Value?.ToString() == itemCode)
                        {
                            alreadyExists = true;
                            break;
                        }
                    }

                    if (alreadyExists)
                    {
                        MessageBox.Show($"物品 {itemName} 已存在，已跳过", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        continue;
                    }

                    // 添加到目标表格
                    int newRowIndex = targetDgv.Rows.Add();
                    targetDgv.Rows[newRowIndex].Cells["ItemId"].Value = itemId; // 保存物品ID
                    targetDgv.Rows[newRowIndex].Cells["ItemName"].Value = itemName;
                    targetDgv.Rows[newRowIndex].Cells["ItemCode"].Value = itemCode;
                    targetDgv.Rows[newRowIndex].Cells["ItemType"].Value = itemType;
                    targetDgv.Rows[newRowIndex].Cells["Quantity"].Value = "1"; // 默认数量为1
                    targetDgv.Rows[newRowIndex].Cells["Price"].Value = price; // 添加价格信息
                }

                MessageBox.Show($"已添加 {selectedRows.Count} 项物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                dialog.DialogResult = DialogResult.OK;
                dialog.Close();
            };

            // 取消按钮点击事件
            btnCancel.Click += (s, e) =>
            {
                dialog.DialogResult = DialogResult.Cancel;
                dialog.Close();
            };

            dialog.ShowDialog();
        }

        /// <summary>
        /// 显示入库记录详情对话框
        /// </summary>
        /// <param name="data">入库记录数据</param>
        private void ShowDetailDialog(Newtonsoft.Json.Linq.JToken data)
        {
            var detailForm = new Form();
            detailForm.Text = "入库记录详情";
            detailForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            detailForm.StartPosition = FormStartPosition.CenterParent;
            detailForm.Size = new System.Drawing.Size(800, 600);
            detailForm.MaximizeBox = false;
            detailForm.MinimizeBox = false;

            // 创建主面板
            var mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Padding = new Padding(20);
            mainPanel.AutoScroll = true;

            // 基本信息面板
            var basicInfoPanel = new Panel();
            basicInfoPanel.Dock = DockStyle.Top;
            basicInfoPanel.Height = 180;
            basicInfoPanel.BorderStyle = BorderStyle.FixedSingle;
            basicInfoPanel.Padding = new Padding(15);

            var basicInfoLabel = new Label();
            basicInfoLabel.Text = "基本信息";
            basicInfoLabel.Font = new System.Drawing.Font("微软雅黑", 12, FontStyle.Bold);
            basicInfoLabel.ForeColor = System.Drawing.Color.FromArgb(0, 120, 215);
            basicInfoLabel.Location = new Point(0, 0);
            basicInfoLabel.AutoSize = true;

            // 基本信息内容
            var basicInfoContent = new TableLayoutPanel();
            basicInfoContent.Location = new Point(0, 30);
            basicInfoContent.Size = new System.Drawing.Size(750, 140);
            basicInfoContent.ColumnCount = 2;
            basicInfoContent.RowCount = 5;
            basicInfoContent.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            basicInfoContent.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // 添加基本信息字段
            AddInfoRow(basicInfoContent, 0, "入库单号：", data["storageCode"]?.ToString() ?? "");
            AddInfoRow(basicInfoContent, 1, "入库类型：", data["storageTypeName"]?.ToString() ?? "");
            AddInfoRow(basicInfoContent, 2, "库房：", data["warehouseName"]?.ToString() ?? "");
            AddInfoRow(basicInfoContent, 3, "供应商：", data["supplierName"]?.ToString() ?? "");
            AddInfoRow(basicInfoContent, 4, "说明：", data["storageExplain"]?.ToString() ?? "");

            // 审核信息面板
            var auditInfoPanel = new Panel();
            auditInfoPanel.Dock = DockStyle.Top;
            auditInfoPanel.Height = 120;
            auditInfoPanel.BorderStyle = BorderStyle.FixedSingle;
            auditInfoPanel.Padding = new Padding(15);

            var auditInfoLabel = new Label();
            auditInfoLabel.Text = "审核信息";
            auditInfoLabel.Font = new System.Drawing.Font("微软雅黑", 12, FontStyle.Bold);
            auditInfoLabel.ForeColor = System.Drawing.Color.FromArgb(0, 120, 215);
            auditInfoLabel.Location = new Point(0, 0);
            auditInfoLabel.AutoSize = true;

            var auditInfoContent = new TableLayoutPanel();
            auditInfoContent.Location = new Point(0, 30);
            auditInfoContent.Size = new System.Drawing.Size(750, 80);
            auditInfoContent.ColumnCount = 4;
            auditInfoContent.RowCount = 2;
            auditInfoContent.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            auditInfoContent.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            auditInfoContent.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120));
            auditInfoContent.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            AddInfoRow(auditInfoContent, 0, "审核状态：", (data["auditState"]?.ToObject<bool>() == true ? "已审批" : "待审批"));
            AddInfoRow(auditInfoContent, 0, "审核人：", data["auditName"]?.ToString() ?? "");
            AddInfoRow(auditInfoContent, 1, "审核时间：", data["auditDate"]?.ToString() ?? "");
            AddInfoRow(auditInfoContent, 1, "创建时间：", data["createTime"]?.ToString() ?? "");

            // 物品详情面板
            var itemsPanel = new Panel();
            itemsPanel.Dock = DockStyle.Fill;
            itemsPanel.BorderStyle = BorderStyle.FixedSingle;
            itemsPanel.Padding = new Padding(15);

            var itemsLabel = new Label();
            itemsLabel.Text = "物品详情";
            itemsLabel.Font = new System.Drawing.Font("微软雅黑", 12, FontStyle.Bold);
            itemsLabel.ForeColor = System.Drawing.Color.FromArgb(0, 120, 215);
            itemsLabel.Location = new Point(0, 0);
            itemsLabel.AutoSize = true;

            // 物品详情表格
            var itemsDataGridView = new DataGridView();
            itemsDataGridView.Location = new Point(0, 30);
            itemsDataGridView.Size = new System.Drawing.Size(750, 320);
            itemsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            itemsDataGridView.AllowUserToAddRows = false;
            itemsDataGridView.AllowUserToDeleteRows = false;
            itemsDataGridView.ReadOnly = true;
            itemsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            itemsDataGridView.BackgroundColor = System.Drawing.Color.White;
            itemsDataGridView.BorderStyle = BorderStyle.Fixed3D;
            itemsDataGridView.Font = new System.Drawing.Font("微软雅黑", 9);

            // 添加表格列
            itemsDataGridView.Columns.Add("materialCode", "物品编码");
            itemsDataGridView.Columns.Add("materialName", "物品名称");
            itemsDataGridView.Columns.Add("materialTypeName", "物品类型");
            itemsDataGridView.Columns.Add("storageNum", "数量");
            itemsDataGridView.Columns.Add("materialSpecs", "规格");
            itemsDataGridView.Columns.Add("materialPrice", "价格");
            itemsDataGridView.Columns.Add("registerNo", "注册号");
            itemsDataGridView.Columns.Add("produceDate", "生产日期");
            itemsDataGridView.Columns.Add("sterilizaDate", "灭菌日期");
            itemsDataGridView.Columns.Add("lapseDate", "过期日期");

            // 填充物品数据
            var details = data["details"];
            if (details != null && details.HasValues)
            {
                foreach (var item in details)
                {
                    itemsDataGridView.Rows.Add(
                        item["materialCode"]?.ToString() ?? "",
                        item["materialName"]?.ToString() ?? "",
                        item["materialTypeName"]?.ToString() ?? "",
                        item["storageNum"]?.ToString() ?? "",
                        item["materialSpecs"]?.ToString() ?? "",
                        item["materialPrice"]?.ToString() ?? "",
                        item["registerNo"]?.ToString() ?? "",
                        item["produceDate"]?.ToString() ?? "",
                        item["sterilizaDate"]?.ToString() ?? "",
                        item["lapseDate"]?.ToString() ?? ""
                    );
                }
            }

            // 确定按钮
            var confirmButton = new Button();
            confirmButton.Text = "确定";
            confirmButton.Size = new System.Drawing.Size(80, 30);
            confirmButton.Location = new Point(700, 520);
            confirmButton.BackColor = System.Drawing.Color.FromArgb(0, 120, 215);
            confirmButton.ForeColor = System.Drawing.Color.White;
            confirmButton.FlatStyle = FlatStyle.Flat;
            confirmButton.Click += (s, e) => detailForm.Close();

            // 组装界面
            basicInfoPanel.Controls.Add(basicInfoLabel);
            basicInfoPanel.Controls.Add(basicInfoContent);

            auditInfoPanel.Controls.Add(auditInfoLabel);
            auditInfoPanel.Controls.Add(auditInfoContent);

            itemsPanel.Controls.Add(itemsLabel);
            itemsPanel.Controls.Add(itemsDataGridView);

            mainPanel.Controls.Add(itemsPanel);
            mainPanel.Controls.Add(auditInfoPanel);
            mainPanel.Controls.Add(basicInfoPanel);

            detailForm.Controls.Add(mainPanel);
            detailForm.Controls.Add(confirmButton);

            detailForm.ShowDialog();
        }

        /// <summary>
        /// 添加信息行到表格布局
        /// </summary>
        /// <param name="table">表格布局面板</param>
        /// <param name="row">行索引</param>
        /// <param name="label">标签文本</param>
        /// <param name="value">值文本</param>
        private void AddInfoRow(TableLayoutPanel table, int row, string label, string value)
        {
            var labelControl = new Label();
            labelControl.Text = label;
            labelControl.Font = new System.Drawing.Font("微软雅黑", 9, FontStyle.Bold);
            labelControl.ForeColor = System.Drawing.Color.FromArgb(64, 64, 64);
            labelControl.AutoSize = true;
            labelControl.TextAlign = ContentAlignment.MiddleLeft;

            var valueControl = new Label();
            valueControl.Text = value;
            valueControl.Font = new System.Drawing.Font("微软雅黑", 9);
            valueControl.ForeColor = System.Drawing.Color.Black;
            valueControl.AutoSize = true;
            valueControl.TextAlign = ContentAlignment.MiddleLeft;

            table.Controls.Add(labelControl, 0, row);
            table.Controls.Add(valueControl, 1, row);
        }
    }
}
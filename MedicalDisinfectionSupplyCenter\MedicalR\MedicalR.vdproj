﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:MedicalR"
"LanguageId" = "3:2052"
"CodePage" = "3:936"
"UILanguageId" = "3:2052"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_01116C56E234C04A2A2B36358EC7CB8F"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_01116C56E234C04A2A2B36358EC7CB8F"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_021B15C3F2475E29EC440A15F81467F8"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_021B15C3F2475E29EC440A15F81467F8"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05674C9ADDEE365B5593552E3B316BA3"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05674C9ADDEE365B5593552E3B316BA3"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09591403E4087838EA3F2F34026C1441"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09591403E4087838EA3F2F34026C1441"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09C8D50F4BF056BEB1F08F12BB38457B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_09C8D50F4BF056BEB1F08F12BB38457B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0A216560CC06AF0A7F64AB1F109F9DF5"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0A216560CC06AF0A7F64AB1F109F9DF5"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0D7C78D4803C0D7CA67518C19C535DFB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0D7C78D4803C0D7CA67518C19C535DFB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_126EDD6274C456311F7C163E3C974C02"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_126EDD6274C456311F7C163E3C974C02"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1616002F5957B21EED3BF52D6A35C237"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1616002F5957B21EED3BF52D6A35C237"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1806659EA99A722F63FE611E2D69C50B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1806659EA99A722F63FE611E2D69C50B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1F67927C1D3A4E978D7BB9BC134D79C8"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2234E383DF64544008C83D682DB1076B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2234E383DF64544008C83D682DB1076B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_238DEFBF224C2DF83C1EFBF450B450B4"
        "OwnerKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_238DEFBF224C2DF83C1EFBF450B450B4"
        "OwnerKey" = "8:_7F5D7AC18E75311B4A31D1467B59D28A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_247D861C49210FF11ED13EBE0A7C08DB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_247D861C49210FF11ED13EBE0A7C08DB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "OwnerKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "OwnerKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "OwnerKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "OwnerKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "OwnerKey" = "8:_CB96099B69163F32A6F2EFAD1CF46277"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_4BC939A2D9C855961F487DEBF3841A4B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_C250F21708B124F2F3221C01B4541BAF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "OwnerKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_27AC8F71DDC82B49B627DF338871FD5D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_27AC8F71DDC82B49B627DF338871FD5D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2B67F8716052D51195901B5B7807908B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2B67F8716052D51195901B5B7807908B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2DE3F69777AFB7F3B459BDF95E938BA9"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2DE3F69777AFB7F3B459BDF95E938BA9"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2E6F931A0343E2B35FE5630A75DA8EB4"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2E6F931A0343E2B35FE5630A75DA8EB4"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2F9A47D599581989BC824AAFEFB6BFBB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2F9A47D599581989BC824AAFEFB6BFBB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2FF7A565C17BCAFBD3625EF90161F725"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2FF7A565C17BCAFBD3625EF90161F725"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_30D4601BE350FEA17188A874BA34F573"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_30D4601BE350FEA17188A874BA34F573"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_34556BAFA5A28C002A180B2BA0D0AEC7"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_34556BAFA5A28C002A180B2BA0D0AEC7"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_34DC43F3DEABEC450A61D965E2E8B353"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_34DC43F3DEABEC450A61D965E2E8B353"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_363BAEB88A31960BAF9389E9F34FF7DB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_363BAEB88A31960BAF9389E9F34FF7DB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_36AB572ED89117229B9A2ECE2F9B49C2"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_36AB572ED89117229B9A2ECE2F9B49C2"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_36D7883BF866DA0563DF0CFB944EA780"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_36D7883BF866DA0563DF0CFB944EA780"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_38CAB68DFAC5A971C74DA4362BE14119"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_38CAB68DFAC5A971C74DA4362BE14119"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39DAA9A79C3315A73D3A379A59FB1893"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_39DAA9A79C3315A73D3A379A59FB1893"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3BD4C045128141E088ADA6E1838B8B91"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3CDB28BB56B33ABE8E2ACDEE13C8651C"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3CDB28BB56B33ABE8E2ACDEE13C8651C"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3D7BCEB188A8C00FE92812A3725AEFB1"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3D7BCEB188A8C00FE92812A3725AEFB1"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F77991B6F8D91B15A2E275DF898FFFC"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F77991B6F8D91B15A2E275DF898FFFC"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_439B01AE55DC4552BDF759513C4E3746"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4460A0DF8D8237BC09CBD663BE2032BC"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4460A0DF8D8237BC09CBD663BE2032BC"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45684A06814A203227C31CBBA231B19A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45684A06814A203227C31CBBA231B19A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45953C539E37FE533D6EE0B818936148"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45953C539E37FE533D6EE0B818936148"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_472EBDB91799F7A1525A14722D095DC2"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_472EBDB91799F7A1525A14722D095DC2"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4739E473B6AC56195D9B75120EB604B8"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4739E473B6AC56195D9B75120EB604B8"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4763927AF3131F63B13E54E887043F8B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4763927AF3131F63B13E54E887043F8B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4A90FC383A9E5F8022555AA4F66FDA93"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4A90FC383A9E5F8022555AA4F66FDA93"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4AF5A66CF3F49D93479C2B05FC16CD0F"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4AF5A66CF3F49D93479C2B05FC16CD0F"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4BC939A2D9C855961F487DEBF3841A4B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4BC939A2D9C855961F487DEBF3841A4B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4BC939A2D9C855961F487DEBF3841A4B"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "OwnerKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4DA01DEEADE419188F1732F0D1EAEE55"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4DA01DEEADE419188F1732F0D1EAEE55"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4E925A9D0095283095398DA918B7D97E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4E925A9D0095283095398DA918B7D97E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4EA34E2A97F12AB4CEC0525453702AEB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4EA34E2A97F12AB4CEC0525453702AEB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4F27AA368C20359E6A0DA1487B10B23E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4F27AA368C20359E6A0DA1487B10B23E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5362A13F4B874227924A3CCF52DE8B18"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5362A13F4B874227924A3CCF52DE8B18"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_555E11C6A3DDD0B81D437D9281B5DEC0"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_555E11C6A3DDD0B81D437D9281B5DEC0"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5645D3E37FAF3B07EC96A43EA8E783EA"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5645D3E37FAF3B07EC96A43EA8E783EA"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5C660F39D4707FA05211F6C5062706F5"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5C660F39D4707FA05211F6C5062706F5"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5CBB1CDCDCA406F7E6F6901B8C5D3B87"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5CBB1CDCDCA406F7E6F6901B8C5D3B87"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D2EEF6BA2D2B7F2E47ECB4D747D64BD"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5D2EEF6BA2D2B7F2E47ECB4D747D64BD"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_62015BE741C4E0A4C431659024888C84"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_62015BE741C4E0A4C431659024888C84"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_643FE5EFFEA737A1C4E6A6DE115267C1"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_643FE5EFFEA737A1C4E6A6DE115267C1"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_67D66108E3EB27781C1ABFDD7136029A"
        "OwnerKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_67D66108E3EB27781C1ABFDD7136029A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_67D66108E3EB27781C1ABFDD7136029A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_687C57558E93DCDD0E9F6F45580CF20E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_687C57558E93DCDD0E9F6F45580CF20E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_68D5B70F94AD4CCE9C6C0704E0746229"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_68D5B70F94AD4CCE9C6C0704E0746229"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6E71DD56FECA059038EA13985CAB8AAB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6E71DD56FECA059038EA13985CAB8AAB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6FB21A16DB42DC2F9EB11A533DF6A1DF"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6FB21A16DB42DC2F9EB11A533DF6A1DF"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6FB21A16DB42DC2F9EB11A533DF6A1DF"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_71AB9050E3DF412B9FF88036401AF803"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_71AB9050E3DF412B9FF88036401AF803"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_737B308408F772285C78254731C26120"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_737B308408F772285C78254731C26120"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_737B308408F772285C78254731C26120"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_737B308408F772285C78254731C26120"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_991088D7112AD03EB68692BF3E75CDEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7ED8135E7F31E1E1CE80B4BFFD7D400D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7ED8135E7F31E1E1CE80B4BFFD7D400D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7F5D7AC18E75311B4A31D1467B59D28A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7F5D7AC18E75311B4A31D1467B59D28A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7FE56FE4D9A6F76F5EEC984E9104F788"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7FE56FE4D9A6F76F5EEC984E9104F788"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_801F20CE0A94B64242883AA55BDA8D14"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_801F20CE0A94B64242883AA55BDA8D14"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_804C5D5F04A25CA1489DD52BB99DC92C"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_804C5D5F04A25CA1489DD52BB99DC92C"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8305ED163A7EE1B015367F460B5A0E32"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8305ED163A7EE1B015367F460B5A0E32"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_87A534DA776C77C1FE7BE1D24BF1D297"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_87A534DA776C77C1FE7BE1D24BF1D297"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9266C439EAE14E4B124A11A4467E721D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9266C439EAE14E4B124A11A4467E721D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_92D3CCB12FB6187ED430504EBF830A5F"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_92D3CCB12FB6187ED430504EBF830A5F"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_936730F6A70B421664E95406786E27A8"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_936730F6A70B421664E95406786E27A8"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9543F55176F8F783A71663713E2E122D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9543F55176F8F783A71663713E2E122D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97E3EE77D5E67D85EDAA188AE36F7001"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97E3EE77D5E67D85EDAA188AE36F7001"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_991088D7112AD03EB68692BF3E75CDEE"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_991088D7112AD03EB68692BF3E75CDEE"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9966FACBB79A4B279B63DFDBEF861660"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_99F851562409CD7A2A860922FC22790A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_99F851562409CD7A2A860922FC22790A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9EC285BB73273762E2016EBE0866AC25"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9EC285BB73273762E2016EBE0866AC25"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9F5112B0239D8DFAA5C6D54AF2315E28"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9F5112B0239D8DFAA5C6D54AF2315E28"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6DF26212CFC4E87B2EFD7C87687866A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6DF26212CFC4E87B2EFD7C87687866A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A73D4E4D544B63AD63743C21B319E7A4"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A73D4E4D544B63AD63743C21B319E7A4"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A8127A80B9D0F48E9A49EB072C7A81E1"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A8127A80B9D0F48E9A49EB072C7A81E1"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A9562FD612552996807FC6990C065B25"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A9562FD612552996807FC6990C065B25"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AA3FF5E975C0F9900FE2DA9ED9CC5384"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AA3FF5E975C0F9900FE2DA9ED9CC5384"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB0BD147C40B4C7645C317D6327ABBF2"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB0BD147C40B4C7645C317D6327ABBF2"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB0BD147C40B4C7645C317D6327ABBF2"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB3A08F03C0FAF389FA1CB802C64FBCC"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB3A08F03C0FAF389FA1CB802C64FBCC"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AD2492277B658B65C1B5BE6345B42049"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AD2492277B658B65C1B5BE6345B42049"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AE486BDD2D504C15500B989FCD8147F4"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AE486BDD2D504C15500B989FCD8147F4"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B40FD6F7D97F535DEBCBC8867777EE18"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B40FD6F7D97F535DEBCBC8867777EE18"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BA04A72048040EEA17BB4D3196109967"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BA04A72048040EEA17BB4D3196109967"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BB162602E0AE3775ADF708C5F97EE6C4"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BB162602E0AE3775ADF708C5F97EE6C4"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BCC6CF914C98DA9278CAF6FDD2A7617D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BCC6CF914C98DA9278CAF6FDD2A7617D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BD0187EC49096496978E30BB1CAAA520"
        "OwnerKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BD0187EC49096496978E30BB1CAAA520"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BD0187EC49096496978E30BB1CAAA520"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BD0187EC49096496978E30BB1CAAA520"
        "OwnerKey" = "8:_AB0BD147C40B4C7645C317D6327ABBF2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_737B308408F772285C78254731C26120"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_4BC939A2D9C855961F487DEBF3841A4B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_C250F21708B124F2F3221C01B4541BAF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "OwnerKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BE5DBD4B902A52D31AC3685F42C3B12E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BE5DBD4B902A52D31AC3685F42C3B12E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BFF1B5F20BFF19D42DD352A4EB0BB4FB"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BFF1B5F20BFF19D42DD352A4EB0BB4FB"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1855EC708E88AAAE33F5264B000FD2A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C1855EC708E88AAAE33F5264B000FD2A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C250F21708B124F2F3221C01B4541BAF"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C250F21708B124F2F3221C01B4541BAF"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C250F21708B124F2F3221C01B4541BAF"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C304FFFBD8CE8D59160B65AF6E548166"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C304FFFBD8CE8D59160B65AF6E548166"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "OwnerKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "OwnerKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "OwnerKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "OwnerKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CA78D837D340278E6BFBB8504151AC6D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CA78D837D340278E6BFBB8504151AC6D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CB96099B69163F32A6F2EFAD1CF46277"
        "OwnerKey" = "8:_A6DF26212CFC4E87B2EFD7C87687866A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2E47D216C633CEDC9A7CB62974BCBF4"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D2E47D216C633CEDC9A7CB62974BCBF4"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D4F38A7C98E9B69097091CEC26CB19A6"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D4F38A7C98E9B69097091CEC26CB19A6"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D77742768346CCB57E071ACF0A7C7152"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D77742768346CCB57E071ACF0A7C7152"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D890AC617DBDD3B58ED4E41AF9C0C733"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D890AC617DBDD3B58ED4E41AF9C0C733"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA2142A10BFBE7DC87EC73733F380538"
        "OwnerKey" = "8:_238DEFBF224C2DF83C1EFBF450B450B4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DA2142A10BFBE7DC87EC73733F380538"
        "OwnerKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DAA728FEA9B4D2100F427BB61081D35F"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DAA728FEA9B4D2100F427BB61081D35F"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBA42F1C27F6393A1D5FC2ABD7D769A3"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBA42F1C27F6393A1D5FC2ABD7D769A3"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBF6F56357B26D457579A0A9495CB994"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DBF6F56357B26D457579A0A9495CB994"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DDB993A79AADC922FCD91479D9EA4D8D"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DDB993A79AADC922FCD91479D9EA4D8D"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E32115D80ACEEB75260DFC229A78C4C5"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E32115D80ACEEB75260DFC229A78C4C5"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "OwnerKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E7427E9E6F88DD56A3F1129C7DFF0A5E"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E7427E9E6F88DD56A3F1129C7DFF0A5E"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E75AAA1C4267C72B41DF09A1B085A069"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E75AAA1C4267C72B41DF09A1B085A069"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E8D0F873850832AC0677480E168D96AC"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E8D0F873850832AC0677480E168D96AC"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9ECE188B5F010A7B8C47922094705BE"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E9ECE188B5F010A7B8C47922094705BE"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EB3EDBAC158A9AE0A2633E39156D8FE1"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EB3EDBAC158A9AE0A2633E39156D8FE1"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "OwnerKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ED0A6BE52154A5EF6C04B786C25090FA"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ED0A6BE52154A5EF6C04B786C25090FA"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ED0A6BE52154A5EF6C04B786C25090FA"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ED0A6BE52154A5EF6C04B786C25090FA"
        "OwnerKey" = "8:_6FB21A16DB42DC2F9EB11A533DF6A1DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EEEDBF171B3A985BA8CFF4B8DF3B59C9"
        "OwnerKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F25DBA22BFF32A354A4C31DA1093BCD8"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F25DBA22BFF32A354A4C31DA1093BCD8"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F5A5CF6648D84D94BE5C2CB3E0C2B174"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F9820B7BB655AC7B9F86651AFE214460"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F9820B7BB655AC7B9F86651AFE214460"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FB002B4C377ABE5F2770B1EBCD6C7397"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FB002B4C377ABE5F2770B1EBCD6C7397"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FBDE6099085867266DC5CB61B7E238B0"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FBDE6099085867266DC5CB61B7E238B0"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FBDE6099085867266DC5CB61B7E238B0"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FBDE6099085867266DC5CB61B7E238B0"
        "OwnerKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FDC34B76F40430FB0368422A5F958B4A"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FDC34B76F40430FB0368422A5F958B4A"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE7A898440DD5831CB7A35B6936EC614"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FE7A898440DD5831CB7A35B6936EC614"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_83BB8AC14D4A441394082649F05E2BEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4C65F352CBC6453CB631C236C7A99E25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2C139D4C7E604679A2EB3D97FD3A8B38"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_791F83E1E3D4F5954E54C176FFD15E60"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_737B308408F772285C78254731C26120"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5D1AFC591F9D476F629D7699D304EF75"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CAC06910BE144D39AD8DBAAA531956F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EAAAD6D75C8C81710D768DB31D065D9B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4298E84A9F356F8BDFABF6403EE4340B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C89900F8E226F14A5BE3B8E3FFAAD974"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_290DD9C5FEA1A536D63AC42125E925AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4BC939A2D9C855961F487DEBF3841A4B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_157CAE2A4DE642106E2D035F89F2464E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EB604B801EB0449CF5ED34AC426A2E29"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C250F21708B124F2F3221C01B4541BAF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_991088D7112AD03EB68692BF3E75CDEE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_763BF6C7C4438DBF0F1F15825CAB1333"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E445154B9CC29F8AEDC66251B6349133"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4C1F6AB8F1923BBCBDC76D164F9D7817"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C477CFB3C539FCC22FE40C394692AB4E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2701595D3C569138BD23806C9C928EAD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BDDD09B69C7AA30D84FBDB5FE93F6279"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_621C69B1A1B6976487780C408E34520B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AB0BD147C40B4C7645C317D6327ABBF2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DBB007E2726FBBE552E8E721DC0A7CC4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BD0187EC49096496978E30BB1CAAA520"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_67D66108E3EB27781C1ABFDD7136029A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FBDE6099085867266DC5CB61B7E238B0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6FB21A16DB42DC2F9EB11A533DF6A1DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_ED0A6BE52154A5EF6C04B786C25090FA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2FF7A565C17BCAFBD3625EF90161F725"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4A90FC383A9E5F8022555AA4F66FDA93"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_34556BAFA5A28C002A180B2BA0D0AEC7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F9820B7BB655AC7B9F86651AFE214460"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BE5DBD4B902A52D31AC3685F42C3B12E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_92D3CCB12FB6187ED430504EBF830A5F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C304FFFBD8CE8D59160B65AF6E548166"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D4F38A7C98E9B69097091CEC26CB19A6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AB3A08F03C0FAF389FA1CB802C64FBCC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_36AB572ED89117229B9A2ECE2F9B49C2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AE486BDD2D504C15500B989FCD8147F4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_363BAEB88A31960BAF9389E9F34FF7DB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_34DC43F3DEABEC450A61D965E2E8B353"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4739E473B6AC56195D9B75120EB604B8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BFF1B5F20BFF19D42DD352A4EB0BB4FB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9F5112B0239D8DFAA5C6D54AF2315E28"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B40FD6F7D97F535DEBCBC8867777EE18"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DAA728FEA9B4D2100F427BB61081D35F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_936730F6A70B421664E95406786E27A8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EB3EDBAC158A9AE0A2633E39156D8FE1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FB002B4C377ABE5F2770B1EBCD6C7397"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AD2492277B658B65C1B5BE6345B42049"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_05674C9ADDEE365B5593552E3B316BA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_801F20CE0A94B64242883AA55BDA8D14"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_09591403E4087838EA3F2F34026C1441"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_555E11C6A3DDD0B81D437D9281B5DEC0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4763927AF3131F63B13E54E887043F8B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A9562FD612552996807FC6990C065B25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_09C8D50F4BF056BEB1F08F12BB38457B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9266C439EAE14E4B124A11A4467E721D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0A216560CC06AF0A7F64AB1F109F9DF5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_021B15C3F2475E29EC440A15F81467F8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6E71DD56FECA059038EA13985CAB8AAB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DBA42F1C27F6393A1D5FC2ABD7D769A3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9EC285BB73273762E2016EBE0866AC25"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F25DBA22BFF32A354A4C31DA1093BCD8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D77742768346CCB57E071ACF0A7C7152"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_126EDD6274C456311F7C163E3C974C02"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E32115D80ACEEB75260DFC229A78C4C5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_27AC8F71DDC82B49B627DF338871FD5D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_01116C56E234C04A2A2B36358EC7CB8F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4EA34E2A97F12AB4CEC0525453702AEB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_45953C539E37FE533D6EE0B818936148"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2DE3F69777AFB7F3B459BDF95E938BA9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DDB993A79AADC922FCD91479D9EA4D8D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_45684A06814A203227C31CBBA231B19A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_97E3EE77D5E67D85EDAA188AE36F7001"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5645D3E37FAF3B07EC96A43EA8E783EA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4E925A9D0095283095398DA918B7D97E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_87A534DA776C77C1FE7BE1D24BF1D297"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3D7BCEB188A8C00FE92812A3725AEFB1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E9ECE188B5F010A7B8C47922094705BE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4DA01DEEADE419188F1732F0D1EAEE55"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5C660F39D4707FA05211F6C5062706F5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A73D4E4D544B63AD63743C21B319E7A4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7FE56FE4D9A6F76F5EEC984E9104F788"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_68D5B70F94AD4CCE9C6C0704E0746229"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AA3FF5E975C0F9900FE2DA9ED9CC5384"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_804C5D5F04A25CA1489DD52BB99DC92C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A8127A80B9D0F48E9A49EB072C7A81E1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1616002F5957B21EED3BF52D6A35C237"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_687C57558E93DCDD0E9F6F45580CF20E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DBF6F56357B26D457579A0A9495CB994"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A6DF26212CFC4E87B2EFD7C87687866A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CB96099B69163F32A6F2EFAD1CF46277"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D890AC617DBDD3B58ED4E41AF9C0C733"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5CBB1CDCDCA406F7E6F6901B8C5D3B87"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C1855EC708E88AAAE33F5264B000FD2A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FDC34B76F40430FB0368422A5F958B4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5D2EEF6BA2D2B7F2E47ECB4D747D64BD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_30D4601BE350FEA17188A874BA34F573"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BCC6CF914C98DA9278CAF6FDD2A7617D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2F9A47D599581989BC824AAFEFB6BFBB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0D7C78D4803C0D7CA67518C19C535DFB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3CDB28BB56B33ABE8E2ACDEE13C8651C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E8D0F873850832AC0677480E168D96AC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3F77991B6F8D91B15A2E275DF898FFFC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_62015BE741C4E0A4C431659024888C84"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7F5D7AC18E75311B4A31D1467B59D28A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_247D861C49210FF11ED13EBE0A7C08DB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5362A13F4B874227924A3CCF52DE8B18"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_71AB9050E3DF412B9FF88036401AF803"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2234E383DF64544008C83D682DB1076B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BB162602E0AE3775ADF708C5F97EE6C4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_99F851562409CD7A2A860922FC22790A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D2E47D216C633CEDC9A7CB62974BCBF4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7ED8135E7F31E1E1CE80B4BFFD7D400D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9543F55176F8F783A71663713E2E122D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_36D7883BF866DA0563DF0CFB944EA780"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2E6F931A0343E2B35FE5630A75DA8EB4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BA04A72048040EEA17BB4D3196109967"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FE7A898440DD5831CB7A35B6936EC614"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E7427E9E6F88DD56A3F1129C7DFF0A5E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4AF5A66CF3F49D93479C2B05FC16CD0F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_39DAA9A79C3315A73D3A379A59FB1893"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E75AAA1C4267C72B41DF09A1B085A069"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_643FE5EFFEA737A1C4E6A6DE115267C1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_472EBDB91799F7A1525A14722D095DC2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8305ED163A7EE1B015367F460B5A0E32"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4F27AA368C20359E6A0DA1487B10B23E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_38CAB68DFAC5A971C74DA4362BE14119"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4460A0DF8D8237BC09CBD663BE2032BC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2B67F8716052D51195901B5B7807908B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1806659EA99A722F63FE611E2D69C50B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EA367C10AFCCD06A622BA01B94216BB2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_248E7BC714094F30E86235420BDF5711"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_238DEFBF224C2DF83C1EFBF450B450B4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DA2142A10BFBE7DC87EC73733F380538"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EEEDBF171B3A985BA8CFF4B8DF3B59C9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CA78D837D340278E6BFBB8504151AC6D"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\MedicalR.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-**********"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:.NETFramework,Version=v4.7.2"
                    {
                    "Name" = "8:Microsoft .NET Framework 4.7.2 (x86 and x64)"
                    "ProductCode" = "8:.NETFramework,Version=v4.7.2"
                    }
                }
            }
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\MedicalR.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-**********"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
            "BootstrapperCfg:{63ACBE69-63AA-4F98-B2B6-99F9E24495F2}"
            {
            "Enabled" = "11:TRUE"
            "PromptEnabled" = "11:TRUE"
            "PrerequisitesLocation" = "2:1"
            "Url" = "8:"
            "ComponentsUrl" = "8:"
                "Items"
                {
                    "{EDC2488A-8267-493A-A98E-7D9C3B36CDF3}:.NETFramework,Version=v4.7.2"
                    {
                    "Name" = "8:Microsoft .NET Framework 4.7.2 (x86 and x64)"
                    "ProductCode" = "8:.NETFramework,Version=v4.7.2"
                    }
                }
            }
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_FA647E17E2264FA7BB50A15BA958427C"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:.NETFramework,Version=v4.7.2"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=863262"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_01116C56E234C04A2A2B36358EC7CB8F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_01116C56E234C04A2A2B36358EC7CB8F"
                    {
                    "Name" = "8:System.Runtime.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_021B15C3F2475E29EC440A15F81467F8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Xml, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_021B15C3F2475E29EC440A15F81467F8"
                    {
                    "Name" = "8:System.Runtime.Serialization.Xml.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Xml.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_05674C9ADDEE365B5593552E3B316BA3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Duplex, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_05674C9ADDEE365B5593552E3B316BA3"
                    {
                    "Name" = "8:System.ServiceModel.Duplex.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Duplex.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_09591403E4087838EA3F2F34026C1441"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Principal, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_09591403E4087838EA3F2F34026C1441"
                    {
                    "Name" = "8:System.Security.Principal.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Principal.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_09C8D50F4BF056BEB1F08F12BB38457B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Csp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_09C8D50F4BF056BEB1F08F12BB38457B"
                    {
                    "Name" = "8:System.Security.Cryptography.Csp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Csp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0A216560CC06AF0A7F64AB1F109F9DF5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Claims, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_0A216560CC06AF0A7F64AB1F109F9DF5"
                    {
                    "Name" = "8:System.Security.Claims.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Claims.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0D7C78D4803C0D7CA67518C19C535DFB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem.Watcher, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_0D7C78D4803C0D7CA67518C19C535DFB"
                    {
                    "Name" = "8:System.IO.FileSystem.Watcher.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.Watcher.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_126EDD6274C456311F7C163E3C974C02"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_126EDD6274C456311F7C163E3C974C02"
                    {
                    "Name" = "8:System.Runtime.InteropServices.RuntimeInformation.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.RuntimeInformation.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_157CAE2A4DE642106E2D035F89F2464E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.RichEdit.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_157CAE2A4DE642106E2D035F89F2464E"
                    {
                    "Name" = "8:DevExpress.RichEdit.v23.1.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.RichEdit.v23.1.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1616002F5957B21EED3BF52D6A35C237"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Ping, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1616002F5957B21EED3BF52D6A35C237"
                    {
                    "Name" = "8:System.Net.Ping.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Ping.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1806659EA99A722F63FE611E2D69C50B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1806659EA99A722F63FE611E2D69C50B"
                    {
                    "Name" = "8:System.AppContext.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.AppContext.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_1F67927C1D3A4E978D7BB9BC134D79C8"
            {
            "SourcePath" = "8:E:\\谷歌下载\\favicon_io (1)\\favicon.ico"
            "TargetName" = "8:favicon.ico"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2234E383DF64544008C83D682DB1076B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Dynamic.Runtime, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2234E383DF64544008C83D682DB1076B"
                    {
                    "Name" = "8:System.Dynamic.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Dynamic.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_238DEFBF224C2DF83C1EFBF450B450B4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression.FileSystem, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_238DEFBF224C2DF83C1EFBF450B450B4"
                    {
                    "Name" = "8:System.IO.Compression.FileSystem.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.FileSystem.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_247D861C49210FF11ED13EBE0A7C08DB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Globalization.Extensions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_247D861C49210FF11ED13EBE0A7C08DB"
                    {
                    "Name" = "8:System.Globalization.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_248E7BC714094F30E86235420BDF5711"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_248E7BC714094F30E86235420BDF5711"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2701595D3C569138BD23806C9C928EAD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Drawing.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2701595D3C569138BD23806C9C928EAD"
                    {
                    "Name" = "8:DevExpress.Drawing.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Drawing.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_27AC8F71DDC82B49B627DF338871FD5D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Handles, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_27AC8F71DDC82B49B627DF338871FD5D"
                    {
                    "Name" = "8:System.Runtime.Handles.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Handles.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_290DD9C5FEA1A536D63AC42125E925AC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraEditors.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_290DD9C5FEA1A536D63AC42125E925AC"
                    {
                    "Name" = "8:DevExpress.XtraEditors.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraEditors.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2B67F8716052D51195901B5B7807908B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections.Concurrent, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2B67F8716052D51195901B5B7807908B"
                    {
                    "Name" = "8:System.Collections.Concurrent.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.Concurrent.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2C139D4C7E604679A2EB3D97FD3A8B38"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraScheduler.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_2C139D4C7E604679A2EB3D97FD3A8B38"
                    {
                    "Name" = "8:DevExpress.XtraScheduler.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraScheduler.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2DE3F69777AFB7F3B459BDF95E938BA9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Resources.Writer, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2DE3F69777AFB7F3B459BDF95E938BA9"
                    {
                    "Name" = "8:System.Resources.Writer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.Writer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2E6F931A0343E2B35FE5630A75DA8EB4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.FileVersionInfo, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2E6F931A0343E2B35FE5630A75DA8EB4"
                    {
                    "Name" = "8:System.Diagnostics.FileVersionInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.FileVersionInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2F9A47D599581989BC824AAFEFB6BFBB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.IsolatedStorage, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2F9A47D599581989BC824AAFEFB6BFBB"
                    {
                    "Name" = "8:System.IO.IsolatedStorage.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.IsolatedStorage.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2FF7A565C17BCAFBD3625EF90161F725"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2FF7A565C17BCAFBD3625EF90161F725"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_30D4601BE350FEA17188A874BA34F573"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.Pipes, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_30D4601BE350FEA17188A874BA34F573"
                    {
                    "Name" = "8:System.IO.Pipes.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Pipes.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_34556BAFA5A28C002A180B2BA0D0AEC7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XPath, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_34556BAFA5A28C002A180B2BA0D0AEC7"
                    {
                    "Name" = "8:System.Xml.XPath.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XPath.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_34DC43F3DEABEC450A61D965E2E8B353"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_34DC43F3DEABEC450A61D965E2E8B353"
                    {
                    "Name" = "8:System.Threading.Tasks.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_363BAEB88A31960BAF9389E9F34FF7DB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks.Parallel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_363BAEB88A31960BAF9389E9F34FF7DB"
                    {
                    "Name" = "8:System.Threading.Tasks.Parallel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.Parallel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_36AB572ED89117229B9A2ECE2F9B49C2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.ThreadPool, Version=4.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_36AB572ED89117229B9A2ECE2F9B49C2"
                    {
                    "Name" = "8:System.Threading.ThreadPool.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.ThreadPool.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_36D7883BF866DA0563DF0CFB944EA780"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Process, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_36D7883BF866DA0563DF0CFB944EA780"
                    {
                    "Name" = "8:System.Diagnostics.Process.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Process.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_38CAB68DFAC5A971C74DA4362BE14119"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections.NonGeneric, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_38CAB68DFAC5A971C74DA4362BE14119"
                    {
                    "Name" = "8:System.Collections.NonGeneric.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.NonGeneric.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_39DAA9A79C3315A73D3A379A59FB1893"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.TypeConverter, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_39DAA9A79C3315A73D3A379A59FB1893"
                    {
                    "Name" = "8:System.ComponentModel.TypeConverter.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.TypeConverter.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_3BD4C045128141E088ADA6E1838B8B91"
            {
            "SourcePath" = "8:C:\\Windows\\System32\\msiexec.exe"
            "TargetName" = "8:卸载 MyApp"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3CDB28BB56B33ABE8E2ACDEE13C8651C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3CDB28BB56B33ABE8E2ACDEE13C8651C"
                    {
                    "Name" = "8:System.IO.FileSystem.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3D7BCEB188A8C00FE92812A3725AEFB1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Emit, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3D7BCEB188A8C00FE92812A3725AEFB1"
                    {
                    "Name" = "8:System.Reflection.Emit.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Emit.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3F77991B6F8D91B15A2E275DF898FFFC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3F77991B6F8D91B15A2E275DF898FFFC"
                    {
                    "Name" = "8:System.IO.FileSystem.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4298E84A9F356F8BDFABF6403EE4340B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraLayout.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4298E84A9F356F8BDFABF6403EE4340B"
                    {
                    "Name" = "8:DevExpress.XtraLayout.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraLayout.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_439B01AE55DC4552BDF759513C4E3746"
            {
            "SourcePath" = "8:..\\MedicalDisinfectionSupplyCenter\\Resources\\aed38304-f7ac-46bf-a600-eaa2d9725da1.png"
            "TargetName" = "8:aed38304-f7ac-46bf-a600-eaa2d9725da1.png"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4460A0DF8D8237BC09CBD663BE2032BC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4460A0DF8D8237BC09CBD663BE2032BC"
                    {
                    "Name" = "8:System.Collections.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_45684A06814A203227C31CBBA231B19A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Resources.Reader, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_45684A06814A203227C31CBBA231B19A"
                    {
                    "Name" = "8:System.Resources.Reader.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.Reader.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_45953C539E37FE533D6EE0B818936148"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.CompilerServices.VisualC, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_45953C539E37FE533D6EE0B818936148"
                    {
                    "Name" = "8:System.Runtime.CompilerServices.VisualC.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.CompilerServices.VisualC.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_472EBDB91799F7A1525A14722D095DC2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_472EBDB91799F7A1525A14722D095DC2"
                    {
                    "Name" = "8:System.ComponentModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4739E473B6AC56195D9B75120EB604B8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Overlapped, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4739E473B6AC56195D9B75120EB604B8"
                    {
                    "Name" = "8:System.Threading.Overlapped.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Overlapped.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4763927AF3131F63B13E54E887043F8B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4763927AF3131F63B13E54E887043F8B"
                    {
                    "Name" = "8:System.Security.Cryptography.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4A90FC383A9E5F8022555AA4F66FDA93"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XPath.XDocument, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4A90FC383A9E5F8022555AA4F66FDA93"
                    {
                    "Name" = "8:System.Xml.XPath.XDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XPath.XDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4AF5A66CF3F49D93479C2B05FC16CD0F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Console, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4AF5A66CF3F49D93479C2B05FC16CD0F"
                    {
                    "Name" = "8:System.Console.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Console.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4BC939A2D9C855961F487DEBF3841A4B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Sparkline.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4BC939A2D9C855961F487DEBF3841A4B"
                    {
                    "Name" = "8:DevExpress.Sparkline.v23.1.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Sparkline.v23.1.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4C1F6AB8F1923BBCBDC76D164F9D7817"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Pdf.v23.1.Drawing, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4C1F6AB8F1923BBCBDC76D164F9D7817"
                    {
                    "Name" = "8:DevExpress.Pdf.v23.1.Drawing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Pdf.v23.1.Drawing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4DA01DEEADE419188F1732F0D1EAEE55"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ObjectModel, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4DA01DEEADE419188F1732F0D1EAEE55"
                    {
                    "Name" = "8:System.ObjectModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ObjectModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4E925A9D0095283095398DA918B7D97E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Emit.Lightweight, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4E925A9D0095283095398DA918B7D97E"
                    {
                    "Name" = "8:System.Reflection.Emit.Lightweight.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Emit.Lightweight.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4EA34E2A97F12AB4CEC0525453702AEB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4EA34E2A97F12AB4CEC0525453702AEB"
                    {
                    "Name" = "8:System.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4F27AA368C20359E6A0DA1487B10B23E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections.Specialized, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4F27AA368C20359E6A0DA1487B10B23E"
                    {
                    "Name" = "8:System.Collections.Specialized.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.Specialized.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5362A13F4B874227924A3CCF52DE8B18"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Globalization, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5362A13F4B874227924A3CCF52DE8B18"
                    {
                    "Name" = "8:System.Globalization.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_555E11C6A3DDD0B81D437D9281B5DEC0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_555E11C6A3DDD0B81D437D9281B5DEC0"
                    {
                    "Name" = "8:System.Security.Cryptography.X509Certificates.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.X509Certificates.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5645D3E37FAF3B07EC96A43EA8E783EA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5645D3E37FAF3B07EC96A43EA8E783EA"
                    {
                    "Name" = "8:System.Reflection.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5C660F39D4707FA05211F6C5062706F5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.WebSockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5C660F39D4707FA05211F6C5062706F5"
                    {
                    "Name" = "8:System.Net.WebSockets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.WebSockets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5CBB1CDCDCA406F7E6F6901B8C5D3B87"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Parallel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5CBB1CDCDCA406F7E6F6901B8C5D3B87"
                    {
                    "Name" = "8:System.Linq.Parallel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Parallel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5D1AFC591F9D476F629D7699D304EF75"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraGrid.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5D1AFC591F9D476F629D7699D304EF75"
                    {
                    "Name" = "8:DevExpress.XtraGrid.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraGrid.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5D2EEF6BA2D2B7F2E47ECB4D747D64BD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.UnmanagedMemoryStream, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5D2EEF6BA2D2B7F2E47ECB4D747D64BD"
                    {
                    "Name" = "8:System.IO.UnmanagedMemoryStream.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.UnmanagedMemoryStream.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_62015BE741C4E0A4C431659024888C84"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_62015BE741C4E0A4C431659024888C84"
                    {
                    "Name" = "8:System.IO.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_621C69B1A1B6976487780C408E34520B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http.Formatting, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_621C69B1A1B6976487780C408E34520B"
                    {
                    "Name" = "8:System.Net.Http.Formatting.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.Formatting.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_643FE5EFFEA737A1C4E6A6DE115267C1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.EventBasedAsync, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_643FE5EFFEA737A1C4E6A6DE115267C1"
                    {
                    "Name" = "8:System.ComponentModel.EventBasedAsync.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.EventBasedAsync.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_67D66108E3EB27781C1ABFDD7136029A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_67D66108E3EB27781C1ABFDD7136029A"
                    {
                    "Name" = "8:System.Numerics.Vectors.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Numerics.Vectors.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_687C57558E93DCDD0E9F6F45580CF20E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.NetworkInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_687C57558E93DCDD0E9F6F45580CF20E"
                    {
                    "Name" = "8:System.Net.NetworkInformation.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.NetworkInformation.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_68D5B70F94AD4CCE9C6C0704E0746229"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Sockets, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_68D5B70F94AD4CCE9C6C0704E0746229"
                    {
                    "Name" = "8:System.Net.Sockets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Sockets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6E71DD56FECA059038EA13985CAB8AAB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Primitives, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_6E71DD56FECA059038EA13985CAB8AAB"
                    {
                    "Name" = "8:System.Runtime.Serialization.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6FB21A16DB42DC2F9EB11A533DF6A1DF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Newtonsoft.Json.Bson, Version=1.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6FB21A16DB42DC2F9EB11A533DF6A1DF"
                    {
                    "Name" = "8:Newtonsoft.Json.Bson.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Newtonsoft.Json.Bson.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_71AB9050E3DF412B9FF88036401AF803"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_71AB9050E3DF412B9FF88036401AF803"
                    {
                    "Name" = "8:System.Globalization.Calendars.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.Calendars.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_737B308408F772285C78254731C26120"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraScheduler.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_737B308408F772285C78254731C26120"
                    {
                    "Name" = "8:DevExpress.XtraScheduler.v23.1.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraScheduler.v23.1.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_763BF6C7C4438DBF0F1F15825CAB1333"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Utils.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_763BF6C7C4438DBF0F1F15825CAB1333"
                    {
                    "Name" = "8:DevExpress.Utils.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Utils.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_791F83E1E3D4F5954E54C176FFD15E60"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraScheduler.v23.1.Core.Desktop, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_791F83E1E3D4F5954E54C176FFD15E60"
                    {
                    "Name" = "8:DevExpress.XtraScheduler.v23.1.Core.Desktop.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraScheduler.v23.1.Core.Desktop.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7ED8135E7F31E1E1CE80B4BFFD7D400D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.TextWriterTraceListener, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_7ED8135E7F31E1E1CE80B4BFFD7D400D"
                    {
                    "Name" = "8:System.Diagnostics.TextWriterTraceListener.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.TextWriterTraceListener.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7F5D7AC18E75311B4A31D1467B59D28A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
                "ScatterAssemblies"
                {
                    "_7F5D7AC18E75311B4A31D1467B59D28A"
                    {
                    "Name" = "8:System.IO.Compression.ZipFile.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.ZipFile.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7FE56FE4D9A6F76F5EEC984E9104F788"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.WebHeaderCollection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_7FE56FE4D9A6F76F5EEC984E9104F788"
                    {
                    "Name" = "8:System.Net.WebHeaderCollection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.WebHeaderCollection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_801F20CE0A94B64242883AA55BDA8D14"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.SecureString, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_801F20CE0A94B64242883AA55BDA8D14"
                    {
                    "Name" = "8:System.Security.SecureString.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.SecureString.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_804C5D5F04A25CA1489DD52BB99DC92C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Requests, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_804C5D5F04A25CA1489DD52BB99DC92C"
                    {
                    "Name" = "8:System.Net.Requests.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Requests.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8305ED163A7EE1B015367F460B5A0E32"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.Annotations, Version=4.0.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_8305ED163A7EE1B015367F460B5A0E32"
                    {
                    "Name" = "8:System.ComponentModel.Annotations.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.Annotations.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_87A534DA776C77C1FE7BE1D24BF1D297"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Emit.ILGeneration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_87A534DA776C77C1FE7BE1D24BF1D297"
                    {
                    "Name" = "8:System.Reflection.Emit.ILGeneration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Emit.ILGeneration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9266C439EAE14E4B124A11A4467E721D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Algorithms, Version=4.3.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9266C439EAE14E4B124A11A4467E721D"
                    {
                    "Name" = "8:System.Security.Cryptography.Algorithms.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Algorithms.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_92D3CCB12FB6187ED430504EBF830A5F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XDocument, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_92D3CCB12FB6187ED430504EBF830A5F"
                    {
                    "Name" = "8:System.Xml.XDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_936730F6A70B421664E95406786E27A8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Security, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_936730F6A70B421664E95406786E27A8"
                    {
                    "Name" = "8:System.ServiceModel.Security.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Security.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9543F55176F8F783A71663713E2E122D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.StackTrace, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9543F55176F8F783A71663713E2E122D"
                    {
                    "Name" = "8:System.Diagnostics.StackTrace.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.StackTrace.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_97E3EE77D5E67D85EDAA188AE36F7001"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_97E3EE77D5E67D85EDAA188AE36F7001"
                    {
                    "Name" = "8:System.Reflection.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_991088D7112AD03EB68692BF3E75CDEE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.BonusSkins.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_991088D7112AD03EB68692BF3E75CDEE"
                    {
                    "Name" = "8:DevExpress.BonusSkins.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.BonusSkins.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_9966FACBB79A4B279B63DFDBEF861660"
            {
            "SourcePath" = "8:..\\MedicalDisinfectionSupplyCenter\\App.config"
            "TargetName" = "8:App.config"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_99F851562409CD7A2A860922FC22790A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.TraceSource, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_99F851562409CD7A2A860922FC22790A"
                    {
                    "Name" = "8:System.Diagnostics.TraceSource.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.TraceSource.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9EC285BB73273762E2016EBE0866AC25"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Formatters, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9EC285BB73273762E2016EBE0866AC25"
                    {
                    "Name" = "8:System.Runtime.Serialization.Formatters.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Formatters.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9F5112B0239D8DFAA5C6D54AF2315E28"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Text.RegularExpressions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9F5112B0239D8DFAA5C6D54AF2315E28"
                    {
                    "Name" = "8:System.Text.RegularExpressions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.RegularExpressions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A6DF26212CFC4E87B2EFD7C87687866A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http.Rtc, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A6DF26212CFC4E87B2EFD7C87687866A"
                    {
                    "Name" = "8:System.Net.Http.Rtc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.Rtc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A73D4E4D544B63AD63743C21B319E7A4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.WebSockets.Client, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A73D4E4D544B63AD63743C21B319E7A4"
                    {
                    "Name" = "8:System.Net.WebSockets.Client.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.WebSockets.Client.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A8127A80B9D0F48E9A49EB072C7A81E1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Primitives, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A8127A80B9D0F48E9A49EB072C7A81E1"
                    {
                    "Name" = "8:System.Net.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A9562FD612552996807FC6990C065B25"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Encoding, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A9562FD612552996807FC6990C065B25"
                    {
                    "Name" = "8:System.Security.Cryptography.Encoding.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Encoding.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AA3FF5E975C0F9900FE2DA9ED9CC5384"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Security, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AA3FF5E975C0F9900FE2DA9ED9CC5384"
                    {
                    "Name" = "8:System.Net.Security.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Security.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AB0BD147C40B4C7645C317D6327ABBF2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_AB0BD147C40B4C7645C317D6327ABBF2"
                    {
                    "Name" = "8:System.Threading.Tasks.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AB3A08F03C0FAF389FA1CB802C64FBCC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Timer, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AB3A08F03C0FAF389FA1CB802C64FBCC"
                    {
                    "Name" = "8:System.Threading.Timer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Timer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AD2492277B658B65C1B5BE6345B42049"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Http, Version=4.0.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AD2492277B658B65C1B5BE6345B42049"
                    {
                    "Name" = "8:System.ServiceModel.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AE486BDD2D504C15500B989FCD8147F4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Thread, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AE486BDD2D504C15500B989FCD8147F4"
                    {
                    "Name" = "8:System.Threading.Thread.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Thread.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B39F8D3FE657AFD0F926C1FBEFD8C84B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Data.Desktop.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B39F8D3FE657AFD0F926C1FBEFD8C84B"
                    {
                    "Name" = "8:DevExpress.Data.Desktop.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Data.Desktop.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B40FD6F7D97F535DEBCBC8867777EE18"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Text.Encoding.Extensions, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_B40FD6F7D97F535DEBCBC8867777EE18"
                    {
                    "Name" = "8:System.Text.Encoding.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.Encoding.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BA04A72048040EEA17BB4D3196109967"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Debug, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_BA04A72048040EEA17BB4D3196109967"
                    {
                    "Name" = "8:System.Diagnostics.Debug.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Debug.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BB162602E0AE3775ADF708C5F97EE6C4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Drawing.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_BB162602E0AE3775ADF708C5F97EE6C4"
                    {
                    "Name" = "8:System.Drawing.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Drawing.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BCC6CF914C98DA9278CAF6FDD2A7617D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.MemoryMappedFiles, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_BCC6CF914C98DA9278CAF6FDD2A7617D"
                    {
                    "Name" = "8:System.IO.MemoryMappedFiles.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.MemoryMappedFiles.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BD0187EC49096496978E30BB1CAAA520"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BD0187EC49096496978E30BB1CAAA520"
                    {
                    "Name" = "8:System.Runtime.CompilerServices.Unsafe.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.CompilerServices.Unsafe.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BDDD09B69C7AA30D84FBDB5FE93F6279"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Data.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BDDD09B69C7AA30D84FBDB5FE93F6279"
                    {
                    "Name" = "8:DevExpress.Data.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Data.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BE5DBD4B902A52D31AC3685F42C3B12E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XmlDocument, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_BE5DBD4B902A52D31AC3685F42C3B12E"
                    {
                    "Name" = "8:System.Xml.XmlDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XmlDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BFF1B5F20BFF19D42DD352A4EB0BB4FB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_BFF1B5F20BFF19D42DD352A4EB0BB4FB"
                    {
                    "Name" = "8:System.Threading.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C1855EC708E88AAAE33F5264B000FD2A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C1855EC708E88AAAE33F5264B000FD2A"
                    {
                    "Name" = "8:System.Linq.Expressions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Expressions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C250F21708B124F2F3221C01B4541BAF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Images.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C250F21708B124F2F3221C01B4541BAF"
                    {
                    "Name" = "8:DevExpress.Images.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Images.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C304FFFBD8CE8D59160B65AF6E548166"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.ReaderWriter, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C304FFFBD8CE8D59160B65AF6E548166"
                    {
                    "Name" = "8:System.Xml.ReaderWriter.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.ReaderWriter.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C477CFB3C539FCC22FE40C394692AB4E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Pdf.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C477CFB3C539FCC22FE40C394692AB4E"
                    {
                    "Name" = "8:DevExpress.Pdf.v23.1.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Pdf.v23.1.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C89900F8E226F14A5BE3B8E3FFAAD974"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraBars.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C89900F8E226F14A5BE3B8E3FFAAD974"
                    {
                    "Name" = "8:DevExpress.XtraBars.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraBars.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CA78D837D340278E6BFBB8504151AC6D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_CA78D837D340278E6BFBB8504151AC6D"
                    {
                    "Name" = "8:Microsoft.Win32.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Win32.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CAC06910BE144D39AD8DBAAA531956F5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraPrinting.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CAC06910BE144D39AD8DBAAA531956F5"
                    {
                    "Name" = "8:DevExpress.XtraPrinting.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraPrinting.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CB96099B69163F32A6F2EFAD1CF46277"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http.WebRequest, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CB96099B69163F32A6F2EFAD1CF46277"
                    {
                    "Name" = "8:System.Net.Http.WebRequest.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.WebRequest.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D2E47D216C633CEDC9A7CB62974BCBF4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Tools, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_D2E47D216C633CEDC9A7CB62974BCBF4"
                    {
                    "Name" = "8:System.Diagnostics.Tools.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Tools.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D4F38A7C98E9B69097091CEC26CB19A6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51"
                "ScatterAssemblies"
                {
                    "_D4F38A7C98E9B69097091CEC26CB19A6"
                    {
                    "Name" = "8:System.ValueTuple.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ValueTuple.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D77742768346CCB57E071ACF0A7C7152"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices.WindowsRuntime, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_D77742768346CCB57E071ACF0A7C7152"
                    {
                    "Name" = "8:System.Runtime.InteropServices.WindowsRuntime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.WindowsRuntime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D890AC617DBDD3B58ED4E41AF9C0C733"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Queryable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_D890AC617DBDD3B58ED4E41AF9C0C733"
                    {
                    "Name" = "8:System.Linq.Queryable.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Queryable.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DA2142A10BFBE7DC87EC73733F380538"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DA2142A10BFBE7DC87EC73733F380538"
                    {
                    "Name" = "8:System.IO.Compression.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DAA728FEA9B4D2100F427BB61081D35F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Text.Encoding, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DAA728FEA9B4D2100F427BB61081D35F"
                    {
                    "Name" = "8:System.Text.Encoding.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.Encoding.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DBA42F1C27F6393A1D5FC2ABD7D769A3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Json, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DBA42F1C27F6393A1D5FC2ABD7D769A3"
                    {
                    "Name" = "8:System.Runtime.Serialization.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DBB007E2726FBBE552E8E721DC0A7CC4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DBB007E2726FBBE552E8E721DC0A7CC4"
                    {
                    "Name" = "8:System.Memory.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Memory.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DBF6F56357B26D457579A0A9495CB994"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.NameResolution, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DBF6F56357B26D457579A0A9495CB994"
                    {
                    "Name" = "8:System.Net.NameResolution.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.NameResolution.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DDB993A79AADC922FCD91479D9EA4D8D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Resources.ResourceManager, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DDB993A79AADC922FCD91479D9EA4D8D"
                    {
                    "Name" = "8:System.Resources.ResourceManager.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.ResourceManager.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E32115D80ACEEB75260DFC229A78C4C5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E32115D80ACEEB75260DFC229A78C4C5"
                    {
                    "Name" = "8:System.Runtime.InteropServices.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E445154B9CC29F8AEDC66251B6349133"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Printing.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E445154B9CC29F8AEDC66251B6349133"
                    {
                    "Name" = "8:DevExpress.Printing.v23.1.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Printing.v23.1.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E7427E9E6F88DD56A3F1129C7DFF0A5E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Data.Common, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E7427E9E6F88DD56A3F1129C7DFF0A5E"
                    {
                    "Name" = "8:System.Data.Common.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Data.Common.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E75AAA1C4267C72B41DF09A1B085A069"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E75AAA1C4267C72B41DF09A1B085A069"
                    {
                    "Name" = "8:System.ComponentModel.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E8D0F873850832AC0677480E168D96AC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem.DriveInfo, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E8D0F873850832AC0677480E168D96AC"
                    {
                    "Name" = "8:System.IO.FileSystem.DriveInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.DriveInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E9ECE188B5F010A7B8C47922094705BE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E9ECE188B5F010A7B8C47922094705BE"
                    {
                    "Name" = "8:System.Reflection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EA367C10AFCCD06A622BA01B94216BB2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51"
                "ScatterAssemblies"
                {
                    "_EA367C10AFCCD06A622BA01B94216BB2"
                    {
                    "Name" = "8:netstandard.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:netstandard.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EAAAD6D75C8C81710D768DB31D065D9B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.XtraTreeList.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_EAAAD6D75C8C81710D768DB31D065D9B"
                    {
                    "Name" = "8:DevExpress.XtraTreeList.v23.1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.XtraTreeList.v23.1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EB3EDBAC158A9AE0A2633E39156D8FE1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Primitives, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_EB3EDBAC158A9AE0A2633E39156D8FE1"
                    {
                    "Name" = "8:System.ServiceModel.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EB604B801EB0449CF5ED34AC426A2E29"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:DevExpress.Office.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_EB604B801EB0449CF5ED34AC426A2E29"
                    {
                    "Name" = "8:DevExpress.Office.v23.1.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:DevExpress.Office.v23.1.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_ED0A6BE52154A5EF6C04B786C25090FA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_ED0A6BE52154A5EF6C04B786C25090FA"
                    {
                    "Name" = "8:Newtonsoft.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Newtonsoft.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EEEDBF171B3A985BA8CFF4B8DF3B59C9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Tracing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_EEEDBF171B3A985BA8CFF4B8DF3B59C9"
                    {
                    "Name" = "8:System.Diagnostics.Tracing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Tracing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F25DBA22BFF32A354A4C31DA1093BCD8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Numerics, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F25DBA22BFF32A354A4C31DA1093BCD8"
                    {
                    "Name" = "8:System.Runtime.Numerics.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Numerics.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{1FB2D0AE-D3B9-43D4-B9DD-F88EC61E35DE}:_F5A5CF6648D84D94BE5C2CB3E0C2B174"
            {
            "SourcePath" = "8:..\\MedicalDisinfectionSupplyCenter\\Resources\\QQ_1753057689866.png"
            "TargetName" = "8:QQ_1753057689866.png"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F9820B7BB655AC7B9F86651AFE214460"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XmlSerializer, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F9820B7BB655AC7B9F86651AFE214460"
                    {
                    "Name" = "8:System.Xml.XmlSerializer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XmlSerializer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FB002B4C377ABE5F2770B1EBCD6C7397"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.NetTcp, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_FB002B4C377ABE5F2770B1EBCD6C7397"
                    {
                    "Name" = "8:System.ServiceModel.NetTcp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.NetTcp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FBDE6099085867266DC5CB61B7E238B0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FBDE6099085867266DC5CB61B7E238B0"
                    {
                    "Name" = "8:System.Buffers.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Buffers.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FDC34B76F40430FB0368422A5F958B4A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_FDC34B76F40430FB0368422A5F958B4A"
                    {
                    "Name" = "8:System.Linq.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FE7A898440DD5831CB7A35B6936EC614"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Contracts, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_FE7A898440DD5831CB7A35B6936EC614"
                    {
                    "Name" = "8:System.Diagnostics.Contracts.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Contracts.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_1053653EBC504752876B604399972886"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_40CDB29C885E4FAA86E2039CFADAB393"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_979C6CF6FFEF467098153467FAF4B495"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:2052"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:MedicalRDisinfection"
        "ProductCode" = "8:{B5B32766-DC95-46CA-A68C-96DC25648D2D}"
        "PackageCode" = "8:{41DE7928-20CF-4719-988E-E666CD4AE3A7}"
        "UpgradeCode" = "8:{29EBA43F-6D94-48D1-ACB7-FB3DD25A2832}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:FALSE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:FALSE"
        "ProductVersion" = "8:1.0.0"
        "Manufacturer" = "8:Medical"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:医疗消毒系统安装"
        "Subject" = "8:"
        "ARPCONTACT" = "8:Default Company Name"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_F4C290BD4C8B4010B49825E7D2327D3E"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_EE3602F7D37A4DADB4277FD88706DB63"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_7D909C2D4D2D41EF8CCA892A23354272"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_25B05BC5031C410FB41239F2B1AE14C6"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_3107ABACDE1346A8916D5A949D1C65E9"
                            {
                            "Name" = "8:MyApp"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                    "{ADCFDA98-8FDD-45E4-90BC-E3D20B029870}:_2E427D0E09E347408A74D48F7E7131A2"
                                    {
                                    "Name" = "8:InstallPath"
                                    "Condition" = "8:"
                                    "Transitive" = "11:FALSE"
                                    "ValueTypes" = "3:1"
                                    "Value" = "8:[TARGETDIR] "
                                    }
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_8E7ADC511F654BC6B2B3CD1E3539FC18"
            {
            "Name" = "8:卸载 智慧医疗消毒系统"
            "Arguments" = "8:/x {ProductCode}"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_3BD4C045128141E088ADA6E1838B8B91"
            "Folder" = "8:_979C6CF6FFEF467098153467FAF4B495"
            "WorkingFolder" = "8:_1053653EBC504752876B604399972886"
            "Icon" = "8:"
            "Feature" = "8:"
            }
            "{970C0BB2-C7D0-45D7-ABFA-7EC378858BC0}:_F80BD2A521EC428189A4AB76CB208CA7"
            {
            "Name" = "8:智慧医疗消毒系统"
            "Arguments" = "8:"
            "Description" = "8:"
            "ShowCmd" = "3:1"
            "IconIndex" = "3:0"
            "Transitive" = "11:FALSE"
            "Target" = "8:_83BB8AC14D4A441394082649F05E2BEE"
            "Folder" = "8:_40CDB29C885E4FAA86E2039CFADAB393"
            "WorkingFolder" = "8:_1053653EBC504752876B604399972886"
            "Icon" = "8:_1F67927C1D3A4E978D7BB9BC134D79C8"
            "Feature" = "8:"
            }
        }
        "UserInterface"
        {
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_04A3F4BA40D1489CB8A2E8BEBCD41C66"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_544A64DE1D07402AAB1368B52811B5C0"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:确认安装"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_BDF6B8DA4E404E82B2E6FE6AC8E1BED3"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:欢迎使用"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_BE67899120F54991B3B04B9CEAF5FF11"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:安装文件夹"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_1EDE8E721AEF452B9922761BA71BD048"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_A6A8CA5311E448FDA4C70FAE2334AF69"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:进度"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_32F4779373FB4CB5830CC4023A1544C9"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_66EB72292CEF447896DB87C929EB93EC"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:已完成"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_75F591D195514338924D3A88B748FDC0"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_8E1388EA3F934BA2A93999446D26D3DE"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_40D3D6AAB99F476DBAE3FA33AB2A47C0"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:欢迎使用"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_82A806A74D5046DFB4E72E53D6DBAC8E"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:确认安装"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_ED69C17320EE4E1C8FFB7F0CCA4BE880"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:安装文件夹"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_C206B82663404FE08638BAEE1EFD61C4"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_A855017372EC4250AEBE9591101310DD"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:进度"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_CD6219C249664ED78865AC76DCFC42AC"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_45DD0FAB45AB4A3A8F5552D59631E1A3"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:已完成"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_F749EED1AEB642959394A90BBAB38670"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_4C65F352CBC6453CB631C236C7A99E25"
            {
            "SourcePath" = "8:..\\MedicalDisinfectionSupplyCenter\\obj\\Debug\\MedicalDisinfectionSupplyCenter.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Built"
            "OutputProjectGuid" = "8:{A7BB82A2-6831-4F1C-A18D-946CF73724D3}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_83BB8AC14D4A441394082649F05E2BEE"
            {
            "SourcePath" = "8:..\\MedicalDisinfectionSupplyCenter\\obj\\Debug\\MedicalDisinfectionSupplyCenter.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_1053653EBC504752876B604399972886"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Built"
            "OutputProjectGuid" = "8:{A7BB82A2-6831-4F1C-A18D-946CF73724D3}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
        }
    }
}

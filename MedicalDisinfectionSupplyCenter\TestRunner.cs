using System;
using System.Threading.Tasks;
using MedicalDisinfectionSupplyCenter.PackagingSterilization;

namespace MedicalDisinfectionSupplyCenter
{
    /// <summary>
    /// 测试运行器
    /// </summary>
    class TestRunner
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("医疗消毒供应中心 - 包装登记功能测试");
            Console.WriteLine("==========================================");
            
            try
            {
                // 运行所有测试
                bool testResult = await PackagingRegistrationTest.RunAllTests();
                
                Console.WriteLine();
                Console.WriteLine("==========================================");
                if (testResult)
                {
                    Console.WriteLine("✓ 所有测试通过！包装登记功能实现正确。");
                }
                else
                {
                    Console.WriteLine("✗ 部分测试失败，请检查实现。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试运行出错: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

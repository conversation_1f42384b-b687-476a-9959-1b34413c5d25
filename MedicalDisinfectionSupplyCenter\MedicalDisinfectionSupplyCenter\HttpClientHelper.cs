using System.Threading.Tasks;
using System.Net.Http.Headers;
using System.Net.Http;

namespace WinFormsAppDemo2.Common
{
    public static class HttpClientHelper
    {
        /// <summary>
        /// Get/Post/Put/Delete(delete是通过url传值的方式)
        /// </summary>
        /// <param name="method">请求方式</param>
        /// <param name="url">请求地址</param>
        /// <param name="isAuthorzation">是否授权</param>
        /// <param name="content">参数内容</param>
        /// <returns></returns>
        public static async Task<string> ClientAsync(string method, string url, bool isAuthorzation = false, HttpContent content = null)
        {
            var result = "";
            using (HttpClient client = new HttpClient())
            {
                if (isAuthorzation)
                {
                    AuthenticationHeaderValue authentication = new AuthenticationHeaderValue(
                    "Bearer",
                    TokenDto.Token);
                    client.DefaultRequestHeaders.Authorization = authentication;
                }
                HttpResponseMessage httpResponseMessage = null;

                if (method.ToUpper() == "GET")
                {
                    httpResponseMessage = client.GetAsync(url).Result;
                }
                else if (method.ToUpper() == "POST")
                {
                    httpResponseMessage = client.PostAsync(url, content).Result;
                }
                else if (method.ToUpper() == "PUT")
                {
                    httpResponseMessage = client.PutAsync(url, content).Result;
                }
                else if (method.ToUpper() == "DELETE")
                {
                    httpResponseMessage = client.DeleteAsync(url).Result;
                }
                if (httpResponseMessage != null)
                {
                    //判断请求是否成功 
                    if (httpResponseMessage.IsSuccessStatusCode)
                    {
                        //json字符串获取
                        string json = await httpResponseMessage.Content.ReadAsStringAsync();
                        result = json;
                    }
                }
                else
                {
                    result = "请求失败";
                }
            }
            return result;
        }

        /// <summary>
        /// delete(通过body方式传值)
        /// </summary>
        /// <param name="url">请求地址</param>
        /// <param name="isAuthorzation">是否授权</param>
        /// <param name="content">参数内容</param>
        /// <returns></returns>
        public static async Task<string> DeleteAsync(string url, bool isAuthorzation = false, HttpContent content = null)
        {
            var result = "";
            using (HttpClient client = new HttpClient())
            {
                if (isAuthorzation)
                {
                    AuthenticationHeaderValue authentication = new AuthenticationHeaderValue(
                    "Bearer",
                    TokenDto.Token);
                    client.DefaultRequestHeaders.Authorization = authentication;
                }

                HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Delete, url);
                httpRequestMessage.Content = content;
                //发送请求
                HttpResponseMessage httpResponseMessage = client.SendAsync(httpRequestMessage).Result;
                //判断请求是否成功 
                if (httpResponseMessage.IsSuccessStatusCode)
                {
                    //json字符串获取
                    string json = await httpResponseMessage.Content.ReadAsStringAsync();
                    result = json;
                }
            }
            return result;
        }
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.SystemManagement
{
    public partial class UserManagementControl : UserControl
    {
        public UserManagementControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // UserManagementControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "UserManagementControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.UserManagementControl_Load);
            this.ResumeLayout(false);
        }

        private void UserManagementControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "用户管理";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Create a panel for search
            Panel searchPanel = new Panel();
            searchPanel.Dock = DockStyle.Top;
            searchPanel.Height = 70;
            searchPanel.Padding = new Padding(20);

            // Search label
            Label searchLabel = new Label();
            searchLabel.Text = "用户名:";
            searchLabel.AutoSize = true;
            searchLabel.Location = new Point(30, 25);

            // Search textbox
            TextBox searchTextBox = new TextBox();
            searchTextBox.Location = new Point(100, 25);
            searchTextBox.Width = 200;

            // Search button
            Button searchButton = new Button();
            searchButton.Text = "搜索";
            searchButton.Location = new Point(320, 23);
            searchButton.Width = 80;
            searchButton.Height = 28;
            searchButton.BackColor = Color.FromArgb(0, 120, 215);
            searchButton.ForeColor = Color.White;

            // Add new user button
            Button addButton = new Button();
            addButton.Text = "添加用户";
            addButton.Location = new Point(420, 23);
            addButton.Width = 100;
            addButton.Height = 28;
            addButton.BackColor = Color.FromArgb(46, 204, 113);
            addButton.ForeColor = Color.White;
            addButton.Click += (s, ev) => ShowAddUserDialog();

            // Add controls to search panel
            searchPanel.Controls.Add(searchLabel);
            searchPanel.Controls.Add(searchTextBox);
            searchPanel.Controls.Add(searchButton);
            searchPanel.Controls.Add(addButton);

            this.Controls.Add(searchPanel);

            // Create a grid for user list
            DataGridView grid = new DataGridView();
            grid.Dock = DockStyle.Fill;
            grid.BackgroundColor = Color.White;
            grid.BorderStyle = BorderStyle.None;
            grid.AllowUserToAddRows = false;
            grid.AllowUserToDeleteRows = false;
            grid.ReadOnly = true;
            grid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            grid.RowHeadersVisible = false;
            grid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // Add columns
            grid.Columns.Add("Id", "ID");
            grid.Columns.Add("Username", "用户名");
            grid.Columns.Add("RealName", "姓名");
            grid.Columns.Add("Role", "角色");
            grid.Columns.Add("Department", "所属部门");
            grid.Columns.Add("Phone", "联系电话");
            grid.Columns.Add("Status", "状态");
            grid.Columns.Add("CreateTime", "创建时间");

            // Add action column with buttons
            DataGridViewButtonColumn actionColumn = new DataGridViewButtonColumn();
            actionColumn.HeaderText = "操作";
            actionColumn.Text = "编辑";
            actionColumn.UseColumnTextForButtonValue = true;
            actionColumn.Width = 60;
            grid.Columns.Add(actionColumn);

            // Add sample data
            grid.Rows.Add("1", "admin", "管理员", "系统管理员", "信息科", "13800000000", "正常", "2023-01-10");
            grid.Rows.Add("2", "doctor1", "张医生", "医生", "外科", "13811111111", "正常", "2023-02-15");
            grid.Rows.Add("3", "nurse1", "李护士", "护士", "内科", "13822222222", "正常", "2023-03-20");
            grid.Rows.Add("4", "operator1", "王操作员", "操作员", "消毒中心", "13833333333", "正常", "2023-04-25");
            grid.Rows.Add("5", "manager1", "赵经理", "部门经理", "消毒中心", "13844444444", "正常", "2023-05-30");

            // Add cell click event for edit button
            grid.CellClick += (s, ev) => {
                if (ev.ColumnIndex == actionColumn.Index && ev.RowIndex >= 0)
                {
                    string username = grid.Rows[ev.RowIndex].Cells["Username"].Value.ToString();
                    MessageBox.Show($"编辑用户: {username}", "编辑用户", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            };

            this.Controls.Add(grid);
        }

        private void ShowAddUserDialog()
        {
            // Create a simple dialog form for adding a user
            Form addUserForm = new Form();
            addUserForm.Text = "添加用户";
            addUserForm.Width = 400;
            addUserForm.Height = 400;
            addUserForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            addUserForm.StartPosition = FormStartPosition.CenterParent;
            addUserForm.MaximizeBox = false;
            addUserForm.MinimizeBox = false;

            // Username
            Label usernameLabel = new Label();
            usernameLabel.Text = "用户名:";
            usernameLabel.Location = new Point(30, 30);
            addUserForm.Controls.Add(usernameLabel);

            TextBox usernameTextBox = new TextBox();
            usernameTextBox.Location = new Point(120, 30);
            usernameTextBox.Width = 200;
            addUserForm.Controls.Add(usernameTextBox);

            // Password
            Label passwordLabel = new Label();
            passwordLabel.Text = "密码:";
            passwordLabel.Location = new Point(30, 70);
            addUserForm.Controls.Add(passwordLabel);

            TextBox passwordTextBox = new TextBox();
            passwordTextBox.Location = new Point(120, 70);
            passwordTextBox.Width = 200;
            passwordTextBox.PasswordChar = '*';
            addUserForm.Controls.Add(passwordTextBox);

            // Real name
            Label nameLabel = new Label();
            nameLabel.Text = "姓名:";
            nameLabel.Location = new Point(30, 110);
            addUserForm.Controls.Add(nameLabel);

            TextBox nameTextBox = new TextBox();
            nameTextBox.Location = new Point(120, 110);
            nameTextBox.Width = 200;
            addUserForm.Controls.Add(nameTextBox);

            // Role
            Label roleLabel = new Label();
            roleLabel.Text = "角色:";
            roleLabel.Location = new Point(30, 150);
            addUserForm.Controls.Add(roleLabel);

            ComboBox roleComboBox = new ComboBox();
            roleComboBox.Location = new Point(120, 150);
            roleComboBox.Width = 200;
            roleComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            roleComboBox.Items.AddRange(new object[] { "系统管理员", "医生", "护士", "操作员", "部门经理" });
            roleComboBox.SelectedIndex = 0;
            addUserForm.Controls.Add(roleComboBox);

            // Department
            Label deptLabel = new Label();
            deptLabel.Text = "部门:";
            deptLabel.Location = new Point(30, 190);
            addUserForm.Controls.Add(deptLabel);

            ComboBox deptComboBox = new ComboBox();
            deptComboBox.Location = new Point(120, 190);
            deptComboBox.Width = 200;
            deptComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            deptComboBox.Items.AddRange(new object[] { "信息科", "外科", "内科", "消毒中心", "手术室" });
            deptComboBox.SelectedIndex = 0;
            addUserForm.Controls.Add(deptComboBox);

            // Phone
            Label phoneLabel = new Label();
            phoneLabel.Text = "电话:";
            phoneLabel.Location = new Point(30, 230);
            addUserForm.Controls.Add(phoneLabel);

            TextBox phoneTextBox = new TextBox();
            phoneTextBox.Location = new Point(120, 230);
            phoneTextBox.Width = 200;
            addUserForm.Controls.Add(phoneTextBox);

            // Save button
            Button saveButton = new Button();
            saveButton.Text = "保存";
            saveButton.Location = new Point(120, 280);
            saveButton.Width = 80;
            saveButton.BackColor = Color.FromArgb(0, 120, 215);
            saveButton.ForeColor = Color.White;
            saveButton.Click += (s, e) => {
                if (string.IsNullOrEmpty(usernameTextBox.Text) || string.IsNullOrEmpty(passwordTextBox.Text))
                {
                    MessageBox.Show("请填写用户名和密码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                MessageBox.Show($"已添加用户: {usernameTextBox.Text}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                addUserForm.Close();
            };
            addUserForm.Controls.Add(saveButton);

            // Cancel button
            Button cancelButton = new Button();
            cancelButton.Text = "取消";
            cancelButton.Location = new Point(220, 280);
            cancelButton.Width = 80;
            cancelButton.Click += (s, e) => addUserForm.Close();
            addUserForm.Controls.Add(cancelButton);

            addUserForm.ShowDialog();
        }
    }
} 
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.ReportManagement
{
    public partial class OutboundReportControl : UserControl
    {
        public OutboundReportControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // OutboundReportControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "OutboundReportControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.OutboundReportControl_Load);
            this.ResumeLayout(false);
        }

        private void OutboundReportControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "出库报表";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Create filter panel
            Panel filterPanel = new Panel();
            filterPanel.Dock = DockStyle.Top;
            filterPanel.Height = 100;
            filterPanel.Padding = new Padding(10);
            this.Controls.Add(filterPanel);

            // Start date
            Label startLabel = new Label();
            startLabel.Text = "开始日期:";
            startLabel.Location = new Point(20, 20);
            startLabel.AutoSize = true;
            filterPanel.Controls.Add(startLabel);

            DateTimePicker startDatePicker = new DateTimePicker();
            startDatePicker.Format = DateTimePickerFormat.Short;
            startDatePicker.Location = new Point(100, 20);
            startDatePicker.Width = 120;
            startDatePicker.Value = DateTime.Now.AddMonths(-1);
            filterPanel.Controls.Add(startDatePicker);

            // End date
            Label endLabel = new Label();
            endLabel.Text = "结束日期:";
            endLabel.Location = new Point(240, 20);
            endLabel.AutoSize = true;
            filterPanel.Controls.Add(endLabel);

            DateTimePicker endDatePicker = new DateTimePicker();
            endDatePicker.Format = DateTimePickerFormat.Short;
            endDatePicker.Location = new Point(320, 20);
            endDatePicker.Width = 120;
            endDatePicker.Value = DateTime.Now;
            filterPanel.Controls.Add(endDatePicker);

            // Device type
            Label typeLabel = new Label();
            typeLabel.Text = "设备类型:";
            typeLabel.Location = new Point(20, 60);
            typeLabel.AutoSize = true;
            filterPanel.Controls.Add(typeLabel);

            ComboBox typeComboBox = new ComboBox();
            typeComboBox.Items.AddRange(new object[] { "全部", "灭菌器", "清洗机", "消毒柜" });
            typeComboBox.SelectedIndex = 0;
            typeComboBox.Location = new Point(100, 60);
            typeComboBox.Width = 120;
            filterPanel.Controls.Add(typeComboBox);

            // Department
            Label deptLabel = new Label();
            deptLabel.Text = "领用科室:";
            deptLabel.Location = new Point(240, 60);
            deptLabel.AutoSize = true;
            filterPanel.Controls.Add(deptLabel);

            ComboBox deptComboBox = new ComboBox();
            deptComboBox.Items.AddRange(new object[] { "全部", "手术室", "消毒中心", "外科", "内科" });
            deptComboBox.SelectedIndex = 0;
            deptComboBox.Location = new Point(320, 60);
            deptComboBox.Width = 120;
            filterPanel.Controls.Add(deptComboBox);

            // Search button
            Button searchButton = new Button();
            searchButton.Text = "查询";
            searchButton.Location = new Point(520, 20);
            searchButton.Width = 80;
            searchButton.Height = 30;
            searchButton.BackColor = Color.FromArgb(0, 120, 215);
            searchButton.ForeColor = Color.White;
            filterPanel.Controls.Add(searchButton);

            // Export button
            Button exportButton = new Button();
            exportButton.Text = "导出Excel";
            exportButton.Location = new Point(520, 60);
            exportButton.Width = 80;
            exportButton.Height = 30;
            exportButton.BackColor = Color.FromArgb(46, 204, 113);
            exportButton.ForeColor = Color.White;
            filterPanel.Controls.Add(exportButton);

            // Print button
            Button printButton = new Button();
            printButton.Text = "打印";
            printButton.Location = new Point(620, 20);
            printButton.Width = 80;
            printButton.Height = 30;
            printButton.BackColor = Color.FromArgb(52, 152, 219);
            printButton.ForeColor = Color.White;
            filterPanel.Controls.Add(printButton);

            // Create statistics panel
            Panel statsPanel = new Panel();
            statsPanel.Dock = DockStyle.Top;
            statsPanel.Height = 80;
            statsPanel.BackColor = Color.FromArgb(240, 240, 240);
            statsPanel.Padding = new Padding(20);
            this.Controls.Add(statsPanel);

            // Total outbound count
            Label totalLabel = new Label();
            totalLabel.Text = "出库单总数: 35单";
            totalLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            totalLabel.Location = new Point(20, 20);
            totalLabel.AutoSize = true;
            statsPanel.Controls.Add(totalLabel);

            // Total device count
            Label deviceCountLabel = new Label();
            deviceCountLabel.Text = "出库设备总数: 102台";
            deviceCountLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            deviceCountLabel.Location = new Point(200, 20);
            deviceCountLabel.AutoSize = true;
            statsPanel.Controls.Add(deviceCountLabel);

            // Total value
            Label valueLabel = new Label();
            valueLabel.Text = "出库总金额: ¥2,235,000.00";
            valueLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            valueLabel.Location = new Point(400, 20);
            valueLabel.AutoSize = true;
            statsPanel.Controls.Add(valueLabel);

            // Monthly average
            Label avgLabel = new Label();
            avgLabel.Text = "月平均出库: ¥186,250.00";
            avgLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            avgLabel.Location = new Point(620, 20);
            avgLabel.AutoSize = true;
            statsPanel.Controls.Add(avgLabel);

            // Create chart panel for visualization
            Panel chartPanel = new Panel();
            chartPanel.Dock = DockStyle.Top;
            chartPanel.Height = 120;
            chartPanel.BackColor = Color.White;
            chartPanel.Padding = new Padding(10);
            this.Controls.Add(chartPanel);

            // Chart title
            Label chartTitle = new Label();
            chartTitle.Text = "近12个月出库趋势";
            chartTitle.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            chartTitle.Location = new Point(20, 10);
            chartTitle.AutoSize = true;
            chartPanel.Controls.Add(chartTitle);

            // Simple bar chart (visualization placeholder)
            Panel barChart = new Panel();
            barChart.Location = new Point(20, 40);
            barChart.Size = new Size(760, 60);
            barChart.BorderStyle = BorderStyle.FixedSingle;
            barChart.Paint += (s, ev) => {
                // Draw simple bar chart
                using (Graphics g = ev.Graphics)
                {
                    g.Clear(Color.White);
                    
                    // Draw grid lines
                    using (Pen gridPen = new Pen(Color.LightGray, 1))
                    {
                        for (int i = 0; i < 6; i++)
                        {
                            int y = 10 + i * 10;
                            g.DrawLine(gridPen, 40, y, 740, y);
                        }
                    }
                    
                    // Draw month labels
                    string[] months = { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" };
                    for (int i = 0; i < 12; i++)
                    {
                        int x = 60 + i * 58;
                        g.DrawString(months[i], new Font("Arial", 7), Brushes.Gray, x, 45);
                    }
                    
                    // Draw bars (random heights for demo)
                    Random rand = new Random(1234); // Fixed seed for consistency
                    for (int i = 0; i < 12; i++)
                    {
                        int height = rand.Next(5, 40);
                        int x = 60 + i * 58;
                        Rectangle rect = new Rectangle(x, 45 - height, 40, height);
                        
                        // Use different colors for different quarters
                        Brush brush;
                        if (i < 3) brush = Brushes.RoyalBlue;
                        else if (i < 6) brush = Brushes.LightSeaGreen;
                        else if (i < 9) brush = Brushes.Orange;
                        else brush = Brushes.Crimson;
                        
                        g.FillRectangle(brush, rect);
                        g.DrawRectangle(Pens.DarkGray, rect);
                    }
                    
                    // Draw Y-axis label
                    g.DrawString("数量", new Font("Arial", 8), Brushes.Gray, 5, 20);
                }
            };
            chartPanel.Controls.Add(barChart);

            // Create report grid
            DataGridView reportGrid = new DataGridView();
            reportGrid.Dock = DockStyle.Fill;
            reportGrid.BackgroundColor = Color.White;
            reportGrid.BorderStyle = BorderStyle.None;
            reportGrid.AllowUserToAddRows = false;
            reportGrid.AllowUserToDeleteRows = false;
            reportGrid.ReadOnly = true;
            reportGrid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            reportGrid.RowHeadersVisible = false;
            reportGrid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            reportGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            this.Controls.Add(reportGrid);

            // Add columns
            reportGrid.Columns.Add("OutboundId", "出库单号");
            reportGrid.Columns.Add("OutboundDate", "出库日期");
            reportGrid.Columns.Add("DeviceName", "设备名称");
            reportGrid.Columns.Add("DeviceType", "设备类型");
            reportGrid.Columns.Add("Department", "领用科室");
            reportGrid.Columns.Add("Count", "数量");
            reportGrid.Columns.Add("UnitPrice", "单价");
            reportGrid.Columns.Add("TotalPrice", "总价");
            reportGrid.Columns.Add("Operator", "操作人");
            reportGrid.Columns.Add("Status", "状态");

            // Add sample data
            reportGrid.Rows.Add("OUT-20230120-001", "2023-01-20", "医用灭菌器", "灭菌器", "手术室", "1", "¥50,000.00", "¥50,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230225-001", "2023-02-25", "高温灭菌器", "灭菌器", "消毒中心", "1", "¥75,000.00", "¥75,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230315-001", "2023-03-15", "全自动清洗机", "清洗机", "消毒中心", "2", "¥60,000.00", "¥120,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230410-001", "2023-04-10", "超声波清洗机", "清洗机", "消毒中心", "1", "¥45,000.00", "¥45,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230520-001", "2023-05-20", "立式消毒柜", "消毒柜", "外科", "3", "¥30,000.00", "¥90,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230625-001", "2023-06-25", "卧式消毒柜", "消毒柜", "内科", "2", "¥25,000.00", "¥50,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230720-001", "2023-07-20", "低温灭菌器", "灭菌器", "手术室", "1", "¥80,000.00", "¥80,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230815-001", "2023-08-15", "多功能清洗机", "清洗机", "消毒中心", "2", "¥55,000.00", "¥110,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20230910-001", "2023-09-10", "壁挂式消毒柜", "消毒柜", "外科", "5", "¥20,000.00", "¥100,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20231025-001", "2023-10-25", "压力蒸汽灭菌器", "灭菌器", "消毒中心", "1", "¥90,000.00", "¥90,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20231127-001", "2023-11-27", "喷淋式清洗机", "清洗机", "消毒中心", "1", "¥65,000.00", "¥65,000.00", "admin", "已完成");
            reportGrid.Rows.Add("OUT-20231220-001", "2023-12-20", "臭氧消毒柜", "消毒柜", "内科", "2", "¥35,000.00", "¥70,000.00", "admin", "进行中");
        }
    }
} 
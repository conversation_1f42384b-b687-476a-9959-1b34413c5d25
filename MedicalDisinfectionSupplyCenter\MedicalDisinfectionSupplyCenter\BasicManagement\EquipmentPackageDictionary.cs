using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.BasicManagement
{
    /// <summary>
    /// 器械包字典管理界面
    /// </summary>
    public partial class EquipmentPackageDictionary : UserControl
    {
        #region 常量与API客户端
        private const string API_BASE_URL = "http://localhost:5172/api/BasicManagement";
        private const string API_WRITE_BASE_URL = "http://localhost:5192/api/BasicManagement";
        private static readonly HttpClient httpClient = new HttpClient() { Timeout = TimeSpan.FromSeconds(30) };
        #endregion

        #region 控件声明
        private LayoutControl layoutControl;
        private LayoutControlGroup rootGroup;

        // 主面板控件
        private PanelControl panelToolbar;
        private ComboBoxEdit comboPackageType;
        private TextEdit txtPackageCode;
        private TextEdit txtPackageName;
        private CheckEdit chkShowDisabled;
        private SimpleButton btnQuery;
        private SimpleButton btnAdd;
        private GridControl gridPackage;
        private GridView gridViewPackage;
        private LabelControl lblTotal;

        // 分页控件
        private PanelControl panelPagination;
        private SimpleButton btnPrevPage;
        private SimpleButton btnNextPage;
        private LabelControl lblPageInfo;
        private ComboBoxEdit comboPageSize;
        private LabelControl lblPageSize;

        // 分页变量
        private int _currentPageIndex = 1;
        private int _pageSize = 20;
        private int _totalCount = 0;
        private int _totalPages = 0;
        #endregion

        /// <summary>
        /// 初始化器械包字典界面
        /// </summary>
        public EquipmentPackageDictionary()
        {
            try
            {
                InitializeComponent();
                InitializeControls();
                InitializeEvents();

                // 使用Load事件来执行异步操作，避免在构造函数中直接调用异步方法
                this.Load += EquipmentPackageDictionary_Load;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化器械包字典界面时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 页面加载事件处理
        /// </summary>
        private async void EquipmentPackageDictionary_Load(object sender, EventArgs e)
        {
            try
            {
                // 在页面加载时执行异步操作
                await LoadPackageTypeComboAsync();

                // 初始化分页控件状态
                UpdatePaginationControls();

                // 加载器械包数据
                await QueryPackageAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载器械包字典数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化所有控件
        /// </summary>
        private void InitializeControls()
        {
            // 主布局控件
            layoutControl = new LayoutControl();
            layoutControl.Dock = DockStyle.Fill;
            layoutControl.Name = "layoutControl";

            // 创建主面板
            var mainPanel = new PanelControl();
            mainPanel.Name = "mainPanel";
            mainPanel.Dock = DockStyle.Fill;

            // ===== 工具栏和表格 =====

            // 工具栏面板
            panelToolbar = new PanelControl();
            panelToolbar.Name = "panelToolbar";
            panelToolbar.Height = 120;
            panelToolbar.Dock = DockStyle.Top;
            panelToolbar.Padding = new Padding(10, 10, 10, 10);

            // 第一行控件
            var lblPackageType = new LabelControl();
            lblPackageType.Location = new Point(20, 18);
            lblPackageType.Size = new Size(70, 28);
            lblPackageType.Text = "包装类型：";
            lblPackageType.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            comboPackageType = new ComboBoxEdit();
            comboPackageType.Name = "comboPackageType";
            comboPackageType.Location = new Point(100, 15);
            comboPackageType.Size = new Size(120, 28);
            comboPackageType.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;

            var lblPackageCode = new LabelControl();
            lblPackageCode.Location = new Point(240, 18);
            lblPackageCode.Size = new Size(70, 28);
            lblPackageCode.Text = "包装编码：";
            lblPackageCode.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            txtPackageCode = new TextEdit();
            txtPackageCode.Name = "txtPackageCode";
            txtPackageCode.Location = new Point(320, 15);
            txtPackageCode.Size = new Size(120, 28);

            var lblPackageName = new LabelControl();
            lblPackageName.Location = new Point(460, 18);
            lblPackageName.Size = new Size(70, 28);
            lblPackageName.Text = "包装名称：";
            lblPackageName.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            txtPackageName = new TextEdit();
            txtPackageName.Name = "txtPackageName";
            txtPackageName.Location = new Point(540, 15);
            txtPackageName.Size = new Size(120, 28);

            // 第二行控件
            chkShowDisabled = new CheckEdit();
            chkShowDisabled.Name = "chkShowDisabled";
            chkShowDisabled.Location = new Point(20, 55);
            chkShowDisabled.Size = new Size(120, 28);
            chkShowDisabled.Text = "显示禁用项";
            chkShowDisabled.Checked = true; // 默认设置为选中状态

            btnQuery = new SimpleButton();
            btnQuery.Name = "btnQuery";
            btnQuery.Location = new Point(160, 55);
            btnQuery.Size = new Size(80, 28);
            btnQuery.Text = "查询";

            btnAdd = new SimpleButton();
            btnAdd.Name = "btnAdd";
            btnAdd.Location = new Point(250, 55);
            btnAdd.Size = new Size(80, 28);
            btnAdd.Text = "新增";

            // 将控件添加到面板
            panelToolbar.Controls.Add(lblPackageType);
            panelToolbar.Controls.Add(comboPackageType);
            panelToolbar.Controls.Add(lblPackageCode);
            panelToolbar.Controls.Add(txtPackageCode);
            panelToolbar.Controls.Add(lblPackageName);
            panelToolbar.Controls.Add(txtPackageName);
            panelToolbar.Controls.Add(chkShowDisabled);
            panelToolbar.Controls.Add(btnQuery);
            panelToolbar.Controls.Add(btnAdd);

            // 器械包列表表格
            gridPackage = new GridControl();
            gridPackage.Name = "gridPackage";
            gridPackage.Dock = DockStyle.Fill;
            gridViewPackage = new GridView(gridPackage);
            gridViewPackage.Name = "gridViewPackage";
            gridPackage.MainView = gridViewPackage;

            // 设置表格列
            SetupGridColumns();

            // 分页面板
            panelPagination = new PanelControl();
            panelPagination.Name = "panelPagination";
            panelPagination.Height = 40;
            panelPagination.Dock = DockStyle.Bottom;
            panelPagination.Padding = new Padding(10, 5, 10, 5);

            // 上一页按钮
            btnPrevPage = new SimpleButton();
            btnPrevPage.Name = "btnPrevPage";
            btnPrevPage.Text = "上一页";
            btnPrevPage.Location = new Point(10, 8);
            btnPrevPage.Size = new Size(70, 24);
            btnPrevPage.Enabled = false;

            // 下一页按钮
            btnNextPage = new SimpleButton();
            btnNextPage.Name = "btnNextPage";
            btnNextPage.Text = "下一页";
            btnNextPage.Location = new Point(90, 8);
            btnNextPage.Size = new Size(70, 24);
            btnNextPage.Enabled = false;

            // 页码信息标签
            lblPageInfo = new LabelControl();
            lblPageInfo.Name = "lblPageInfo";
            lblPageInfo.Text = "第1页/共1页";
            lblPageInfo.Location = new Point(170, 12);
            lblPageInfo.Size = new Size(100, 16);

            // 每页大小标签
            lblPageSize = new LabelControl();
            lblPageSize.Name = "lblPageSize";
            lblPageSize.Text = "每页显示：";
            lblPageSize.Location = new Point(280, 12);
            lblPageSize.Size = new Size(60, 16);

            // 每页大小下拉框
            comboPageSize = new ComboBoxEdit();
            comboPageSize.Name = "comboPageSize";
            comboPageSize.Location = new Point(345, 8);
            comboPageSize.Size = new Size(60, 24);
            comboPageSize.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
            comboPageSize.Properties.Items.AddRange(new object[] { "10", "20", "50", "100" });
            comboPageSize.SelectedIndex = 1; // 默认选择20

            // 合计标签
            lblTotal = new LabelControl();
            lblTotal.Name = "lblTotal";
            lblTotal.Text = "合计：0条";
            lblTotal.Location = new Point(420, 12);
            lblTotal.Size = new Size(100, 16);

            // 将控件添加到分页面板
            panelPagination.Controls.Add(btnPrevPage);
            panelPagination.Controls.Add(btnNextPage);
            panelPagination.Controls.Add(lblPageInfo);
            panelPagination.Controls.Add(lblPageSize);
            panelPagination.Controls.Add(comboPageSize);
            panelPagination.Controls.Add(lblTotal);

            // 添加控件到主面板（按正确顺序）
            mainPanel.Controls.Add(panelPagination); // 底部分页面板
            mainPanel.Controls.Add(gridPackage);     // 填充剩余空间
            mainPanel.Controls.Add(panelToolbar);    // 顶部工具栏

            // 将主面板添加到布局控件
            layoutControl.Controls.Add(mainPanel);

            // 将主控件添加到用户控件
            this.Controls.Add(layoutControl);

            // 配置主布局组
            rootGroup = new LayoutControlGroup();
            rootGroup.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            layoutControl.Root = rootGroup;

            // 配置主面板布局项
            var layoutControlItemMain = new LayoutControlItem();
            layoutControlItemMain.Control = mainPanel;
            layoutControlItemMain.TextVisible = false;
            layoutControl.Root.AddItem(layoutControlItemMain);
        }

        /// <summary>
        /// 设置表格列
        /// </summary>
        private void SetupGridColumns()
        {
            // 清除现有列
            gridViewPackage.Columns.Clear();

            // 添加列
            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "ID",
                FieldName = "Id",
                Visible = false
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "包装类型",
                FieldName = "PackageTypeName",
                Visible = true,
                Width = 100
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "包装编码",
                FieldName = "PackageCode",
                Visible = true,
                Width = 120
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "包装名称",
                FieldName = "PackageName",
                Visible = true,
                Width = 150
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "科室",
                FieldName = "Department",
                Visible = true,
                Width = 100
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "灭菌方式",
                FieldName = "SterilizationMethod",
                Visible = true,
                Width = 100
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "外包装",
                FieldName = "OuterPackaging",
                Visible = true,
                Width = 80
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "有效天数",
                FieldName = "Days",
                Visible = true,
                Width = 80
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "服务价格",
                FieldName = "ServicePrice",
                Visible = true,
                Width = 80
            });

            gridViewPackage.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "状态",
                FieldName = "StatusName",
                Visible = true,
                Width = 60
            });

            // 添加操作列
            var colOperation = new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "操作",
                FieldName = "Operation",
                Visible = true,
                Width = 120,
                UnboundType = DevExpress.Data.UnboundColumnType.Object
            };

            // 创建按钮编辑器
            var repositoryItemButtonEdit = new RepositoryItemButtonEdit();
            repositoryItemButtonEdit.TextEditStyle = TextEditStyles.HideTextEditor;
            repositoryItemButtonEdit.Buttons[0].Caption = "编辑";
            repositoryItemButtonEdit.Buttons[0].Kind = ButtonPredefines.Glyph;
            repositoryItemButtonEdit.Buttons.Add(new EditorButton(ButtonPredefines.Glyph));
            repositoryItemButtonEdit.Buttons[1].Caption = "删除";
            repositoryItemButtonEdit.Buttons[1].Kind = ButtonPredefines.Glyph;
            repositoryItemButtonEdit.ButtonClick += RepositoryItemButtonEdit_ButtonClick;

            colOperation.ColumnEdit = repositoryItemButtonEdit;
            gridViewPackage.Columns.Add(colOperation);

            // 设置表格属性
            gridViewPackage.OptionsBehavior.Editable = false;
            gridViewPackage.OptionsView.ShowGroupPanel = false;
            gridViewPackage.OptionsView.ShowIndicator = true;
            gridViewPackage.OptionsView.EnableAppearanceEvenRow = true;
            gridViewPackage.OptionsView.EnableAppearanceOddRow = true;
        }

        /// <summary>
        /// 初始化事件
        /// </summary>
        private void InitializeEvents()
        {
            // 查询按钮点击事件
            btnQuery.Click += async (sender, e) =>
            {
                _currentPageIndex = 1; // 重置到第一页
                await QueryPackageAsync();
            };

            // 添加按钮点击事件
            btnAdd.Click += BtnAdd_Click;

            // 表格双击事件
            gridViewPackage.DoubleClick += GridViewPackage_DoubleClick;

            // 分页事件
            btnPrevPage.Click += async (sender, e) =>
            {
                if (_currentPageIndex > 1)
                {
                    _currentPageIndex--;
                    await QueryPackageAsync();
                }
            };

            btnNextPage.Click += async (sender, e) =>
            {
                if (_currentPageIndex < _totalPages)
                {
                    _currentPageIndex++;
                    await QueryPackageAsync();
                }
            };

            // 每页大小改变事件
            comboPageSize.SelectedIndexChanged += async (sender, e) =>
            {
                if (int.TryParse(comboPageSize.Text, out int newPageSize))
                {
                    _pageSize = newPageSize;
                    _currentPageIndex = 1; // 重置到第一页
                    await QueryPackageAsync();
                }
            };

            // 显示禁用项复选框变更事件
            chkShowDisabled.CheckedChanged += async (sender, e) =>
            {
                _currentPageIndex = 1; // 重置到第一页
                await QueryPackageAsync();
            };

            // 器械包编码输入框回车键查询事件
            txtPackageCode.KeyDown += async (sender, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    _currentPageIndex = 1; // 重置到第一页
                    await QueryPackageAsync();
                }
            };

            // 器械包名称输入框回车键查询事件
            txtPackageName.KeyDown += async (sender, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    _currentPageIndex = 1; // 重置到第一页
                    await QueryPackageAsync();
                }
            };
        }

        /// <summary>
        /// 操作按钮点击事件
        /// </summary>
        private void RepositoryItemButtonEdit_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            try
            {
                var view = gridViewPackage;
                if (view.FocusedRowHandle >= 0)
                {
                    var package = view.GetRow(view.FocusedRowHandle) as EquipmentPackageListDto;
                    if (package != null)
                    {
                        if (e.Button.Index == 0) // 编辑按钮
                        {
                            EditPackage(package);
                        }
                        if (e.Button.Index == 1) // 删除按钮
                        {
                            DeletePackage(package);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 表格双击事件
        /// </summary>
        private void GridViewPackage_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                var view = gridViewPackage;
                if (view.FocusedRowHandle >= 0)
                {
                    var package = view.GetRow(view.FocusedRowHandle) as EquipmentPackageListDto;
                    if (package != null)
                    {
                        EditPackage(package);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开编辑失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加按钮点击事件
        /// </summary>
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("新增器械包功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新增器械包失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑器械包
        /// </summary>
        private void EditPackage(EquipmentPackageListDto package)
        {
            try
            {
                MessageBox.Show($"编辑器械包功能开发中...\n器械包：{package.PackageName}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑器械包失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除器械包
        /// </summary>
        private void DeletePackage(EquipmentPackageListDto package)
        {
            try
            {
                var result = MessageBox.Show($"确定要删除器械包 '{package.PackageName}' 吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    MessageBox.Show("删除器械包功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除器械包失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载器械包类型下拉框数据
        /// </summary>
        private async Task LoadPackageTypeComboAsync()
        {
            try
            {
                // 检查HttpClient状态
                if (httpClient == null)
                {
                    MessageBox.Show("网络客户端未初始化，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 清空现有项
                comboPackageType.Properties.Items.Clear();
                comboPackageType.Properties.Items.Add(new PackageTypeComboItem
                {
                    Id = 0,
                    Name = "所有"
                });

                // 添加预定义的器械包类型
                var packageTypes = new List<PackageTypeComboItem>
                {
                    new PackageTypeComboItem { Id = 1, Name = "专科包" },
                    new PackageTypeComboItem { Id = 2, Name = "通用包" }
                };

                foreach (var type in packageTypes)
                {
                    comboPackageType.Properties.Items.Add(type);
                }

                // 设置默认选择
                comboPackageType.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载器械包类型失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 查询器械包列表
        /// </summary>
        private async Task QueryPackageAsync()
        {
            try
            {
                // 检查HttpClient状态
                if (httpClient == null)
                {
                    MessageBox.Show("网络客户端未初始化，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 构建查询参数
                var queryParams = new List<string>
                {
                    $"PageIndex={_currentPageIndex}",
                    $"PageSize={_pageSize}"
                };

                // 添加器械包类型筛选
                if (comboPackageType.SelectedItem is PackageTypeComboItem selectedType && selectedType.Id > 0)
                {
                    queryParams.Add($"PackageType={selectedType.Id}");
                }

                // 添加器械包编码筛选
                if (!string.IsNullOrWhiteSpace(txtPackageCode.Text))
                {
                    queryParams.Add($"PackageCode={Uri.EscapeDataString(txtPackageCode.Text.Trim())}");
                }

                // 添加器械包名称筛选
                if (!string.IsNullOrWhiteSpace(txtPackageName.Text))
                {
                    queryParams.Add($"PackageName={Uri.EscapeDataString(txtPackageName.Text.Trim())}");
                }

                // 添加状态筛选
                if (!chkShowDisabled.Checked)
                {
                    queryParams.Add("Status=1"); // 只显示启用的
                }

                // 构建查询字符串
                var queryString = string.Join("&", queryParams);

                // 构建API请求URL
                string apiUrl = $"{API_BASE_URL}/QueryEquipmentPackage?{queryString}";

                // 发送GET请求查询器械包列表
                using (var response = await httpClient.GetAsync(apiUrl))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonString = await response.Content.ReadAsStringAsync();

                        try
                        {
                            // 首先尝试使用标准ApiResponse格式解析
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponseModel>(jsonString);

                            // 检查是否成功解析并且code为200
                            if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                            {
                                try
                                {
                                    // 解析分页数据
                                    var pageData = apiResponse.data.ToObject<PageDataModel<EquipmentPackageListDto>>();
                                    if (pageData != null)
                                    {
                                        // 设置状态显示名称、包装类型名称、灭菌方式和外包装中文显示
                                        foreach (var item in pageData.Items)
                                        {
                                            item.StatusName = item.Status == 1 ? "启用" : "停用";
                                            item.PackageTypeName = item.PackageType == 1 ? "通用包" : "专科包";

                                            // 转换灭菌方式数字为中文
                                            if (int.TryParse(item.SterilizationMethod, out int sterilizationValue))
                                            {
                                                if (Enum.IsDefined(typeof(SterilizationMethodEnum), sterilizationValue))
                                                {
                                                    var sterilizationEnum = (SterilizationMethodEnum)sterilizationValue;
                                                    item.SterilizationMethod = GetEnumDescription(sterilizationEnum);
                                                }
                                            }

                                            // 转换外包装数字为中文
                                            if (int.TryParse(item.OuterPackaging, out int packagingValue))
                                            {
                                                if (Enum.IsDefined(typeof(OuterPackagingEnum), packagingValue))
                                                {
                                                    var packagingEnum = (OuterPackagingEnum)packagingValue;
                                                    item.OuterPackaging = GetEnumDescription(packagingEnum);
                                                }
                                            }
                                        }

                                        // 更新分页信息
                                        _totalCount = pageData.TotalCount;
                                        _totalPages = (int)Math.Ceiling((double)_totalCount / _pageSize);

                                        // 更新分页控件状态
                                        UpdatePaginationControls();

                                        // 绑定数据到表格
                                        gridPackage.DataSource = pageData.Items;
                                        gridViewPackage.RefreshData();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"解析分页数据出错：{ex.Message}");
                                    MessageBox.Show($"解析分页数据出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                // 尝试使用替代格式解析
                                var alternateResponse = JsonConvert.DeserializeObject<dynamic>(jsonString);
                                if (alternateResponse != null && alternateResponse.data != null)
                                {
                                    try
                                    {
                                        // 解析分页数据
                                        var pageData = ((JToken)alternateResponse.data).ToObject<PageData<EquipmentPackageListDto>>();
                                        if (pageData != null)
                                        {
                                            // 设置状态显示名称、包装类型名称、灭菌方式和外包装中文显示
                                            foreach (var item in pageData.Items)
                                            {
                                                item.StatusName = item.Status == 1 ? "启用" : "停用";
                                                item.PackageTypeName = item.PackageType == 1 ? "通用包" : "专科包";

                                                // 转换灭菌方式数字为中文
                                                if (int.TryParse(item.SterilizationMethod, out int sterilizationValue))
                                                {
                                                    if (Enum.IsDefined(typeof(SterilizationMethodEnum), sterilizationValue))
                                                    {
                                                        var sterilizationEnum = (SterilizationMethodEnum)sterilizationValue;
                                                        item.SterilizationMethod = GetEnumDescription(sterilizationEnum);
                                                    }
                                                }

                                                // 转换外包装数字为中文
                                                if (int.TryParse(item.OuterPackaging, out int packagingValue))
                                                {
                                                    if (Enum.IsDefined(typeof(OuterPackagingEnum), packagingValue))
                                                    {
                                                        var packagingEnum = (OuterPackagingEnum)packagingValue;
                                                        item.OuterPackaging = GetEnumDescription(packagingEnum);
                                                    }
                                                }
                                            }

                                            // 更新分页信息
                                            _totalCount = pageData.TotalCount;
                                            _totalPages = (int)Math.Ceiling((double)_totalCount / _pageSize);

                                            // 更新分页控件状态
                                            UpdatePaginationControls();

                                            // 绑定数据到表格
                                            gridPackage.DataSource = pageData.Items;
                                            gridViewPackage.RefreshData();
                                            return; // 成功处理，提前返回
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"尝试替代格式解析失败: {ex.Message}");
                                    }
                                }

                                // 如果所有尝试都失败，显示错误消息
                                MessageBox.Show($"查询器械包列表失败：{(apiResponse?.msg ?? "未知错误")}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        catch (Exception parseEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"API响应解析异常: {parseEx.Message}");
                            MessageBox.Show($"解析API响应时出错: {parseEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show($"查询器械包列表失败：{response.ReasonPhrase}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询器械包列表出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取枚举值的描述信息
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>描述文本</returns>
        private string GetEnumDescription(Enum value)
        {
            if (value == null)
            {
                return string.Empty;
            }

            FieldInfo fi = value.GetType().GetField(value.ToString());
            if (fi == null)
            {
                return value.ToString();
            }

            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }

        /// <summary>
        /// 更新分页控件状态
        /// </summary>
        private void UpdatePaginationControls()
        {
            try
            {
                // 确保在UI线程上执行
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate { UpdatePaginationControls(); });
                    return;
                }

                // 更新页码信息
                lblPageInfo.Text = $"第{_currentPageIndex}页/共{_totalPages}页";

                // 更新合计标签
                lblTotal.Text = $"合计：{_totalCount}条";

                // 更新按钮状态
                btnPrevPage.Enabled = _currentPageIndex > 1;
                btnNextPage.Enabled = _currentPageIndex < _totalPages;

                // 如果没有数据，禁用所有分页按钮
                if (_totalCount == 0)
                {
                    btnPrevPage.Enabled = false;
                    btnNextPage.Enabled = false;
                    lblPageInfo.Text = "第0页/共0页";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新分页控件状态时出错：{ex.Message}");
            }
        }

        private void EquipmentPackageDictionary_Load_1(object sender, EventArgs e)
        {

        }
    }



    /// <summary>
    /// 器械包列表DTO
    /// </summary>
    public class EquipmentPackageListDto
    {
        public int Id { get; set; }
        public int PackageType { get; set; }
        public string PackageTypeName { get; set; }
        public string PackageCode { get; set; }
        public string PackageName { get; set; }
        public string Department { get; set; }
        public string SterilizationMethod { get; set; }
        public string OuterPackaging { get; set; }
        public int Days { get; set; }
        public decimal ServicePrice { get; set; }
        public int Status { get; set; }
        public string StatusName { get; set; }
        public string PackageImage { get; set; }
        public string Barcodes { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 器械包类型下拉框项
    /// </summary>
    public class PackageTypeComboItem
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }

    /// <summary>
    /// 灭菌方式枚举
    /// </summary>
    public enum SterilizationMethodEnum
    {
        /// <summary>
        /// 高温蒸汽灭菌
        /// </summary>
        [Description("高温蒸汽灭菌")]
        HighTemperatureSteam = 1,

        /// <summary>
        /// 低温等离子灭菌
        /// </summary>
        [Description("低温等离子灭菌")]
        LowTemperaturePlasma = 2,

        /// <summary>
        /// 环氧乙烷灭菌
        /// </summary>
        [Description("环氧乙烷灭菌")]
        EthyleneOxide = 3,

        /// <summary>
        /// 甲醛蒸汽灭菌
        /// </summary>
        [Description("甲醛蒸汽灭菌")]
        FormaldehydeSteam = 4
    }

    /// <summary>
    /// 外包装枚举
    /// </summary>
    public enum OuterPackagingEnum
    {
        /// <summary>
        /// 无纺布
        /// </summary>
        [Description("无纺布")]
        NonWovenFabric = 1,

        /// <summary>
        /// 纸塑包装
        /// </summary>
        [Description("纸塑包装")]
        PaperPlastic = 2,

        /// <summary>
        /// 硬质容器
        /// </summary>
        [Description("硬质容器")]
        RigidContainer = 3,

        /// <summary>
        /// 纸袋
        /// </summary>
        [Description("纸袋")]
        PaperBag = 4
    }

    #region 公共API响应模型类
    /// <summary>
    /// API响应基类
    /// </summary>
    public class ApiResponseModel
    {
        public string msg { get; set; }
        public int code { get; set; }
        public JToken data { get; set; }
        public bool Success => code == 200;
    }

    /// <summary>
    /// 分页数据包装
    /// </summary>
    public class PageDataModel<T>
    {
        [JsonProperty("pageData")]
        public List<T> Items { get; set; }

        [JsonProperty("totalCount")]
        public int TotalCount { get; set; }
    }
    #endregion
}

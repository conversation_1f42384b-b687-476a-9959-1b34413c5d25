using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using WinFormsAppDemo2.Common;
using System.Net.Http;

namespace MedicalDisinfectionSupplyCenter.PackagingSterilization
{
    // API响应数据模型
    public class ApiResponse<T>
    {
        public string msg { get; set; }
        public T data { get; set; }
        public int code { get; set; }
    }

    // 灵活的API响应模型，支持不同的返回格式
    public class FlexibleApiResponse
    {
        public string msg { get; set; }
        public object data { get; set; }
        public object code { get; set; } // 支持字符串或数字
        public string message { get; set; } // 备用消息字段
        public bool success { get; set; } // 备用成功标识
    }

    // 物品信息数据模型
    public class ItemInfo
    {
        public int id { get; set; }
        public int touseId { get; set; }
        public int recyclingId { get; set; }
        public int useId { get; set; }
        public string itemCode { get; set; }
        public string itemName { get; set; }
        public int itemType { get; set; }
        public string itemattribute { get; set; }
        public int itemNumber { get; set; }
    }
    public partial class PackagingRegistration : DevExpress.XtraEditors.XtraForm
    {
        // API基础URL
        private const string API_BASE_URL = "http://localhost:5172";
        //private const string API_BASE_URL = "http://***********:4050";

        public PackagingRegistration()
        {
            InitializeComponent();
            InitializeGridData();
            // 异步加载API数据
            LoadDataFromApiAsync();
        }

        private void InitializeGridData()
        {
            // 创建数据表结构
            DataTable dt = new DataTable();
            dt.Columns.Add("Selected", typeof(bool)); // 复选框列，显示选中状态
            dt.Columns.Add("ItemId", typeof(int)); // 隐藏列，存储API返回的ID
            dt.Columns.Add("包名称", typeof(string));
            dt.Columns.Add("包条码", typeof(string));
            dt.Columns.Add("包类型", typeof(string));
            dt.Columns.Add("包属性", typeof(string));
            dt.Columns.Add("使用科室", typeof(string));

            // 绑定到GridControl
            gridControl1.DataSource = dt;

            // 配置GridView
            ConfigureGridView();
        }

        private void ConfigureGridView()
        {
            // 隐藏ItemId列
            if (gridView1.Columns["ItemId"] != null)
            {
                gridView1.Columns["ItemId"].Visible = false;
            }

            // 配置复选框列
            if (gridView1.Columns["Selected"] != null)
            {
                gridView1.Columns["Selected"].OptionsColumn.AllowEdit = true;
                gridView1.Columns["Selected"].Caption = " ";
                gridView1.Columns["Selected"].Width = 50;
            }

            // 设置表格选项 - 使用行选择模式而不是复选框模式
            gridView1.OptionsSelection.MultiSelect = true;
            gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.RowSelect;
        }

        // 从API异步加载数据
        private async void LoadDataFromApiAsync()
        {
            try
            {
                // 构建API URL
                string apiUrl = $"{API_BASE_URL}/api/RecyclingCleaning/GetItemTableInfo";

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false, null);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ItemInfo>>>(jsonResult);

                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 填充数据到表格
                        FillGridWithApiData(apiResponse.data);
                    }
                    else
                    {
                        XtraMessageBox.Show($"API返回错误: {apiResponse?.msg ?? "未知错误"}",
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    XtraMessageBox.Show("API调用失败，未返回数据", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 将API数据填充到表格中
        private void FillGridWithApiData(List<ItemInfo> items)
        {
            try
            {
                DataTable dt = (DataTable)gridControl1.DataSource;

                // 清空现有数据
                dt.Rows.Clear();

                // 添加API数据
                foreach (var item in items)
                {
                    dt.Rows.Add(
                        false,                    // Selected (复选框，默认未选中)
                        item.id,                  // ItemId (隐藏列，存储API返回的ID)
                        item.itemName,            // 包名称
                        item.itemCode,            // 包条码
                        GetItemTypeText(item.itemType), // 包类型
                        item.itemattribute,       // 包属性
                        ""                        // 使用科室 (暂时为空)
                    );
                }

                // 刷新表格显示
                gridControl1.RefreshDataSource();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"填充数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 将数字类型转换为文本描述
        private string GetItemTypeText(int itemType)
        {
            // 根据实际业务需求定义类型映射
            switch (itemType)
            {
                case 0:
                    return "器械包";
                case 1:
                    return "敷料包";
                case 2:
                    return "混合包";
                default:
                    return "未知类型";
            }
        }

        // 获取选中项目的ID列表
        private List<int> GetSelectedItemIds()
        {
            List<int> selectedIds = new List<int>();

            try
            {
                DataTable dt = (DataTable)gridControl1.DataSource;

                foreach (DataRow row in dt.Rows)
                {
                    // 检查复选框是否选中
                    if (Convert.ToBoolean(row["Selected"]))
                    {
                        // 获取对应的ID
                        int id = Convert.ToInt32(row["ItemId"]);
                        selectedIds.Add(id);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"获取选中项目时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return selectedIds;
        }

        private async void btnConfirm_Click(object sender, EventArgs e)
        {
            // 调用新的添加包装登记方法
            await AddPackagingRegistration();
        }

        /// <summary>
        /// 添加包装登记的新方法
        /// 参数：打包人、检查人、打包时间、选中的设备ID列表
        /// </summary>
        private async Task AddPackagingRegistration()
        {
            try
            {
                // 获取界面输入的参数
                string packer = txtPackager.Text.Trim();
                string examiner = txtInspector.Text.Trim();
                DateTime packingTime = datePackaging.DateTime;
                List<int> selectedIds = GetSelectedItemIds();

                // 验证输入参数
                if (string.IsNullOrEmpty(packer))
                {
                    XtraMessageBox.Show("请输入打包人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPackager.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(examiner))
                {
                    XtraMessageBox.Show("请输入检查人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtInspector.Focus();
                    return;
                }

                if (selectedIds.Count == 0)
                {
                    XtraMessageBox.Show("请先选择要登记的项目", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 构建请求数据对象
                var requestData = new
                {
                    packer = packer,
                    examiner = examiner,
                    packingTime = packingTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    equipmentPackageId = selectedIds
                };

                // 调用API添加包装登记
                bool success = await SubmitPackagingRegistrationToApi(requestData);

                if (success)
                {
                    XtraMessageBox.Show("包装登记添加成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 清空输入框
                    ClearInputFields();

                    // 设置对话框结果为OK，通知父窗体操作成功
                    this.DialogResult = DialogResult.OK;

                    // 关闭当前窗体，自动返回到PackagingManagement页面
                    this.Close();
                }
                else
                {
                    XtraMessageBox.Show("包装登记添加失败，请重试", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"添加包装登记时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 提交包装登记数据到API
        /// </summary>
        /// <param name="requestData">请求数据对象</param>
        /// <returns>是否提交成功</returns>
        private async Task<bool> SubmitPackagingRegistrationToApi(object requestData)
        {
            try
            {
                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(requestData);

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"提交的JSON数据: {jsonData}");

                // 构建API URL
                string apiUrl = "http://localhost:5192/api/PackagingSterilization";
                //string apiUrl = "http://***********:4060/api/PackagingSterilization";

                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("POST", apiUrl, false, content);

                // 输出API返回结果
                System.Diagnostics.Debug.WriteLine($"API返回结果: {jsonResult}");

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    try
                    {
                        // 首先尝试解析为动态对象，避免类型转换错误
                        dynamic apiResponse = JsonConvert.DeserializeObject(jsonResult);

                        // 检查返回结果的格式
                        if (apiResponse != null)
                        {
                            // 检查是否包含code字段
                            if (apiResponse.code != null)
                            {
                                // 尝试将code转换为数字进行比较
                                string codeStr = apiResponse.code.ToString();
                                if (int.TryParse(codeStr, out int codeInt))
                                {
                                    return codeInt == 200;
                                }
                                // 如果code是字符串，检查是否为成功标识
                                return codeStr.Equals("Success", StringComparison.OrdinalIgnoreCase) ||
                                       codeStr.Equals("200", StringComparison.OrdinalIgnoreCase);
                            }

                            // 检查是否直接返回成功消息
                            string responseStr = jsonResult.ToLower();
                            if (responseStr.Contains("success") || responseStr.Contains("成功"))
                            {
                                return true;
                            }
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        // JSON解析失败，检查是否是简单的成功消息
                        System.Diagnostics.Debug.WriteLine($"JSON解析失败: {jsonEx.Message}");
                        string responseStr = jsonResult.ToLower();
                        if (responseStr.Contains("success") || responseStr.Contains("成功"))
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"提交包装登记数据异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                XtraMessageBox.Show($"提交数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 清空输入框
        /// </summary>
        private void ClearInputFields()
        {
            txtPackager.Text = "";
            txtInspector.Text = "";
            datePackaging.DateTime = DateTime.Now;

            // 清空表格选择
            DataTable dt = (DataTable)gridControl1.DataSource;
            if (dt != null)
            {
                foreach (DataRow row in dt.Rows)
                {
                    row["Selected"] = false;
                }
                gridControl1.RefreshDataSource();
            }
        }

        // 刷新数据方法
        public void RefreshData()
        {
            LoadDataFromApiAsync();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            // 退出按钮点击事件
            this.Close();
        }

        // 测试API连接的方法
        private async void TestApiConnection()
        {
            try
            {
                string apiUrl = $"{API_BASE_URL}/api/RecyclingCleaning/GetItemTableInfo";
                XtraMessageBox.Show($"正在测试API连接: {apiUrl}", "测试",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false, null);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    XtraMessageBox.Show($"API连接成功!\n返回数据长度: {jsonResult.Length}\n前100字符: {jsonResult.Substring(0, Math.Min(100, jsonResult.Length))}",
                        "测试结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    XtraMessageBox.Show("API连接失败，未返回数据", "测试结果",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"API测试失败: {ex.Message}", "测试结果",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 测试包装登记API的方法
        /// </summary>
        public async void TestPackagingRegistrationApi()
        {
            try
            {
                // 构建测试数据
                var testData = new
                {
                    packer = "测试打包人",
                    examiner = "测试检查人",
                    packingTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    equipmentPackageId = new List<int> { 1, 2 }
                };

                string jsonData = JsonConvert.SerializeObject(testData);
                string apiUrl = "http://localhost:5192/api/PackagingSterilization";
                //string apiUrl = "http://***********:4060/api/PackagingSterilization";

                XtraMessageBox.Show($"正在测试包装登记API:\nURL: {apiUrl}\n测试数据: {jsonData}",
                    "API测试", MessageBoxButtons.OK, MessageBoxIcon.Information);

                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                string result = await HttpClientHelper.ClientAsync("POST", apiUrl, false, content);

                XtraMessageBox.Show($"API返回结果:\n{result}",
                    "测试结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"测试失败: {ex.Message}\n详细信息: {ex}",
                    "测试错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.SystemManagement
{
    public partial class RoleManagementControl : UserControl
    {
        public RoleManagementControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // RoleManagementControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "RoleManagementControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.RoleManagementControl_Load);
            this.ResumeLayout(false);
        }

        private void RoleManagementControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "角色管理";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Create layout with two panels side by side
            SplitContainer splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Vertical;
            splitContainer.SplitterDistance = 300;
            splitContainer.Panel1MinSize = 250;
            splitContainer.Panel2MinSize = 250;
            this.Controls.Add(splitContainer);

            // === Left Panel (Role List) ===
            Panel leftPanel = new Panel();
            leftPanel.Dock = DockStyle.Fill;
            leftPanel.Padding = new Padding(10);
            splitContainer.Panel1.Controls.Add(leftPanel);

            // Add Role button
            Button addRoleButton = new Button();
            addRoleButton.Text = "添加角色";
            addRoleButton.Dock = DockStyle.Top;
            addRoleButton.Height = 30;
            addRoleButton.BackColor = Color.FromArgb(0, 120, 215);
            addRoleButton.ForeColor = Color.White;
            addRoleButton.Click += (s, ev) => ShowAddRoleDialog();
            leftPanel.Controls.Add(addRoleButton);

            // Role list (ListBox)
            ListBox roleListBox = new ListBox();
            roleListBox.Dock = DockStyle.Fill;
            roleListBox.BorderStyle = BorderStyle.FixedSingle;
            roleListBox.Font = new Font("微软雅黑", 10);
            roleListBox.Items.AddRange(new object[] {
                "系统管理员",
                "医生",
                "护士",
                "操作员",
                "部门经理",
                "质控员",
                "仓库管理员"
            });
            roleListBox.SelectedIndexChanged += (s, ev) => {
                if (roleListBox.SelectedItem != null)
                {
                    UpdateRoleDetails(roleListBox.SelectedItem.ToString());
                }
            };
            leftPanel.Controls.Add(roleListBox);
            
            // === Right Panel (Role Details) ===
            Panel rightPanel = new Panel();
            rightPanel.Dock = DockStyle.Fill;
            rightPanel.Padding = new Padding(10);
            splitContainer.Panel2.Controls.Add(rightPanel);

            // Role Details Title
            Label detailsTitle = new Label();
            detailsTitle.Text = "角色详情";
            detailsTitle.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            detailsTitle.Dock = DockStyle.Top;
            detailsTitle.Height = 30;
            rightPanel.Controls.Add(detailsTitle);

            // Permission tree
            TreeView permissionTree = new TreeView();
            permissionTree.Dock = DockStyle.Fill;
            permissionTree.CheckBoxes = true;
            permissionTree.Font = new Font("微软雅黑", 9);
            
            // Add sample nodes
            TreeNode systemNode = permissionTree.Nodes.Add("系统管理");
            systemNode.Nodes.Add("用户管理");
            systemNode.Nodes.Add("角色管理");
            systemNode.Nodes.Add("权限管理");
            systemNode.Expand();

            TreeNode deviceNode = permissionTree.Nodes.Add("设备管理");
            deviceNode.Nodes.Add("设备入库");
            deviceNode.Nodes.Add("设备出库");
            deviceNode.Nodes.Add("设备字典");
            deviceNode.Expand();

            TreeNode basicNode = permissionTree.Nodes.Add("基础数据");
            basicNode.Nodes.Add("设备字典");
            basicNode.Nodes.Add("供应商管理");
            basicNode.Nodes.Add("科室管理");
            basicNode.Expand();

            rightPanel.Controls.Add(permissionTree);

            // Save changes button
            Button saveButton = new Button();
            saveButton.Text = "保存更改";
            saveButton.Dock = DockStyle.Bottom;
            saveButton.Height = 30;
            saveButton.BackColor = Color.FromArgb(0, 120, 215);
            saveButton.ForeColor = Color.White;
            saveButton.Click += (s, ev) => {
                if (roleListBox.SelectedItem != null)
                {
                    MessageBox.Show($"已保存角色 '{roleListBox.SelectedItem}' 的权限设置", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            };
            rightPanel.Controls.Add(saveButton);

            // Select the first role by default
            if (roleListBox.Items.Count > 0)
            {
                roleListBox.SelectedIndex = 0;
            }
        }

        private void UpdateRoleDetails(string roleName)
        {
            // In a real app, this would load the permissions for the selected role
            // For this demo, we'll just update the title
            
            // Find the Panel2 of the parent SplitContainer
            if (this.Controls[1] is SplitContainer splitContainer)
            {
                Panel rightPanel = splitContainer.Panel2.Controls[0] as Panel;
                Label detailsTitle = rightPanel.Controls[0] as Label;
                detailsTitle.Text = $"角色详情: {roleName}";
                
                // Simulate loading different permissions by checking different nodes
                TreeView permissionTree = rightPanel.Controls[1] as TreeView;
                
                // Uncheck all nodes first
                UncheckAllNodes(permissionTree.Nodes);
                
                // Check specific nodes based on role
                if (roleName == "系统管理员")
                {
                    // Admin has all permissions
                    CheckAllNodes(permissionTree.Nodes);
                }
                else if (roleName == "医生" || roleName == "护士")
                {
                    // Medical staff can access device information
                    CheckNodeByText(permissionTree.Nodes, "设备字典");
                }
                else if (roleName == "操作员")
                {
                    // Operators can access device operations
                    CheckNodeByText(permissionTree.Nodes, "设备入库");
                    CheckNodeByText(permissionTree.Nodes, "设备出库");
                }
                else if (roleName == "仓库管理员")
                {
                    // Warehouse managers can access device and supplier management
                    CheckNodeByText(permissionTree.Nodes, "设备管理");
                    CheckNodeByText(permissionTree.Nodes, "供应商管理");
                }
            }
        }
        
        private void CheckAllNodes(TreeNodeCollection nodes)
        {
            foreach (TreeNode node in nodes)
            {
                node.Checked = true;
                CheckAllNodes(node.Nodes);
            }
        }
        
        private void UncheckAllNodes(TreeNodeCollection nodes)
        {
            foreach (TreeNode node in nodes)
            {
                node.Checked = false;
                UncheckAllNodes(node.Nodes);
            }
        }
        
        private void CheckNodeByText(TreeNodeCollection nodes, string text)
        {
            foreach (TreeNode node in nodes)
            {
                if (node.Text == text)
                {
                    node.Checked = true;
                }
                CheckNodeByText(node.Nodes, text);
            }
        }

        private void ShowAddRoleDialog()
        {
            // Create a simple dialog form for adding a role
            Form addRoleForm = new Form();
            addRoleForm.Text = "添加角色";
            addRoleForm.Width = 350;
            addRoleForm.Height = 220;
            addRoleForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            addRoleForm.StartPosition = FormStartPosition.CenterParent;
            addRoleForm.MaximizeBox = false;
            addRoleForm.MinimizeBox = false;

            // Role name
            Label nameLabel = new Label();
            nameLabel.Text = "角色名称:";
            nameLabel.Location = new Point(30, 30);
            addRoleForm.Controls.Add(nameLabel);

            TextBox nameTextBox = new TextBox();
            nameTextBox.Location = new Point(120, 30);
            nameTextBox.Width = 180;
            addRoleForm.Controls.Add(nameTextBox);

            // Description
            Label descLabel = new Label();
            descLabel.Text = "角色描述:";
            descLabel.Location = new Point(30, 70);
            addRoleForm.Controls.Add(descLabel);

            TextBox descTextBox = new TextBox();
            descTextBox.Location = new Point(120, 70);
            descTextBox.Width = 180;
            descTextBox.Height = 60;
            descTextBox.Multiline = true;
            addRoleForm.Controls.Add(descTextBox);

            // Save button
            Button saveButton = new Button();
            saveButton.Text = "保存";
            saveButton.Location = new Point(120, 150);
            saveButton.Width = 80;
            saveButton.BackColor = Color.FromArgb(0, 120, 215);
            saveButton.ForeColor = Color.White;
            saveButton.Click += (s, e) => {
                if (string.IsNullOrEmpty(nameTextBox.Text))
                {
                    MessageBox.Show("请填写角色名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                MessageBox.Show($"已添加角色: {nameTextBox.Text}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                addRoleForm.Close();
                
                // In a real app, you would add the role to the list
                if (this.Controls[1] is SplitContainer splitContainer)
                {
                    Panel leftPanel = splitContainer.Panel1.Controls[0] as Panel;
                    ListBox roleListBox = leftPanel.Controls[1] as ListBox;
                    roleListBox.Items.Add(nameTextBox.Text);
                    roleListBox.SelectedIndex = roleListBox.Items.Count - 1;
                }
            };
            addRoleForm.Controls.Add(saveButton);

            // Cancel button
            Button cancelButton = new Button();
            cancelButton.Text = "取消";
            cancelButton.Location = new Point(220, 150);
            cancelButton.Width = 80;
            cancelButton.Click += (s, e) => addRoleForm.Close();
            addRoleForm.Controls.Add(cancelButton);

            addRoleForm.ShowDialog();
        }
    }
} 
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="FluentDesignForm1.IconOptions.LargeImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAmdEVYdFRpdGxlAEFuYWx5c2lzO0RhdGFBbmFseXNp
        cztMaW5lU3R5bGU79o4ixAAAA4lJREFUWEfFl+lSE0EQx1dOz9fhEuSWokoFRF9BIkcCSTjFUiKHZfkE
        SjjFD0pJUnKLaCkCfpFNuJ+mrf/M9GYzZElAga36VffOv+fYnunsxiAi4yI50nDe8HXpgrAmTzMMI/0U
        ZPwD6C8Wkfbw5W86OZsaCdqGpH0wlBi1CCMdnfs+HlLH5C61M+O75BfskG98WzIWJf9YVFjfaIR8YxHy
        jkbJOxqhttEItYIRYJJnJEIe2GCE3CMRcsMGTXIHt6glaNLd5z+xAGTCSK/q/kbl/mUq9y/F2TL/EpXB
        +pap3Ae7JChl3yvvy7yLVIp2r51FYUtg2xaFLYFtW6TitgUqbl2wMoB9SLafmWcAxsXZO9MqwASJYN26
        9I6nRZ/ICY4XFxqc0pyVAD2F9v567HEgHn2NjONKJcaGtIObVD8ofV5MvWpzZGCD7g8q4CtUfyMTg6Fc
        ROmIUuGSMck9bFILCCo7bFLzsEl3nv3AANkAkzS/3TpCk503f6jRRvXT7+iPTBgZVV2rVomUtC7EWZSL
        nVueBSr2wM7zAFm3O1eoyDNPRW5mLua38P0cFbXEKGyeszKAfbDvn3iqJPAe8k8yfNYupwCPYf0OnOQE
        M6c5/TqiElIZ5KSTpRorFgCHU6inSk8X4G1i/YqC7/VYe4w91tqCTJxilIUolzjWLXiPrbb+dapzgBcD
        v/bFL6pVtg6+Da6CbEz2OnxIr0IH9HLmgIY+7dPA9D71T+9R4MMeVfeKkhGrx6C973foydQO9UxtU/e7
        beqajFLHhKSqZxWxVwEmkW9KU7wh5VsSb0iTKru+8phGVmXHSqw8YJtjpSKZtZ6qov0LFTbN0k07jZ+p
        wAbH4o2a3xim/MfhmAWuMOW5wpzVY/eKQSC/GeGjTTylxnGx1xQcC80qQz6VfHjsOJ1sPc4pVo+xx1pl
        qA+aaKBkutMEyXRxo6dVTyW2R08j2lLRUeK6Dlg3smv7USrxJaLKRJwBqy3Ado1qAkIXC65R9/F2TdPX
        6J6ywu8TOhYhJ2ifiMa9rVSZXAc1CfSKzpWYHlg7opd3CP0GwMS6XiZ1ZEKWS4EqkTxVJvmukPUE+CjN
        c4UkDSHKbZA+pxMfprkNM6JdIn3W8cGa80i25TTMCB/w7wDvIYLFEykfIn/9wE9FF0+sYnQdbbouzkCy
        U+qkc4yTloouqkC/eFL94vb/q+v/Vs+bv04bdKC1vcOWAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="fluentFormDefaultManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>
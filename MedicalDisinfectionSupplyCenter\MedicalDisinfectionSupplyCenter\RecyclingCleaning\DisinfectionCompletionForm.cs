using System;
using System.Drawing;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public partial class DisinfectionCompletionForm : Form
    {
        public string FinishPerson { get; private set; }
        public DateTime FinishTime { get; private set; }
        public string DisinfectionResult { get; private set; }

        private TextBox txtFinishPerson;
        private DateTimePicker dtpFinishTime;
        private ComboBox cmbDisinfectionResult;
        private Button btnOK;
        private Button btnCancel;
        private Label lblDisinfectionBatch;
        private Label lblFinishPerson;
        private Label lblFinishTime;
        private Label lblDisinfectionResult;

        public DisinfectionCompletionForm(string disinfectionBatch)
        {
            InitializeComponent();
            lblDisinfectionBatch.Text = $"消毒批次：{disinfectionBatch}";
            
            // 设置默认值
            dtpFinishTime.Value = DateTime.Now;
            txtFinishPerson.Text = Environment.UserName; // 默认当前用户
        }

        private void InitializeComponent()
        {
            this.lblDisinfectionBatch = new System.Windows.Forms.Label();
            this.lblFinishPerson = new System.Windows.Forms.Label();
            this.txtFinishPerson = new System.Windows.Forms.TextBox();
            this.lblFinishTime = new System.Windows.Forms.Label();
            this.dtpFinishTime = new System.Windows.Forms.DateTimePicker();
            this.lblDisinfectionResult = new System.Windows.Forms.Label();
            this.cmbDisinfectionResult = new System.Windows.Forms.ComboBox();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // lblDisinfectionBatch
            // 
            this.lblDisinfectionBatch.AutoSize = true;
            this.lblDisinfectionBatch.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.lblDisinfectionBatch.Location = new System.Drawing.Point(30, 30);
            this.lblDisinfectionBatch.Name = "lblDisinfectionBatch";
            this.lblDisinfectionBatch.Size = new System.Drawing.Size(120, 31);
            this.lblDisinfectionBatch.TabIndex = 0;
            this.lblDisinfectionBatch.Text = "消毒批次：";
            // 
            // lblFinishPerson
            // 
            this.lblFinishPerson.AutoSize = true;
            this.lblFinishPerson.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblFinishPerson.Location = new System.Drawing.Point(30, 80);
            this.lblFinishPerson.Name = "lblFinishPerson";
            this.lblFinishPerson.Size = new System.Drawing.Size(100, 27);
            this.lblFinishPerson.TabIndex = 1;
            this.lblFinishPerson.Text = "完成人员：";
            // 
            // txtFinishPerson
            // 
            this.txtFinishPerson.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtFinishPerson.Location = new System.Drawing.Point(150, 77);
            this.txtFinishPerson.Name = "txtFinishPerson";
            this.txtFinishPerson.Size = new System.Drawing.Size(200, 34);
            this.txtFinishPerson.TabIndex = 2;
            // 
            // lblFinishTime
            // 
            this.lblFinishTime.AutoSize = true;
            this.lblFinishTime.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblFinishTime.Location = new System.Drawing.Point(30, 130);
            this.lblFinishTime.Name = "lblFinishTime";
            this.lblFinishTime.Size = new System.Drawing.Size(100, 27);
            this.lblFinishTime.TabIndex = 3;
            this.lblFinishTime.Text = "完成时间：";
            // 
            // dtpFinishTime
            // 
            this.dtpFinishTime.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            this.dtpFinishTime.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.dtpFinishTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpFinishTime.Location = new System.Drawing.Point(150, 127);
            this.dtpFinishTime.Name = "dtpFinishTime";
            this.dtpFinishTime.Size = new System.Drawing.Size(200, 34);
            this.dtpFinishTime.TabIndex = 4;
            // 
            // lblDisinfectionResult
            // 
            this.lblDisinfectionResult.AutoSize = true;
            this.lblDisinfectionResult.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblDisinfectionResult.Location = new System.Drawing.Point(30, 180);
            this.lblDisinfectionResult.Name = "lblDisinfectionResult";
            this.lblDisinfectionResult.Size = new System.Drawing.Size(100, 27);
            this.lblDisinfectionResult.TabIndex = 5;
            this.lblDisinfectionResult.Text = "消毒结果：";
            // 
            // cmbDisinfectionResult
            // 
            this.cmbDisinfectionResult.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDisinfectionResult.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.cmbDisinfectionResult.FormattingEnabled = true;
            this.cmbDisinfectionResult.Items.AddRange(new object[] {
            "合格",
            "不合格",
            "需重新消毒"});
            this.cmbDisinfectionResult.Location = new System.Drawing.Point(150, 177);
            this.cmbDisinfectionResult.Name = "cmbDisinfectionResult";
            this.cmbDisinfectionResult.Size = new System.Drawing.Size(200, 35);
            this.cmbDisinfectionResult.TabIndex = 6;
            // 
            // btnOK
            // 
            this.btnOK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(123)))), ((int)(((byte)(255)))));
            this.btnOK.FlatAppearance.BorderSize = 0;
            this.btnOK.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnOK.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnOK.ForeColor = System.Drawing.Color.White;
            this.btnOK.Location = new System.Drawing.Point(150, 240);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(90, 40);
            this.btnOK.TabIndex = 7;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = false;
            this.btnOK.Click += new System.EventHandler(this.BtnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(117)))), ((int)(((byte)(125)))));
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(260, 240);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(90, 40);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // DisinfectionCompletionForm
            // 
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(400, 320);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.cmbDisinfectionResult);
            this.Controls.Add(this.lblDisinfectionResult);
            this.Controls.Add(this.dtpFinishTime);
            this.Controls.Add(this.lblFinishTime);
            this.Controls.Add(this.txtFinishPerson);
            this.Controls.Add(this.lblFinishPerson);
            this.Controls.Add(this.lblDisinfectionBatch);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "DisinfectionCompletionForm";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "消毒完成";
            this.Load += new System.EventHandler(this.DisinfectionCompletionForm_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private void DisinfectionCompletionForm_Load(object sender, EventArgs e)
        {
            // 设置默认选择
            cmbDisinfectionResult.SelectedIndex = 0; // 默认选择"合格"
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(txtFinishPerson.Text))
            {
                MessageBox.Show("请输入完成人员", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFinishPerson.Focus();
                return;
            }

            if (cmbDisinfectionResult.SelectedIndex == -1)
            {
                MessageBox.Show("请选择消毒结果", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbDisinfectionResult.Focus();
                return;
            }

            // 保存数据
            FinishPerson = txtFinishPerson.Text.Trim();
            FinishTime = dtpFinishTime.Value;
            DisinfectionResult = cmbDisinfectionResult.SelectedItem.ToString();

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}

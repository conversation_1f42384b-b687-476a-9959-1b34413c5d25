using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.BasicManagement
{
    /// <summary>
    /// 设备字典管理界面
    /// </summary>
    public partial class DeviceDictionary : UserControl
    {
        #region 常量与API客户端
        private const string API_BASE_URL = "http://localhost:5172/api/BasicManagement";
        //private const string API_BASE_URL = "http://***********:4050/api/BasicManagement";
        // TODO: 请您确认后端的【写入】端口是否为5173，如果不是，请修改此处
        private const string API_WRITE_BASE_URL = "http://localhost:5192/api/BasicManagement";
        //private const string API_WRITE_BASE_URL = "http://***********:4060/api/BasicManagement";
        private static readonly HttpClient httpClient = new HttpClient();
        #endregion

        #region 控件声明
        private LayoutControl layoutControl;
        private LayoutControlGroup rootGroup;
        private LayoutControlItem layoutControlItemToolbar;
        private PanelControl panelToolbar;
        private ComboBoxEdit comboDeviceType;
        private CheckEdit chkShowDisabled;
        private SimpleButton btnQuery;
        private SimpleButton btnAdd;
        private LayoutControlItem layoutControlItemGrid;
        private GridControl gridDevice;
        private GridView gridViewDevice;
        private LabelControl lblTotal;
        #endregion

        /// <summary>
        /// 初始化设备字典界面
        /// </summary>
        public DeviceDictionary()
        {
            InitializeComponent();
            InitializeControls();
            SetupLayout();
            InitializeGrid();

            // 配置HttpClient
            httpClient.Timeout = TimeSpan.FromSeconds(30);
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        /// <summary>
        /// 初始化所有控件
        /// </summary>
        private void InitializeControls()
        {
            // 主布局控件
            layoutControl = new LayoutControl();
            layoutControl.Dock = DockStyle.Fill;
            layoutControl.Name = "layoutControl";

            // 工具栏面板 - 增加高度以提供更好间距
            panelToolbar = new PanelControl();
            panelToolbar.Name = "panelToolbar";
            panelToolbar.Height = 80;
            panelToolbar.Dock = DockStyle.Top;
            panelToolbar.Padding = new Padding(10, 10, 10, 10);

            // 设备类型标签
            var lblDeviceType = new LabelControl();
            lblDeviceType.Location = new Point(20, 28);
            lblDeviceType.Size = new Size(70, 28);
            lblDeviceType.Text = "设备类型：";
            lblDeviceType.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 设备类型下拉框 - 与Equipment表设备类型对应
            comboDeviceType = new ComboBoxEdit();
            comboDeviceType.Name = "comboDeviceType";
            comboDeviceType.Properties.Items.AddRange(new object[] { "所有", "清洗设备", "消毒设备", "灭菌设备" });
            comboDeviceType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            comboDeviceType.SelectedIndex = 0;
            comboDeviceType.Location = new Point(100, 25);
            comboDeviceType.Width = 140;

            // 显示停用复选框 - 增加间距
            chkShowDisabled = new CheckEdit();
            chkShowDisabled.Name = "chkShowDisabled";
            chkShowDisabled.Text = "显示停用";
            chkShowDisabled.Checked = true; // 默认设置为选中状态
            chkShowDisabled.Location = new Point(260, 28);

            // 查询按钮 - 增加间距
            btnQuery = new SimpleButton();
            btnQuery.Name = "btnQuery";
            btnQuery.Text = "查询";
            btnQuery.Appearance.BackColor = Color.FromArgb(0, 120, 215);
            btnQuery.Appearance.ForeColor = Color.White;
            btnQuery.Appearance.Options.UseBackColor = true;
            btnQuery.Appearance.Options.UseForeColor = true;
            btnQuery.Location = new Point(380, 25);
            btnQuery.Width = 80;
            btnQuery.Click += btnQuery_Click;

            // 新增按钮 - 增加间距
            btnAdd = new SimpleButton();
            btnAdd.Name = "btnAdd";
            btnAdd.Text = "新增";
            btnAdd.Appearance.BackColor = Color.FromArgb(40, 167, 69);
            btnAdd.Appearance.ForeColor = Color.White;
            btnAdd.Appearance.Options.UseBackColor = true;
            btnAdd.Appearance.Options.UseForeColor = true;
            btnAdd.Location = new Point(480, 25);
            btnAdd.Width = 80;
            btnAdd.Click += btnAdd_Click;



            // 将控件添加到面板
            panelToolbar.Controls.Add(lblDeviceType);
            panelToolbar.Controls.Add(comboDeviceType);
            panelToolbar.Controls.Add(chkShowDisabled);
            panelToolbar.Controls.Add(btnQuery);
            panelToolbar.Controls.Add(btnAdd);


            // 设备列表表格
            gridDevice = new GridControl();
            gridDevice.Name = "gridDevice";
            gridDevice.Dock = DockStyle.Fill;
            gridViewDevice = new GridView(gridDevice);
            gridViewDevice.Name = "gridViewDevice";
            gridDevice.MainView = gridViewDevice;

            // 合计标签
            lblTotal = new LabelControl();
            lblTotal.Name = "lblTotal";
            lblTotal.Text = "合计：0条";
            lblTotal.Location = new Point(10, panelToolbar.Height + gridDevice.Height + 10);

            // 将主控件添加到用户控件
            this.Controls.Add(layoutControl);

        }

        /// <summary>
        /// 设置布局
        /// </summary>
        private void SetupLayout()
        {
            rootGroup = new LayoutControlGroup();
            rootGroup.Name = "rootGroup";
            rootGroup.TextVisible = false;

            // 工具栏布局项 - 同步调整高度
            layoutControlItemToolbar = new LayoutControlItem();
            layoutControlItemToolbar.Control = panelToolbar;
            layoutControlItemToolbar.Name = "layoutControlItemToolbar";
            layoutControlItemToolbar.TextVisible = false;
            layoutControlItemToolbar.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            layoutControlItemToolbar.MaxSize = new Size(0, 80);
            layoutControlItemToolbar.MinSize = new Size(0, 80);

            // 表格布局项
            layoutControlItemGrid = new LayoutControlItem();
            layoutControlItemGrid.Control = gridDevice;
            layoutControlItemGrid.Name = "layoutControlItemGrid";
            layoutControlItemGrid.TextVisible = false;

            rootGroup.AddItem(layoutControlItemToolbar);
            rootGroup.AddItem(layoutControlItemGrid);
            layoutControl.Root.AddGroup(rootGroup);
        }

        /// <summary>
        /// 初始化表格列 - 与后端Equipment表结构对应
        /// </summary>
        private void InitializeGrid()
        {
            gridViewDevice.OptionsView.ShowGroupPanel = false;
            gridViewDevice.OptionsView.ShowFooter = true;
            gridViewDevice.OptionsBehavior.Editable = true; // 允许表格编辑以使滑块可交互
            gridViewDevice.OptionsSelection.MultiSelect = false;

            // 添加列 - 对应Equipment实体字段
            var columns = new DevExpress.XtraGrid.Columns.GridColumn[] {
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "设备编码", FieldName = "EquipmentCode", Visible = true, Width = 120 },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "设备名称", FieldName = "EquipmentName", Visible = true, Width = 150 },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "设备类型", FieldName = "EquipmentTypeName", Visible = true, Width = 100 },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "生产厂家", FieldName = "ManufacturerName", Visible = true, Width = 120 },
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "状态", FieldName = "Status", Visible = true, Width = 80 }, // 绑定到Status ID
                new DevExpress.XtraGrid.Columns.GridColumn { Caption = "创建时间", FieldName = "CreateTime", Visible = true, Width = 150 }
                // 备注列已被移除
            };
            gridViewDevice.Columns.AddRange(columns);

            // 添加操作列
            var operationColumn = new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "操作",
                Visible = true,
                Width = 160, // 调整宽度以容纳两个按钮
            };
            gridViewDevice.Columns.Add(operationColumn);

            // 创建并配置按钮编辑器
            var buttonRepo = new RepositoryItemButtonEdit();
            buttonRepo.TextEditStyle = TextEditStyles.HideTextEditor; // 只显示按钮，不显示文本框
            buttonRepo.ButtonsStyle = BorderStyles.Simple;

            // 添加 "编辑" 按钮
            buttonRepo.Buttons.Add(new EditorButton(ButtonPredefines.Glyph) { Caption = "编辑", Kind = ButtonPredefines.Glyph });
            // 添加 "删除" 按钮
            buttonRepo.Buttons.Add(new EditorButton(ButtonPredefines.Glyph) { Caption = "删除", Kind = ButtonPredefines.Glyph });
            
            // 订阅按钮点击事件
            buttonRepo.ButtonClick += OperationButton_Click;

            // 将按钮编辑器分配给操作列
            operationColumn.ColumnEdit = buttonRepo;
            // 确保按钮总是可见的
            gridViewDevice.OptionsView.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;

            // 默认将所有列设为只读
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridViewDevice.Columns)
            {
                col.OptionsColumn.AllowEdit = false;
            }
             // 明确允许状态列和操作列可编辑
            gridViewDevice.Columns["Status"].OptionsColumn.AllowEdit = true;
            operationColumn.OptionsColumn.AllowEdit = true;


            // --- 将状态列改造为ToggleSwitch ---
            var statusColumn = gridViewDevice.Columns["Status"];
            if (statusColumn != null)
            {
                var toggleSwitchRepo = new RepositoryItemToggleSwitch();
                toggleSwitchRepo.Toggled += OnStatusToggled; // 订阅状态切换事件
                toggleSwitchRepo.OnText = "正常";
                toggleSwitchRepo.OffText = "停用";
                // 将1映射为On(true), 0映射为Off(false)，以匹配后端API的期望
                toggleSwitchRepo.ValueOn = 1; 
                toggleSwitchRepo.ValueOff = 0;

                statusColumn.ColumnEdit = toggleSwitchRepo;
            }
            // ------------------------------------

            // 配置空值显示
            gridViewDevice.OptionsBehavior.AllowPixelScrolling = DevExpress.Utils.DefaultBoolean.True;
            gridViewDevice.Appearance.Empty.BackColor = Color.White;
            gridViewDevice.Appearance.Empty.Options.UseBackColor = true;

            // 设置创建时间列的显示格式
            var colCreateTime = gridViewDevice.Columns["CreateTime"];
            if (colCreateTime != null)
            {
                colCreateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colCreateTime.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm";
            }
        }

        /// <summary>
        /// 操作列按钮点击事件
        /// </summary>
        private void OperationButton_Click(object sender, ButtonPressedEventArgs e)
        {
            var rowHandle = gridViewDevice.FocusedRowHandle;
            if (rowHandle < 0) return;
            var equipment = gridViewDevice.GetRow(rowHandle) as EquipmentDto;
            if (equipment == null) return;

            if (e.Button.Caption == "编辑")
            {
                EditDevice(equipment);
            }
            else if (e.Button.Caption == "删除")
            {
                DeleteDevice(equipment);
            }
        }

        /// <summary>
        /// 编辑设备 - 完整实现
        /// </summary>
        private void EditDevice(EquipmentDto equipment)
        {
            using (var editForm = new Form())
            {
                editForm.Text = "设备编辑";
                editForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                editForm.StartPosition = FormStartPosition.CenterParent;
                editForm.Width = 430;
                editForm.Height = 340;
                editForm.MaximizeBox = false;
                editForm.MinimizeBox = false;

                // 设备类型
                var lblType = new Label() { Text = "设备类型", Left = 30, Top = 30, Width = 80 };
                var cmbType = new System.Windows.Forms.ComboBox() { Left = 120, Top = 28, Width = 250, DropDownStyle = ComboBoxStyle.DropDownList };
                cmbType.Items.AddRange(new object[] { "清洗设备", "消毒设备", "灭菌设备" });
                cmbType.SelectedIndex = (equipment.EquipmentType > 0 && equipment.EquipmentType <= 3) ? equipment.EquipmentType - 1 : 0;

                // *设备编码
                var lblCodeStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 70, Width = 10 };
                var lblCode = new Label() { Text = "设备编码", Left = 30, Top = 70, Width = 80 };
                var txtCode = new TextBox() { Left = 120, Top = 68, Width = 250, Text = equipment.EquipmentCode };

                // *设备名称
                var lblNameStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 110, Width = 10 };
                var lblName = new Label() { Text = "设备名称", Left = 30, Top = 110, Width = 80 };
                var txtName = new TextBox() { Left = 120, Top = 108, Width = 250, Text = equipment.EquipmentName };

                // 生产厂家
                var lblManufacturer = new Label() { Text = "生产厂家", Left = 30, Top = 150, Width = 80 };
                var cmbManufacturer = new System.Windows.Forms.ComboBox() { Left = 120, Top = 148, Width = 250, DropDownStyle = ComboBoxStyle.DropDownList };
                
                // 从枚举动态加载生产厂家
                var manufacturerValues = Enum.GetValues(typeof(Manufacturer));
                var manufacturerDataSource = new List<dynamic>();
                foreach (Manufacturer mf in manufacturerValues)
                {
                    manufacturerDataSource.Add(new { Name = EnumHelper.GetDescription(mf), Value = mf });
                }
                cmbManufacturer.DataSource = manufacturerDataSource;
                cmbManufacturer.DisplayMember = "Name";
                cmbManufacturer.ValueMember = "Value";

                // 设置选中项
                cmbManufacturer.SelectedValue = (Manufacturer)equipment.Manufacturer;


                // 保存按钮
                var btnSave = new Button() { Text = "保存", Left = 220, Top = 220, Width = 80, BackColor = Color.FromArgb(0, 120, 215), ForeColor = Color.White, FlatStyle = FlatStyle.Flat };
                btnSave.FlatAppearance.BorderSize = 0;
                
                // 退出按钮
                var btnCancel = new Button() { Text = "退出", Left = 120, Top = 220, Width = 80, FlatStyle = FlatStyle.Flat };
                btnCancel.FlatAppearance.BorderColor = Color.Gray;
                btnCancel.FlatAppearance.BorderSize = 1;


                btnCancel.Click += (s, ev) => editForm.DialogResult = DialogResult.Cancel;

                btnSave.Click += async (s, ev) =>
                {
                    // 必填校验
                    if (string.IsNullOrWhiteSpace(txtCode.Text))
                    {
                        MessageBox.Show("设备编码不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(txtName.Text))
                    {
                        MessageBox.Show("设备名称不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    // 构造请求对象
                    var updateDevice = new
                    {
                        Id = equipment.Id,
                        EquipmentCode = txtCode.Text.Trim(),
                        EquipmentName = txtName.Text.Trim(),
                        EquipmentType = cmbType.SelectedIndex + 1, // 假设后端类型为1/2/3
                        Manufacturer = ((int)cmbManufacturer.SelectedValue).ToString(),
                        Status = equipment.Status // 保持原状态
                    };
                    try
                    {
                        var url = $"{API_WRITE_BASE_URL}/UpdateEquipment";
                        var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(updateDevice);
                        var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
                        var response = await httpClient.PutAsync(url, content);
                        if (response.IsSuccessStatusCode)
                        {
                            MessageBox.Show("设备信息更新成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            editForm.DialogResult = DialogResult.OK;
                        }
                        else
                        {
                            var errorContent = await response.Content.ReadAsStringAsync();
                            MessageBox.Show($"更新失败: {errorContent}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"网络异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                editForm.Controls.Add(lblType);
                editForm.Controls.Add(cmbType);
                editForm.Controls.Add(lblCodeStar);
                editForm.Controls.Add(lblCode);
                editForm.Controls.Add(txtCode);
                editForm.Controls.Add(lblNameStar);
                editForm.Controls.Add(lblName);
                editForm.Controls.Add(txtName);
                editForm.Controls.Add(lblManufacturer);
                editForm.Controls.Add(cmbManufacturer);
                editForm.Controls.Add(btnSave);
                editForm.Controls.Add(btnCancel);

                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadEquipmentData(); // 编辑成功后刷新主表格
                }
            }
        }

        /// <summary>
        /// 删除设备 - 包含确认对话框和API调用
        /// </summary>
        private async void DeleteDevice(EquipmentDto equipment)
        {
            var result = MessageBox.Show($"您确定要删除设备 '{equipment.EquipmentName}' 吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (result == DialogResult.Yes)
            {
                var (success, errorMessage) = await DeleteEquipmentAsync(equipment.Id);
                if (success)
                {
                    MessageBox.Show("设备删除成功。", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadEquipmentData(); // 重新加载数据
                }
                else
                {
                    MessageBox.Show($"删除失败: {errorMessage}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 调用API删除设备
        /// </summary>
        private async Task<(bool, string)> DeleteEquipmentAsync(int equipmentId)
        {
            try
            {
                var url = $"{API_WRITE_BASE_URL}/DeleteEquipment/{equipmentId}";
                var response = await httpClient.DeleteAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    return (true, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMessage = $"API请求失败 (状态码: {response.StatusCode})。详情: {errorContent}";
                    System.Diagnostics.Debug.WriteLine(errorMessage);
                    return (false, errorMessage);
                }
            }
            catch (Exception ex)
            {
                var innerExceptionMessage = ex.InnerException?.Message;
                var detailedMessage = string.IsNullOrEmpty(innerExceptionMessage) ? ex.Message : $"{ex.Message} (内部错误: {innerExceptionMessage})";
                var errorMessage = $"网络或连接异常: {detailedMessage}";
                System.Diagnostics.Debug.WriteLine(errorMessage);
                return (false, errorMessage);
            }
        }


        /// <summary>
        /// 状态滑块切换时的事件处理
        /// </summary>
        private async void OnStatusToggled(object sender, EventArgs e)
        {
            // 确保UI更新完成
            gridViewDevice.PostEditor();

            var toggle = (ToggleSwitch)sender;
            // 获取当前操作的行
            var rowHandle = gridViewDevice.FocusedRowHandle;
            if (rowHandle < 0) return;

            var equipment = gridViewDevice.GetRow(rowHandle) as EquipmentDto;
            if (equipment == null) return;

            // 新的状态ID (1:正常, 0:停用)，以匹配后端API的期望
            int newStatus = toggle.IsOn ? 1 : 0;

            // 禁用滑块防止重复点击
            toggle.Enabled = false;

            try
            {
                var (success, errorMessage) = await UpdateEquipmentStatusAsync(equipment.Id, newStatus);
                if (success)
                {
                    // 更新成功后，刷新数据以显示最新状态
                    // 可以只刷新当前行，但完全刷新更稳妥
                    await Task.Delay(200); // 短暂延迟以获得更好的用户反馈
                    LoadEquipmentData();
                }
                else
                {
                    // 如果失败，将滑块恢复到原始状态并显示详细错误
                    MessageBox.Show($"状态更新失败: {errorMessage}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    toggle.IsOn = !toggle.IsOn;
                }
            }
            finally
            {
                // 重新启用滑块
                toggle.Enabled = true;
            }
        }

        /// <summary>
        /// 调用API更新设备状态
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="newStatus">新的状态ID (1:正常, 2:停用)</param>
        /// <returns>一个元组，包含成功状态和可选的错误消息</returns>
        private async Task<(bool, string)> UpdateEquipmentStatusAsync(int equipmentId, int newStatus)
        {
            try
            {
                // 使用专门的写入API地址
                var url = $"{API_WRITE_BASE_URL}/UpdateEquipmentStatus";
                var updateRequest = new { Id = equipmentId, Status = newStatus };

                // 手动序列化内容
                var jsonContent = JsonConvert.SerializeObject(updateRequest);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 后端不支持PATCH，改回使用PUT方法
                var response = await httpClient.PutAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    return (true, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMessage = $"API请求失败 (状态码: {response.StatusCode})。详情: {errorContent}";
                    System.Diagnostics.Debug.WriteLine(errorMessage);
                    return (false, errorMessage);
                }
            }
            catch (Exception ex)
            {
                // 尝试获取更详细的内部异常信息
                var innerExceptionMessage = ex.InnerException?.Message;
                var detailedMessage = string.IsNullOrEmpty(innerExceptionMessage) ? ex.Message : $"{ex.Message} (内部错误: {innerExceptionMessage})";

                var errorMessage = $"网络或连接异常: {detailedMessage}";
                System.Diagnostics.Debug.WriteLine(errorMessage);
                return (false, errorMessage);
            }
        }

        /// <summary>
        /// 从API加载设备数据
        /// 异步获取后端Equipment表数据
        /// </summary>
        private async void LoadEquipmentData()
        {
            try
            {
                // 显示加载状态
                btnQuery.Enabled = false;
                btnQuery.Text = "加载中...";

                // 构建API请求URL
                var url = $"{API_BASE_URL}/QueryEquipment";

                // 添加查询参数
                var parameters = new List<string>();
                if (comboDeviceType.SelectedIndex > 0)
                {
                    // 后端需要的是设备类型的整数ID，而不是文本
                    parameters.Add($"equipmentType={comboDeviceType.SelectedIndex}");
                }
                // 根据复选框状态决定是否添加status参数
                // 未选中：只看正常的 (status=1)
                // 选中：不加参数，看所有状态
                if (!chkShowDisabled.Checked)
                {
                    parameters.Add("status=1");
                }

                if (parameters.Any())
                {
                    url += "?" + string.Join("&", parameters);
                }



                // 发送HTTP请求
                var response = await httpClient.GetAsync(url);



                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    MessageBox.Show($"API请求失败：{response.StatusCode}\n详细信息：{errorContent}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var json = await response.Content.ReadAsStringAsync();

                try
                {
                    // 解析分页格式（基于实际API响应格式）
                    var pageResult = JsonConvert.DeserializeObject<ApiPageResult<EquipmentDto>>(json);

                    if (pageResult?.Success == true && pageResult.Data?.Items != null)
                    {
                        // 数据映射：将ID转换为可读的名称
                        var equipmentTypeMap = new Dictionary<int, string>
                        {
                            { 1, "清洗设备" },
                            { 2, "消毒设备" },
                            { 3, "灭菌设备" }
                        };
                        var statusMap = new Dictionary<int, string>
                        {
                            { 1, "正常" },
                            { 2, "停用" }
                        };

                        foreach (var item in pageResult.Data.Items)
                        {
                            // 如果API未返回类型名称，则根据类型ID进行映射
                            if (string.IsNullOrEmpty(item.EquipmentTypeName))
                            {
                                item.EquipmentTypeName = equipmentTypeMap.TryGetValue(item.EquipmentType, out var typeName)
                                    ? typeName
                                    : $"未知类型 ({item.EquipmentType})";
                            }
                            // ToggleSwitch会根据Status ID自动显示状态，不再需要手动映射StatusName
                            // 如果API未返回生产厂家名称，则根据ID进行映射
                            if (string.IsNullOrEmpty(item.ManufacturerName))
                            {
                                // 检查ID是否在枚举中定义，然后获取其描述
                                if (Enum.IsDefined(typeof(Manufacturer), item.Manufacturer))
                                {
                                    item.ManufacturerName = EnumHelper.GetDescription((Manufacturer)item.Manufacturer);
                                }
                                else
                                {
                                    item.ManufacturerName = $"未知厂家 ({item.Manufacturer})";
                                }
                            }
                        }

                        // 绑定分页数据到Grid
                        gridDevice.DataSource = pageResult.Data.Items;

                        // 强制刷新网格显示
                        gridViewDevice.RefreshData();
                        gridViewDevice.BestFitColumns();

                        // 更新状态信息
                        lblTotal.Text = $"共 {pageResult.Data.TotalCount} 条记录，当前显示 {pageResult.Data.Items.Count} 条";

                        // 如果没有数据，显示提示
                        if (pageResult.Data.Items.Count == 0)
                        {
                            MessageBox.Show("没有查询到相关设备数据", "提示",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    else
                    {
                        var errorMsg = pageResult?.Msg ?? "获取数据失败";
                        MessageBox.Show(errorMsg, "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (JsonException jsonEx)
                {
                    // 记录原始JSON数据用于调试
                    System.Diagnostics.Debug.WriteLine($"解析异常: {jsonEx}");
                    System.Diagnostics.Debug.WriteLine($"原始响应数据: {json}");
                    MessageBox.Show($"数据格式错误详情：\n{jsonEx.Message}\n\n原始数据：\n{json.Substring(0, Math.Min(json.Length, 500))}...",
                        "数据格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (HttpRequestException ex)
            {
                MessageBox.Show($"网络连接失败：{ex.Message}\n请检查网络连接和API服务是否正常运行", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (TaskCanceledException ex)
            {
                MessageBox.Show($"请求超时：{ex.Message}\n请检查网络连接或稍后重试", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (JsonException ex)
            {
                MessageBox.Show($"数据格式错误：{ex.Message}\n请检查API返回的数据格式", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnQuery.Enabled = true;
                btnQuery.Text = "查询";
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private void btnQuery_Click(object sender, EventArgs e)
        {
            LoadEquipmentData();
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private void btnAdd_Click(object sender, EventArgs e)
        {
            // 弹出新增设备窗口（不新建类，直接在此方法内实现）
            using (var addForm = new Form())
            {
                addForm.Text = "新增设备";
                addForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                addForm.StartPosition = FormStartPosition.CenterParent;
                addForm.Width = 430;
                addForm.Height = 340;
                addForm.MaximizeBox = false;
                addForm.MinimizeBox = false;

                // 设备类型
                var lblType = new Label() { Text = "设备类型", Left = 30, Top = 30, Width = 80 };
                var cmbType = new System.Windows.Forms.ComboBox() { Left = 120, Top = 28, Width = 250, DropDownStyle = ComboBoxStyle.DropDownList };
                cmbType.Items.AddRange(new object[] { "清洗设备", "消毒设备", "灭菌设备" });
                cmbType.SelectedIndex = 0;

                // *设备编码
                var lblCodeStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 70, Width = 10 };
                var lblCode = new Label() { Text = "设备编码", Left = 30, Top = 70, Width = 80 };
                var txtCode = new TextBox() { Left = 120, Top = 68, Width = 250 };

                // *设备名称
                var lblNameStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 110, Width = 10 };
                var lblName = new Label() { Text = "设备名称", Left = 30, Top = 110, Width = 80 };
                var txtName = new TextBox() { Left = 120, Top = 108, Width = 250 };

                // 生产厂家
                var lblManufacturer = new Label() { Text = "生产厂家", Left = 30, Top = 150, Width = 80 };
                var cmbManufacturer = new System.Windows.Forms.ComboBox() { Left = 120, Top = 148, Width = 250, DropDownStyle = ComboBoxStyle.DropDownList };

                // 从枚举动态加载生产厂家
                var manufacturerValues = Enum.GetValues(typeof(Manufacturer));
                var manufacturerDataSource = new List<dynamic>();
                foreach (Manufacturer mf in manufacturerValues)
                {
                    manufacturerDataSource.Add(new { Name = EnumHelper.GetDescription(mf), Value = mf });
                }
                cmbManufacturer.DataSource = manufacturerDataSource;
                cmbManufacturer.DisplayMember = "Name";
                cmbManufacturer.ValueMember = "Value";
                // 将默认选中项设置为 "富士康"，其对应枚举值为0。
                // 使用SelectedValue比SelectedIndex更可靠，特别是在数据绑定后。
                cmbManufacturer.SelectedValue = Manufacturer.富士康;

                // 保存按钮
                var btnSave = new Button() { Text = "保存", Left = 220, Top = 220, Width = 80, BackColor = Color.FromArgb(0, 120, 215), ForeColor = Color.White, FlatStyle = FlatStyle.Flat };
                btnSave.FlatAppearance.BorderSize = 0;

                // 退出按钮
                var btnCancel = new Button() { Text = "退出", Left = 120, Top = 220, Width = 80, FlatStyle = FlatStyle.Flat };
                btnCancel.FlatAppearance.BorderColor = Color.Gray;
                btnCancel.FlatAppearance.BorderSize = 1;


                btnCancel.Click += (s, ev) => addForm.DialogResult = DialogResult.Cancel;

                btnSave.Click += async (s, ev) =>
                {
                    // 必填校验
                    if (string.IsNullOrWhiteSpace(txtCode.Text))
                    {
                        MessageBox.Show("设备编码不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(txtName.Text))
                    {
                        MessageBox.Show("设备名称不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    // 构造请求对象
                    var newDevice = new
                    {
                        EquipmentCode = txtCode.Text.Trim(),
                        EquipmentName = txtName.Text.Trim(),
                        EquipmentType = cmbType.SelectedIndex + 1, // 假设后端类型为1/2/3
                        Manufacturer = ((int)cmbManufacturer.SelectedValue).ToString(),
                        Status = 1 // 新增默认为启用
                    };
                    try
                    {
                        var url = $"{API_WRITE_BASE_URL}/AddEquipment";
                        var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(newDevice);
                        var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
                        var response = await httpClient.PostAsync(url, content);
                        if (response.IsSuccessStatusCode)
                        {
                            MessageBox.Show("新增设备成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            addForm.DialogResult = DialogResult.OK;
                        }
                        else
                        {
                            var errorContent = await response.Content.ReadAsStringAsync();
                            MessageBox.Show($"新增失败: {errorContent}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"网络异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                addForm.Controls.Add(lblType);
                addForm.Controls.Add(cmbType);
                addForm.Controls.Add(lblCodeStar);
                addForm.Controls.Add(lblCode);
                addForm.Controls.Add(txtCode);
                addForm.Controls.Add(lblNameStar);
                addForm.Controls.Add(lblName);
                addForm.Controls.Add(txtName);
                addForm.Controls.Add(lblManufacturer);
                addForm.Controls.Add(cmbManufacturer);
                addForm.Controls.Add(btnSave);
                addForm.Controls.Add(btnCancel);

                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadEquipmentData(); // 新增成功后刷新主表格
                }
            }
        }

        /// <summary>
        /// 控件加载事件
        /// </summary>
        private async void DeviceDictionary_Load_1(object sender, EventArgs e)
        {
            // 首次加载时测试API连接
            await TestApiConnection();
        }

        /// <summary>
        /// 测试API连接状态
        /// </summary>
        private async Task TestApiConnection()
        {
            try
            {
                var testUrl = $"{API_BASE_URL}/QueryEquipment";
                var response = await httpClient.GetAsync(testUrl);

                if (response.IsSuccessStatusCode)
                {
                    // API连接正常，直接加载数据
                    LoadEquipmentData();
                }
                else
                {
                    var errorMsg = await response.Content.ReadAsStringAsync();
                    MessageBox.Show($"API连接测试失败：\n状态码：{response.StatusCode}\n错误信息：{errorMsg}\n\n请检查：\n1. 后端API服务是否已启动\n2. API地址是否正确：{API_BASE_URL}\n3. 网络连接是否正常",
                        "API连接测试", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法连接到API服务：\n{ex.Message}\n\n请检查：\n1. 后端API服务是否已启动\n2. API地址是否正确：{API_BASE_URL}\n3. 网络连接是否正常\n4. 防火墙设置",
                    "API连接测试", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }




    }

    /// <summary>
    /// 生产厂家枚举
    /// </summary>
    public enum Manufacturer
    {
        /// <summary>
        /// 富士康
        /// </summary>
        [Description("富士康")]
        富士康 = 0,

        /// <summary>
        /// 博世
        /// </summary>
        [Description("博世")]
        博世 = 5,
        /// <summary>
        /// 宝洁
        /// </summary>
        [Description("宝洁")]
        宝洁 = 10,

        /// <summary>
        /// 奥林巴斯
        /// </summary>
        [Description("奥林巴斯")]
        奥林巴斯 = 99
    }

    /// <summary>
    /// 枚举帮助类，用于获取枚举的描述文本
    /// </summary>
    public static class EnumHelper
    {
        /// <summary>
        /// 获取枚举值的描述信息
        /// </summary>
        /// <param name="value">枚举值</param>
        /// <returns>描述文本</returns>
        public static string GetDescription(Enum value)
        {
            if (value == null)
            {
                return string.Empty;
            }

            FieldInfo fi = value.GetType().GetField(value.ToString());
            if (fi == null)
            {
                return value.ToString();
            }

            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }
    }
}

/// <summary>
/// API统一响应格式
/// </summary>
public class ApiResult<T>
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
}

/// <summary>
/// 分页响应格式 - 适配后端实际返回格式
/// </summary>
public class ApiPageResult<T>
{
    public string Msg { get; set; }
    public PageData<T> Data { get; set; }
    public int Code { get; set; }

    public bool Success => Code == 200;
}

/// <summary>
/// 分页数据包装
/// </summary>
public class PageData<T>
{
    [JsonProperty("pageData")]
    public List<T> Items { get; set; }

    [JsonProperty("totalCount")]
    public int TotalCount { get; set; }

    [JsonProperty("pageIndex")]
    public int PageIndex { get; set; }

    [JsonProperty("pageSize")]
    public int PageSize { get; set; }
}

/// <summary>
/// 设备API响应模型 - 与后端Equipment表结构对应
/// </summary>
public class EquipmentDto
{
    [JsonProperty("equipmentCode")]
    public string EquipmentCode { get; set; } = string.Empty;

    [JsonProperty("equipmentName")]
    public string EquipmentName { get; set; } = string.Empty;

    [JsonProperty("equipmentType")]
    public int EquipmentType { get; set; }

    [JsonProperty("equipmentTypeName")]
    public string EquipmentTypeName { get; set; } = string.Empty;

    [JsonProperty("manufacturerName")]
    public string ManufacturerName { get; set; } = string.Empty;

    [JsonProperty("manufacturer")]
    public int Manufacturer { get; set; }

    [JsonProperty("status")]
    public int Status { get; set; }

    [JsonProperty("statusName")]
    public string StatusName { get; set; } = string.Empty;

    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("createTime")]
    public DateTime? CreateTime { get; set; }

    [JsonProperty("createUserId")]
    public int? CreateUserId { get; set; }

    [JsonProperty("createUserName")]
    public string CreateUserName { get; set; } = string.Empty;

    [JsonProperty("updateTime")]
    public DateTime? UpdateTime { get; set; }

    [JsonProperty("updateUserId")]
    public int? UpdateUserId { get; set; }

    [JsonProperty("updateUserName")]
    public string UpdateUserName { get; set; } = string.Empty;

    [JsonProperty("isDeleted")]
    public bool IsDeleted { get; set; }

    [JsonProperty("remark")]
    public string Remark { get; set; } = string.Empty;
}

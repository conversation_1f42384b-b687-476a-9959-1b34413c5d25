using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Text;
using System.Net.Http;
using Newtonsoft.Json;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    // API响应数据模型
    public class ApiResponse<T>
    {
        public string msg { get; set; }
        public T data { get; set; }
        public int code { get; set; }
    }

    // 回收完成物品信息数据模型
    public class RecoveredItem
    {
        public int id { get; set; }
        public string itemName { get; set; }
        public string itemCode { get; set; }
        public string department { get; set; }
        public string recycleTime { get; set; }
        public string useDepartment { get; set; }
        public DateTime? recoveredTime { get; set; }

        // API返回的字段名
        public string recoveryTime { get; set; }  // 对应API返回的recoveryTime字段
    }

    // 清洗登记请求数据模型
    public class CleaningRegistrationRequest
    {
        public string cleaningMode { get; set; }
        public int cleaningEquipment { get; set; }
        public string cleaningBatch { get; set; }
        public string cleaner { get; set; }
        public string startTime { get; set; }
        public List<int> itemIds { get; set; }
    }

    public class CleaningRegistrationForm : Form
    {
        private ComboBoxEdit cmbCleaningMethod;
        private ComboBoxEdit cmbCleaningEquipment;
        private ComboBoxEdit cmbCleaningBatch;
        private TextEdit txtCleaner;
        private DateEdit dateStartTime;
        private GridControl gridControl;
        private GridView gridView;
        private SimpleButton btnConfirm;
        private SimpleButton btnCancel;
        private Label titleLabel;

        private DataTable itemsDataTable;
        private string ApiBaseUrl = MedicalDisinfectionSupplyCenter.ApiUrl.ApiReadUrl;
        private string ApiWriteUrl = MedicalDisinfectionSupplyCenter.ApiUrl.ApiWriteUrl;
        public CleaningRegistrationForm()
        {
            InitializeComponent();
            InitializeDataAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.BackColor = Color.White;

            // 创建标题栏
            CreateTitleBar();

            // 创建主内容区域
            var contentPanel = new Panel
            {
                Location = new Point(0, 50),
                Size = new Size(900, 550),
                BackColor = Color.White
            };
            this.Controls.Add(contentPanel);

            // 左侧表单区域
            var leftPanel = new Panel
            {
                Location = new Point(20, 20),
                Size = new Size(280, 480),
                BackColor = Color.White
            };
            contentPanel.Controls.Add(leftPanel);

            // 右侧表格区域
            var rightPanel = new Panel
            {
                Location = new Point(320, 20),
                Size = new Size(560, 480),
                BackColor = Color.White
            };
            contentPanel.Controls.Add(rightPanel);

            // 创建左侧表单控件
            CreateLeftPanelControls(leftPanel);

            // 创建右侧表格
            CreateRightPanelControls(rightPanel);


        }

        private void CreateTitleBar()
        {
            // 标题栏背景
            var titlePanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(900, 50),
                BackColor = Color.White
            };
            this.Controls.Add(titlePanel);

            // 标题文字
            titleLabel = new Label
            {
                Text = "清洗登记",
                Font = new Font("微软雅黑", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(51, 51, 51),
                Location = new Point(20, 15),
                AutoSize = true
            };
            titlePanel.Controls.Add(titleLabel);

            // 确定按钮
            btnConfirm = new SimpleButton
            {
                Text = "确定",
                Size = new Size(80, 30),
                Location = new Point(720, 10),
                BackColor = Color.FromArgb(24, 144, 255),
                ForeColor = Color.White
            };
            btnConfirm.Appearance.BackColor = Color.FromArgb(24, 144, 255);
            btnConfirm.Appearance.ForeColor = Color.White;
            btnConfirm.Appearance.BorderColor = Color.FromArgb(24, 144, 255);
            btnConfirm.Click += BtnConfirm_Click;
            titlePanel.Controls.Add(btnConfirm);

            // 取消按钮
            btnCancel = new SimpleButton
            {
                Text = "取消",
                Size = new Size(80, 30),
                Location = new Point(810, 10),
                BackColor = Color.FromArgb(245, 245, 245),
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            btnCancel.Appearance.BackColor = Color.FromArgb(245, 245, 245);
            btnCancel.Appearance.ForeColor = Color.FromArgb(102, 102, 102);
            btnCancel.Appearance.BorderColor = Color.FromArgb(217, 217, 217);
            btnCancel.Click += BtnCancel_Click;
            titlePanel.Controls.Add(btnCancel);
        }

        private void CreateLeftPanelControls(Panel leftPanel)
        {
            int yPos = 20;
            int labelWidth = 80;
            int controlWidth = 240;
            int rowHeight = 60;

            // 清洗方式
            var lblCleaningMethod = new Label
            {
                Text = "清洗方式",
                Location = new Point(0, yPos),
                Size = new Size(labelWidth, 20),
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            leftPanel.Controls.Add(lblCleaningMethod);

            cmbCleaningMethod = new ComboBoxEdit
            {
                Location = new Point(0, yPos + 25),
                Size = new Size(controlWidth, 30)
            };
            cmbCleaningMethod.Properties.Items.AddRange(new[] { "机洗", "手洗", "超声波清洗" });
            cmbCleaningMethod.SelectedIndex = 0;
            cmbCleaningMethod.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            leftPanel.Controls.Add(cmbCleaningMethod);

            yPos += rowHeight;

            // 清洗设备
            var lblCleaningEquipment = new Label
            {
                Text = "清洗设备",
                Location = new Point(0, yPos),
                Size = new Size(labelWidth, 20),
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            leftPanel.Controls.Add(lblCleaningEquipment);

            cmbCleaningEquipment = new ComboBoxEdit
            {
                Location = new Point(0, yPos + 25),
                Size = new Size(controlWidth, 30)
            };
            cmbCleaningEquipment.Properties.Items.AddRange(new[] { "设备1", "设备2", "设备3" });
            cmbCleaningEquipment.SelectedIndex = 0;
            cmbCleaningEquipment.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            leftPanel.Controls.Add(cmbCleaningEquipment);

            yPos += rowHeight;

            // 清洗批次
            var lblCleaningBatch = new Label
            {
                Text = "清洗批次",
                Location = new Point(0, yPos),
                Size = new Size(labelWidth, 20),
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            leftPanel.Controls.Add(lblCleaningBatch);

            cmbCleaningBatch = new ComboBoxEdit
            {
                Location = new Point(0, yPos + 25),
                Size = new Size(controlWidth, 30)
            };
            // 生成清洗批次选项
            GenerateCleaningBatchOptions();
            cmbCleaningBatch.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            leftPanel.Controls.Add(cmbCleaningBatch);

            yPos += rowHeight;

            // 清洗人
            var lblCleaner = new Label
            {
                Text = "清洗人",
                Location = new Point(0, yPos),
                Size = new Size(labelWidth, 20),
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            leftPanel.Controls.Add(lblCleaner);

            txtCleaner = new TextEdit
            {
                Location = new Point(0, yPos + 25),
                Size = new Size(controlWidth, 30),
                Text = "张三"
            };
            leftPanel.Controls.Add(txtCleaner);

            yPos += rowHeight;

            // 开始时间
            var lblStartTime = new Label
            {
                Text = "开始时间",
                Location = new Point(0, yPos),
                Size = new Size(labelWidth, 20),
                Font = new Font("微软雅黑", 9),
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            leftPanel.Controls.Add(lblStartTime);

            dateStartTime = new DateEdit
            {
                Location = new Point(0, yPos + 25),
                Size = new Size(controlWidth, 30)
            };
            dateStartTime.Properties.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            dateStartTime.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            dateStartTime.Properties.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            dateStartTime.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            dateStartTime.Properties.Mask.EditMask = "yyyy-MM-dd HH:mm:ss";
            dateStartTime.EditValue = DateTime.Parse("2022-07-22 10:10:12");
            leftPanel.Controls.Add(dateStartTime);
        }

        private void CreateRightPanelControls(Panel rightPanel)
        {
            // 创建GridControl
            gridControl = new GridControl
            {
                Location = new Point(0, 0),
                Size = new Size(560, 450),
                BackColor = Color.White
            };
            rightPanel.Controls.Add(gridControl);

            // 创建GridView
            gridView = new GridView(gridControl);
            gridControl.MainView = gridView;

            // 设置GridView属性
            InitializeGridView();
        }

        private void InitializeGridView()
        {
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ShowIndicator = false;
            gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            gridView.OptionsView.EnableAppearanceEvenRow = true;
            gridView.OptionsView.EnableAppearanceOddRow = true;
            gridView.OptionsView.ShowColumnHeaders = true;

            // 设置选择模式
            gridView.OptionsSelection.MultiSelect = true;
            gridView.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            gridView.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.True;

            // 创建数据表
            itemsDataTable = new DataTable();
            itemsDataTable.Columns.Add("ItemId", typeof(int));        // 隐藏列，存储物品ID
            itemsDataTable.Columns.Add("ItemName", typeof(string));
            itemsDataTable.Columns.Add("ItemCode", typeof(string));
            itemsDataTable.Columns.Add("Department", typeof(string));
            itemsDataTable.Columns.Add("RecycleTime", typeof(string));

            // 设置列
            gridView.Columns.Clear();
            var colItemId = gridView.Columns.AddField("ItemId");      // 隐藏列
            colItemId.Visible = false;                                // 设置为不可见
            var colItemName = gridView.Columns.AddVisible("ItemName", "物品名称");
            var colItemCode = gridView.Columns.AddVisible("ItemCode", "物品条码");
            var colDepartment = gridView.Columns.AddVisible("Department", "使用科室");
            var colRecycleTime = gridView.Columns.AddVisible("RecycleTime", "回收时间");

            // 设置列宽
            colItemName.Width = 140;
            colItemCode.Width = 100;
            colDepartment.Width = 100;
            colRecycleTime.Width = 170;

            // 设置列对齐
            colItemName.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colItemCode.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colDepartment.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colRecycleTime.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colItemName.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colItemCode.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colDepartment.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colRecycleTime.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            // 设置样式
            SetGridViewAppearance();

            // 绑定数据
            gridControl.DataSource = itemsDataTable;
        }

        private void SetGridViewAppearance()
        {
            // 设置表头样式
            gridView.Appearance.HeaderPanel.BackColor = Color.FromArgb(250, 250, 250);
            gridView.Appearance.HeaderPanel.ForeColor = Color.FromArgb(102, 102, 102);
            gridView.Appearance.HeaderPanel.Font = new Font("微软雅黑", 9, FontStyle.Regular);
            gridView.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            gridView.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 设置奇偶行样式
            gridView.Appearance.EvenRow.BackColor = Color.White;
            gridView.Appearance.OddRow.BackColor = Color.White;

            // 设置选中行样式
            gridView.Appearance.FocusedRow.BackColor = Color.FromArgb(230, 247, 255);
            gridView.Appearance.FocusedRow.ForeColor = Color.Black;

            // 设置普通行样式
            gridView.Appearance.Row.Font = new Font("微软雅黑", 9);
            gridView.Appearance.Row.ForeColor = Color.FromArgb(51, 51, 51);
            gridView.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 设置行高
            gridView.RowHeight = 40;

            // 设置边框
            gridView.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            gridControl.LookAndFeel.UseDefaultLookAndFeel = false;
            gridControl.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003;
        }



        private void GenerateCleaningBatchOptions()
        {
            // 生成清洗批次选项，格式：QX + 年月日 + 序号
            string today = DateTime.Now.ToString("yyyyMMdd");
            for (int i = 1; i <= 10; i++)
            {
                string batchNumber = $"QX{today}{i:D2}";
                cmbCleaningBatch.Properties.Items.Add(batchNumber);
            }
            if (cmbCleaningBatch.Properties.Items.Count > 0)
            {
                cmbCleaningBatch.SelectedIndex = 0;
            }
        }

        private async void InitializeDataAsync()
        {
            // 从API加载数据
            await LoadDataFromApiAsync();
        }

        /// <summary>
        /// 从API异步加载回收完成的物品数据
        /// </summary>
        private async Task LoadDataFromApiAsync()
        {
            try
            {
                // 显示加载提示
                if (itemsDataTable != null)
                {
                    itemsDataTable.Clear();
                    itemsDataTable.Rows.Add(0, "正在加载数据...", "", "", "");
                }

                // 构建API URL
                
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetRecoveredFinsh";
                System.Diagnostics.Debug.WriteLine($"🚀 开始调用API: {apiUrl}");

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false, null);
                System.Diagnostics.Debug.WriteLine($"📥 API返回数据长度: {jsonResult?.Length ?? 0}");

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 添加调试输出，查看原始JSON数据
                    System.Diagnostics.Debug.WriteLine($"📋 API原始返回数据: {jsonResult}");

                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<RecoveredItem>>>(jsonResult);

                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 清空现有数据
                        itemsDataTable.Clear();

                        // 添加调试输出，查看解析后的数据
                        foreach (var item in apiResponse.data)
                        {
                            System.Diagnostics.Debug.WriteLine($"🔍 物品数据: ID={item.id}, Name={item.itemName}, recoveryTime={item.recoveryTime}, recoveredTime={item.recoveredTime}, recycleTime={item.recycleTime}");
                        }

                        // 填充数据到表格
                        FillGridWithApiData(apiResponse.data);

                        System.Diagnostics.Debug.WriteLine($"✅ 成功加载 {apiResponse.data.Count} 条数据");
                    }
                    else
                    {
                        // API返回错误
                        itemsDataTable.Clear();
                        MessageBox.Show($"API返回错误: {apiResponse?.msg ?? "未知错误"}",
                            "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        // 加载示例数据作为备用
                        LoadSampleData();
                    }
                }
                else
                {
                    // API调用失败
                    itemsDataTable.Clear();
                    MessageBox.Show("API调用失败，未返回数据。将显示示例数据。", "警告",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // 加载示例数据作为备用
                    LoadSampleData();
                }
            }
            catch (Exception ex)
            {
                // 异常处理
                System.Diagnostics.Debug.WriteLine($"❌ API调用异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                itemsDataTable.Clear();
                MessageBox.Show($"加载数据时发生错误: {ex.Message}\n将显示示例数据。", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 加载示例数据作为备用
                LoadSampleData();
            }
        }

        /// <summary>
        /// 将API数据填充到表格中
        /// </summary>
        /// <param name="items">API返回的物品列表</param>
        private void FillGridWithApiData(List<RecoveredItem> items)
        {
            foreach (var item in items)
            {
                // 格式化回收时间 - 优先使用API返回的recoveryTime字段
                string recycleTimeStr = "";

                // 尝试解析API返回的recoveryTime字段
                if (!string.IsNullOrEmpty(item.recoveryTime))
                {
                    if (DateTime.TryParse(item.recoveryTime, out DateTime parsedTime))
                    {
                        recycleTimeStr = parsedTime.ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    else
                    {
                        recycleTimeStr = item.recoveryTime; // 如果解析失败，直接使用原始字符串
                    }
                }
                else
                {
                    // 备用方案：使用其他时间字段
                    recycleTimeStr = item.recoveredTime?.ToString("yyyy-MM-dd HH:mm:ss") ??
                                   item.recycleTime ?? "";
                }

                // 添加数据行
                itemsDataTable.Rows.Add(
                    item.id,                                 // ItemId (隐藏列)
                    item.itemName ?? "",                     // ItemName
                    item.itemCode ?? "",                     // ItemCode
                    item.useDepartment ?? item.department ?? "", // Department
                    recycleTimeStr                           // RecycleTime
                );
            }
        }

        /// <summary>
        /// 加载示例数据（作为API失败时的备用方案）
        /// </summary>
        private void LoadSampleData()
        {
            // 添加示例数据
            itemsDataTable.Rows.Add(1, "骨科手术包", "1231", "内科", "2022-07-22 10:10:12");
            itemsDataTable.Rows.Add(2, "骨科小手术包", "1232", "内科", "2022-07-22 10:10:12");
            itemsDataTable.Rows.Add(3, "普通手术器械", "1233", "外科", "2022-07-22 10:15:30");
            itemsDataTable.Rows.Add(4, "心脏手术包", "1234", "心外科", "2022-07-22 10:20:45");
        }

        /// <summary>
        /// 刷新数据 - 重新从API加载数据
        /// </summary>
        public async Task RefreshDataAsync()
        {
            await LoadDataFromApiAsync();
        }

        private async void BtnConfirm_Click(object sender, EventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(txtCleaner.Text))
            {
                MessageBox.Show("请输入清洗人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 获取选中的物品
            var selectedItems = GetSelectedItems();
            if (selectedItems.Count == 0)
            {
                MessageBox.Show("请至少选择一个物品进行清洗", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 提交清洗登记
            if (await SubmitCleaningRegistrationAsync(selectedItems))
            {
                MessageBox.Show("清洗登记成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private List<DataRow> GetSelectedItems()
        {
            var selectedItems = new List<DataRow>();
            for (int i = 0; i < gridView.DataRowCount; i++)
            {
                if (gridView.IsRowSelected(i))
                {
                    var dataRow = gridView.GetDataRow(i);
                    selectedItems.Add(dataRow);
                }
            }
            return selectedItems;
        }

        /// <summary>
        /// 异步提交清洗登记到API
        /// </summary>
        /// <param name="selectedItems">选中的物品列表</param>
        /// <returns>提交是否成功</returns>
        private async Task<bool> SubmitCleaningRegistrationAsync(List<DataRow> selectedItems)
        {
            try
            {
                // 获取选中物品的ID列表
                var itemIds = selectedItems.Select(item => Convert.ToInt32(item["ItemId"])).ToList();

                // 获取清洗设备ID（假设下拉框的SelectedIndex对应设备ID，从1开始）
                int cleaningEquipmentId = cmbCleaningEquipment.SelectedIndex + 1;

                // 构建API请求数据
                var requestData = new CleaningRegistrationRequest
                {
                    cleaningMode = cmbCleaningMethod.Text,
                    cleaningEquipment = cleaningEquipmentId,
                    cleaningBatch = cmbCleaningBatch.Text,
                    cleaner = txtCleaner.Text,
                    startTime = dateStartTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    itemIds = itemIds
                };

                // 序列化为JSON
                string jsonData = JsonConvert.SerializeObject(requestData);
                System.Diagnostics.Debug.WriteLine($"🚀 提交清洗登记数据: {jsonData}");

                // 构建API URL
                string apiUrl = $"{ApiWriteUrl}/api/RecyclingCleaning/AddCleaningRegistration";
                System.Diagnostics.Debug.WriteLine($"📡 API请求URL: {apiUrl}");

                // 创建HttpContent
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                // 调用API
                string result = await HttpClientHelper.ClientAsync("POST", apiUrl, false, content);
                System.Diagnostics.Debug.WriteLine($"📥 API返回结果: {result}");

                if (!string.IsNullOrEmpty(result))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<object>(result);

                    if (apiResponse != null)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ 清洗登记提交成功");
                        return true;
                    }
                    else
                    {
                       
                      
                        return false;
                    }
                }
                else
                {
                    MessageBox.Show("API调用失败，未返回数据", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 提交清洗登记异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"提交失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
    }
}

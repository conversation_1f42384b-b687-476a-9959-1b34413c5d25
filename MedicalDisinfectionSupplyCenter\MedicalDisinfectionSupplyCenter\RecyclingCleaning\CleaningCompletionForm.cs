using System;
using System.Drawing;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public partial class CleaningCompletionForm : Form
    {
        public string FinishPerson { get; private set; }
        public DateTime FinishTime { get; private set; }
        public bool CleaningResult { get; private set; }

        private TextBox txtFinishPerson;
        private DateTimePicker dtpFinishTime;
        private RadioButton rbSuccess;
        private RadioButton rbFailed;
        private Button btnConfirm;
        private Label lblFinishPerson;
        private Label lblFinishTime;
        private Label lblResult;
        private Button btnCancel;

        public CleaningCompletionForm(string cleaningBatch)
        {
            InitializeComponent();
            this.Text = $"完成清洗 - 批次: {cleaningBatch}";
            
            // 设置默认值
            dtpFinishTime.Value = DateTime.Now;
            rbSuccess.Checked = true;
        }

        private void InitializeComponent()
        {
            this.txtFinishPerson = new System.Windows.Forms.TextBox();
            this.dtpFinishTime = new System.Windows.Forms.DateTimePicker();
            this.rbSuccess = new System.Windows.Forms.RadioButton();
            this.rbFailed = new System.Windows.Forms.RadioButton();
            this.btnConfirm = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.lblFinishPerson = new System.Windows.Forms.Label();
            this.lblFinishTime = new System.Windows.Forms.Label();
            this.lblResult = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // txtFinishPerson
            // 
            this.txtFinishPerson.Location = new System.Drawing.Point(110, 27);
            this.txtFinishPerson.Name = "txtFinishPerson";
            this.txtFinishPerson.Size = new System.Drawing.Size(200, 28);
            this.txtFinishPerson.TabIndex = 1;
            this.txtFinishPerson.Text = "luchangqing";
            // 
            // dtpFinishTime
            // 
            this.dtpFinishTime.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            this.dtpFinishTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpFinishTime.Location = new System.Drawing.Point(110, 67);
            this.dtpFinishTime.Name = "dtpFinishTime";
            this.dtpFinishTime.ShowUpDown = true;
            this.dtpFinishTime.Size = new System.Drawing.Size(200, 28);
            this.dtpFinishTime.TabIndex = 3;
            // 
            // rbSuccess
            // 
            this.rbSuccess.Checked = true;
            this.rbSuccess.Location = new System.Drawing.Point(110, 110);
            this.rbSuccess.Name = "rbSuccess";
            this.rbSuccess.Size = new System.Drawing.Size(80, 23);
            this.rbSuccess.TabIndex = 5;
            this.rbSuccess.TabStop = true;
            this.rbSuccess.Text = "成功";
            // 
            // rbFailed
            // 
            this.rbFailed.Location = new System.Drawing.Point(200, 110);
            this.rbFailed.Name = "rbFailed";
            this.rbFailed.Size = new System.Drawing.Size(80, 23);
            this.rbFailed.TabIndex = 6;
            this.rbFailed.Text = "失败";
            // 
            // btnConfirm
            // 
            this.btnConfirm.Location = new System.Drawing.Point(150, 180);
            this.btnConfirm.Name = "btnConfirm";
            this.btnConfirm.Size = new System.Drawing.Size(75, 30);
            this.btnConfirm.TabIndex = 7;
            this.btnConfirm.Text = "确认";
            this.btnConfirm.UseVisualStyleBackColor = true;
            this.btnConfirm.Click += new System.EventHandler(this.btnConfirm_Click_1);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(235, 180);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 30);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            // 
            // lblFinishPerson
            // 
            this.lblFinishPerson.Location = new System.Drawing.Point(20, 30);
            this.lblFinishPerson.Name = "lblFinishPerson";
            this.lblFinishPerson.Size = new System.Drawing.Size(80, 23);
            this.lblFinishPerson.TabIndex = 0;
            this.lblFinishPerson.Text = "完成人:";
            // 
            // lblFinishTime
            // 
            this.lblFinishTime.Location = new System.Drawing.Point(20, 70);
            this.lblFinishTime.Name = "lblFinishTime";
            this.lblFinishTime.Size = new System.Drawing.Size(80, 23);
            this.lblFinishTime.TabIndex = 2;
            this.lblFinishTime.Text = "完成时间:";
            // 
            // lblResult
            // 
            this.lblResult.Location = new System.Drawing.Point(20, 110);
            this.lblResult.Name = "lblResult";
            this.lblResult.Size = new System.Drawing.Size(80, 23);
            this.lblResult.TabIndex = 4;
            this.lblResult.Text = "清洗结果:";
            // 
            // CleaningCompletionForm
            // 
            this.ClientSize = new System.Drawing.Size(442, 224);
            this.Controls.Add(this.lblFinishPerson);
            this.Controls.Add(this.txtFinishPerson);
            this.Controls.Add(this.lblFinishTime);
            this.Controls.Add(this.dtpFinishTime);
            this.Controls.Add(this.lblResult);
            this.Controls.Add(this.rbSuccess);
            this.Controls.Add(this.rbFailed);
            this.Controls.Add(this.btnConfirm);
            this.Controls.Add(this.btnCancel);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CleaningCompletionForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private void BtnConfirm_Click(object sender, EventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(txtFinishPerson.Text))
            {
                MessageBox.Show("请输入完成人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFinishPerson.Focus();
                return;
            }

            // 设置返回值
            FinishPerson = txtFinishPerson.Text.Trim();
            FinishTime = dtpFinishTime.Value;
            CleaningResult = rbSuccess.Checked;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnConfirm_Click_1(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("🔘 点击了完成清洗确认按钮");

            // 验证输入
            if (string.IsNullOrWhiteSpace(txtFinishPerson.Text))
            {
                MessageBox.Show("请输入完成人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFinishPerson.Focus();
                return;
            }

            // 设置返回值
            FinishPerson = txtFinishPerson.Text.Trim();
            FinishTime = dtpFinishTime.Value;
            CleaningResult = rbSuccess.Checked;

            System.Diagnostics.Debug.WriteLine($"📝 完成信息: 完成人={FinishPerson}, 完成时间={FinishTime:yyyy-MM-dd}, 清洗结果={CleaningResult}");

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.SystemManagement
{
    public partial class PermissionManagementControl : UserControl
    {
        public PermissionManagementControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // PermissionManagementControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "PermissionManagementControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.PermissionManagementControl_Load);
            this.ResumeLayout(false);
        }

        private void PermissionManagementControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "权限管理";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Main layout container
            TableLayoutPanel mainLayout = new TableLayoutPanel();
            mainLayout.Dock = DockStyle.Fill;
            mainLayout.ColumnCount = 1;
            mainLayout.RowCount = 2;
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.Controls.Add(mainLayout);

            // Toolbar panel
            Panel toolbarPanel = new Panel();
            toolbarPanel.Dock = DockStyle.Fill;
            toolbarPanel.Height = 40;

            // Add button
            Button addButton = new Button();
            addButton.Text = "添加权限";
            addButton.Width = 100;
            addButton.Height = 30;
            addButton.Location = new Point(10, 5);
            addButton.BackColor = Color.FromArgb(0, 120, 215);
            addButton.ForeColor = Color.White;
            addButton.Click += (s, ev) => ShowAddPermissionDialog();
            toolbarPanel.Controls.Add(addButton);

            // Refresh button
            Button refreshButton = new Button();
            refreshButton.Text = "刷新";
            refreshButton.Width = 80;
            refreshButton.Height = 30;
            refreshButton.Location = new Point(120, 5);
            refreshButton.BackColor = Color.FromArgb(46, 204, 113);
            refreshButton.ForeColor = Color.White;
            toolbarPanel.Controls.Add(refreshButton);

            mainLayout.Controls.Add(toolbarPanel, 0, 0);

            // Create TreeView for permission management
            TreeView permissionTree = new TreeView();
            permissionTree.Dock = DockStyle.Fill;
            permissionTree.Font = new Font("微软雅黑", 10);
            permissionTree.ShowLines = true;
            permissionTree.FullRowSelect = true;
            permissionTree.HideSelection = false;
            permissionTree.ShowPlusMinus = true;

            // Create context menu for tree nodes
            ContextMenuStrip contextMenu = new ContextMenuStrip();
            ToolStripMenuItem addItem = new ToolStripMenuItem("添加子权限");
            addItem.Click += (s, ev) => {
                if (permissionTree.SelectedNode != null)
                {
                    ShowAddPermissionDialog(permissionTree.SelectedNode);
                }
            };
            contextMenu.Items.Add(addItem);

            ToolStripMenuItem editItem = new ToolStripMenuItem("编辑");
            editItem.Click += (s, ev) => {
                if (permissionTree.SelectedNode != null)
                {
                    MessageBox.Show($"编辑权限: {permissionTree.SelectedNode.Text}", "编辑", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            };
            contextMenu.Items.Add(editItem);

            ToolStripMenuItem deleteItem = new ToolStripMenuItem("删除");
            deleteItem.Click += (s, ev) => {
                if (permissionTree.SelectedNode != null)
                {
                    if (MessageBox.Show($"确定要删除 '{permissionTree.SelectedNode.Text}' 权限吗？", "确认删除", 
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        permissionTree.SelectedNode.Remove();
                    }
                }
            };
            contextMenu.Items.Add(deleteItem);

            permissionTree.ContextMenuStrip = contextMenu;

            // Build the permission tree
            BuildPermissionTree(permissionTree);

            mainLayout.Controls.Add(permissionTree, 0, 1);

            // Add node click event
            permissionTree.AfterSelect += (s, ev) => {
                if (ev.Node != null)
                {
                    // In a real app, you would load details for the selected permission
                    System.Diagnostics.Debug.WriteLine($"Selected permission: {ev.Node.Text}");
                }
            };
        }

        private void BuildPermissionTree(TreeView treeView)
        {
            // System management
            TreeNode systemNode = treeView.Nodes.Add("系统管理", "系统管理");
            systemNode.Nodes.Add("user_management", "用户管理");
            systemNode.Nodes.Add("role_management", "角色管理");
            systemNode.Nodes.Add("permission_management", "权限管理");
            
            // Device management
            TreeNode deviceNode = treeView.Nodes.Add("device_management", "设备管理");
            TreeNode deviceInNode = deviceNode.Nodes.Add("device_inbound", "设备入库");
            deviceInNode.Nodes.Add("device_inbound_add", "新增入库");
            deviceInNode.Nodes.Add("device_inbound_edit", "编辑入库单");
            deviceInNode.Nodes.Add("device_inbound_delete", "删除入库单");
            deviceInNode.Nodes.Add("device_inbound_approve", "审批入库单");
            
            TreeNode deviceOutNode = deviceNode.Nodes.Add("device_outbound", "设备出库");
            deviceOutNode.Nodes.Add("device_outbound_add", "新增出库");
            deviceOutNode.Nodes.Add("device_outbound_edit", "编辑出库单");
            deviceOutNode.Nodes.Add("device_outbound_delete", "删除出库单");
            deviceOutNode.Nodes.Add("device_outbound_approve", "审批出库单");
            
            deviceNode.Nodes.Add("device_dictionary", "设备字典");
            
            // Basic data management
            TreeNode basicNode = treeView.Nodes.Add("basic_management", "基础数据");
            basicNode.Nodes.Add("supplier_management", "供应商管理");
            basicNode.Nodes.Add("department_management", "科室管理");
            
            // Report management
            TreeNode reportNode = treeView.Nodes.Add("report_management", "报表管理");
            reportNode.Nodes.Add("inventory_report", "库存报表");
            reportNode.Nodes.Add("inbound_report", "入库报表");
            reportNode.Nodes.Add("outbound_report", "出库报表");
            
            // Expand all nodes
            treeView.ExpandAll();
        }

        private void ShowAddPermissionDialog(TreeNode parentNode = null)
        {
            // Create a form for adding a new permission
            Form addPermissionForm = new Form();
            addPermissionForm.Text = parentNode == null ? "添加权限" : $"添加子权限 - 父级: {parentNode.Text}";
            addPermissionForm.Width = 400;
            addPermissionForm.Height = 300;
            addPermissionForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            addPermissionForm.StartPosition = FormStartPosition.CenterParent;
            addPermissionForm.MaximizeBox = false;
            addPermissionForm.MinimizeBox = false;

            // Permission ID
            Label idLabel = new Label();
            idLabel.Text = "权限编码:";
            idLabel.Location = new Point(30, 30);
            addPermissionForm.Controls.Add(idLabel);

            TextBox idTextBox = new TextBox();
            idTextBox.Location = new Point(120, 30);
            idTextBox.Width = 230;
            addPermissionForm.Controls.Add(idTextBox);

            // Permission name
            Label nameLabel = new Label();
            nameLabel.Text = "权限名称:";
            nameLabel.Location = new Point(30, 70);
            addPermissionForm.Controls.Add(nameLabel);

            TextBox nameTextBox = new TextBox();
            nameTextBox.Location = new Point(120, 70);
            nameTextBox.Width = 230;
            addPermissionForm.Controls.Add(nameTextBox);

            // Permission URL
            Label urlLabel = new Label();
            urlLabel.Text = "权限URL:";
            urlLabel.Location = new Point(30, 110);
            addPermissionForm.Controls.Add(urlLabel);

            TextBox urlTextBox = new TextBox();
            urlTextBox.Location = new Point(120, 110);
            urlTextBox.Width = 230;
            addPermissionForm.Controls.Add(urlTextBox);

            // Permission type
            Label typeLabel = new Label();
            typeLabel.Text = "权限类型:";
            typeLabel.Location = new Point(30, 150);
            addPermissionForm.Controls.Add(typeLabel);

            ComboBox typeComboBox = new ComboBox();
            typeComboBox.Location = new Point(120, 150);
            typeComboBox.Width = 230;
            typeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            typeComboBox.Items.AddRange(new object[] { "菜单", "按钮", "接口" });
            typeComboBox.SelectedIndex = 0;
            addPermissionForm.Controls.Add(typeComboBox);

            // Save button
            Button saveButton = new Button();
            saveButton.Text = "保存";
            saveButton.Location = new Point(120, 200);
            saveButton.Width = 80;
            saveButton.BackColor = Color.FromArgb(0, 120, 215);
            saveButton.ForeColor = Color.White;
            saveButton.Click += (s, e) => {
                if (string.IsNullOrEmpty(idTextBox.Text) || string.IsNullOrEmpty(nameTextBox.Text))
                {
                    MessageBox.Show("请填写权限编码和名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // In a real app, you would add the permission to the database
                MessageBox.Show($"已添加权限: {nameTextBox.Text}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // Add the new node to the tree
                TreeNode newNode = new TreeNode(nameTextBox.Text);
                newNode.Name = idTextBox.Text;
                
                if (parentNode != null)
                {
                    parentNode.Nodes.Add(newNode);
                    parentNode.Expand();
                }
                else
                {
                    // Find the TreeView and add the node to the root
                    TreeView treeView = null;
                    foreach (Control control in this.Controls)
                    {
                        if (control is TableLayoutPanel panel)
                        {
                            foreach (Control panelControl in panel.Controls)
                            {
                                if (panelControl is TreeView tv)
                                {
                                    treeView = tv;
                                    break;
                                }
                            }
                        }
                    }
                    
                    if (treeView != null)
                    {
                        treeView.Nodes.Add(newNode);
                    }
                }
                
                addPermissionForm.Close();
            };
            addPermissionForm.Controls.Add(saveButton);

            // Cancel button
            Button cancelButton = new Button();
            cancelButton.Text = "取消";
            cancelButton.Location = new Point(220, 200);
            cancelButton.Width = 80;
            cancelButton.Click += (s, e) => addPermissionForm.Close();
            addPermissionForm.Controls.Add(cancelButton);

            // Show the form
            addPermissionForm.ShowDialog();
        }
    }
} 
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.UserManagement
{
    public partial class UserGroupControl : UserControl
    {
        public UserGroupControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // UserGroupControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "UserGroupControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.UserGroupControl_Load);
            this.ResumeLayout(false);
        }

        private void UserGroupControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "用户组管理";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Create split container to divide the form
            SplitContainer splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Vertical;
            splitContainer.SplitterDistance = 300;
            splitContainer.Panel1MinSize = 200;
            splitContainer.Panel2MinSize = 300;
            this.Controls.Add(splitContainer);

            // === Left Panel (Group List) ===
            Panel leftPanel = new Panel();
            leftPanel.Dock = DockStyle.Fill;
            splitContainer.Panel1.Controls.Add(leftPanel);

            // Group list toolbar
            Panel groupToolbar = new Panel();
            groupToolbar.Dock = DockStyle.Top;
            groupToolbar.Height = 40;
            leftPanel.Controls.Add(groupToolbar);

            // Add group button
            Button addGroupButton = new Button();
            addGroupButton.Text = "添加用户组";
            addGroupButton.Width = 100;
            addGroupButton.Height = 30;
            addGroupButton.Location = new Point(10, 5);
            addGroupButton.BackColor = Color.FromArgb(46, 204, 113);
            addGroupButton.ForeColor = Color.White;
            addGroupButton.Click += (s, ev) => ShowAddGroupDialog();
            groupToolbar.Controls.Add(addGroupButton);

            // Group ListBox
            ListBox groupListBox = new ListBox();
            groupListBox.Dock = DockStyle.Fill;
            groupListBox.Font = new Font("微软雅黑", 10);
            groupListBox.BorderStyle = BorderStyle.FixedSingle;
            
            // Add sample groups
            groupListBox.Items.AddRange(new object[] {
                "管理员组",
                "医生组",
                "护士组",
                "操作员组",
                "质控组",
                "信息科组"
            });
            
            groupListBox.SelectedIndexChanged += (s, ev) => {
                if (groupListBox.SelectedItem != null)
                {
                    UpdateGroupDetails(groupListBox.SelectedItem.ToString());
                }
            };
            leftPanel.Controls.Add(groupListBox);

            // === Right Panel (Group Details and Member List) ===
            Panel rightPanel = new Panel();
            rightPanel.Dock = DockStyle.Fill;
            splitContainer.Panel2.Controls.Add(rightPanel);

            // Group details panel
            Panel detailsPanel = new Panel();
            detailsPanel.Dock = DockStyle.Top;
            detailsPanel.Height = 120;
            detailsPanel.BorderStyle = BorderStyle.FixedSingle;
            detailsPanel.Padding = new Padding(10);
            rightPanel.Controls.Add(detailsPanel);

            // Group name
            Label groupNameLabel = new Label();
            groupNameLabel.Text = "组名称:";
            groupNameLabel.Location = new Point(20, 20);
            groupNameLabel.AutoSize = true;
            detailsPanel.Controls.Add(groupNameLabel);

            Label groupNameValue = new Label();
            groupNameValue.Name = "groupNameValue";
            groupNameValue.Text = "未选择";
            groupNameValue.Location = new Point(120, 20);
            groupNameValue.AutoSize = true;
            groupNameValue.Font = new Font(groupNameValue.Font, FontStyle.Bold);
            detailsPanel.Controls.Add(groupNameValue);

            // Group description
            Label descLabel = new Label();
            descLabel.Text = "描述:";
            descLabel.Location = new Point(20, 50);
            descLabel.AutoSize = true;
            detailsPanel.Controls.Add(descLabel);

            Label descValue = new Label();
            descValue.Name = "descValue";
            descValue.Text = "请选择一个用户组查看详情";
            descValue.Location = new Point(120, 50);
            descValue.Width = 400;
            descValue.Height = 40;
            detailsPanel.Controls.Add(descValue);

            // Group created time
            Label createdLabel = new Label();
            createdLabel.Text = "创建时间:";
            createdLabel.Location = new Point(20, 80);
            createdLabel.AutoSize = true;
            detailsPanel.Controls.Add(createdLabel);

            Label createdValue = new Label();
            createdValue.Name = "createdValue";
            createdValue.Text = "";
            createdValue.Location = new Point(120, 80);
            createdValue.AutoSize = true;
            detailsPanel.Controls.Add(createdValue);

            // Members toolbar
            Panel membersToolbar = new Panel();
            membersToolbar.Dock = DockStyle.Top;
            membersToolbar.Height = 40;
            rightPanel.Controls.Add(membersToolbar);

            // Group members label
            Label membersLabel = new Label();
            membersLabel.Text = "组成员";
            membersLabel.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            membersLabel.Location = new Point(10, 10);
            membersLabel.AutoSize = true;
            membersToolbar.Controls.Add(membersLabel);

            // Add member button
            Button addMemberButton = new Button();
            addMemberButton.Text = "添加成员";
            addMemberButton.Width = 90;
            addMemberButton.Height = 30;
            addMemberButton.Location = new Point(410, 5);
            addMemberButton.BackColor = Color.FromArgb(0, 120, 215);
            addMemberButton.ForeColor = Color.White;
            addMemberButton.Click += (s, ev) => {
                if (groupListBox.SelectedItem == null)
                {
                    MessageBox.Show("请先选择一个用户组", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                MessageBox.Show($"添加成员到组: {groupListBox.SelectedItem}", "添加成员", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            };
            membersToolbar.Controls.Add(addMemberButton);

            // Members DataGridView
            DataGridView membersGrid = new DataGridView();
            membersGrid.Dock = DockStyle.Fill;
            membersGrid.BackgroundColor = Color.White;
            membersGrid.BorderStyle = BorderStyle.None;
            membersGrid.AllowUserToAddRows = false;
            membersGrid.AllowUserToDeleteRows = false;
            membersGrid.ReadOnly = true;
            membersGrid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            membersGrid.RowHeadersVisible = false;
            membersGrid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            membersGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            membersGrid.Name = "membersGrid";
            rightPanel.Controls.Add(membersGrid);

            // Add columns
            membersGrid.Columns.Add("Id", "ID");
            membersGrid.Columns.Add("Username", "用户名");
            membersGrid.Columns.Add("RealName", "姓名");
            membersGrid.Columns.Add("Role", "角色");
            membersGrid.Columns.Add("Department", "所属部门");
            membersGrid.Columns.Add("JoinTime", "加入时间");

            // Add remove column
            DataGridViewButtonColumn removeColumn = new DataGridViewButtonColumn();
            removeColumn.HeaderText = "操作";
            removeColumn.Text = "移除";
            removeColumn.UseColumnTextForButtonValue = true;
            removeColumn.Width = 60;
            membersGrid.Columns.Add(removeColumn);

            // Add cell click event for remove button
            membersGrid.CellClick += (s, ev) => {
                if (ev.RowIndex >= 0 && ev.ColumnIndex == removeColumn.Index)
                {
                    string username = membersGrid.Rows[ev.RowIndex].Cells["Username"].Value.ToString();
                    string groupName = groupListBox.SelectedItem?.ToString() ?? "未知组";
                    
                    if (MessageBox.Show($"确定要将用户 '{username}' 从组 '{groupName}' 中移除吗？", 
                        "确认移除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        membersGrid.Rows.RemoveAt(ev.RowIndex);
                        MessageBox.Show($"已从组 '{groupName}' 中移除用户 '{username}'", 
                            "移除成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            };

            // Select the first group by default
            if (groupListBox.Items.Count > 0)
            {
                groupListBox.SelectedIndex = 0;
            }
        }

        private void UpdateGroupDetails(string groupName)
        {
            // Update the group details panel
            foreach (Control control in this.Controls)
            {
                if (control is SplitContainer splitContainer)
                {
                    Panel rightPanel = splitContainer.Panel2.Controls[0] as Panel;
                    Panel detailsPanel = rightPanel.Controls[0] as Panel;
                    
                    // Update group name
                    Label nameLabel = detailsPanel.Controls["groupNameValue"] as Label;
                    if (nameLabel != null)
                    {
                        nameLabel.Text = groupName;
                    }
                    
                    // Update description
                    Label descLabel = detailsPanel.Controls["descValue"] as Label;
                    if (descLabel != null)
                    {
                        string description = "这是一个示例描述。";
                        switch (groupName)
                        {
                            case "管理员组":
                                description = "系统管理员组，拥有系统的全部权限。";
                                break;
                            case "医生组":
                                description = "医生用户组，可以查看和使用医疗设备。";
                                break;
                            case "护士组":
                                description = "护士用户组，可以查看和使用部分医疗设备。";
                                break;
                            case "操作员组":
                                description = "设备操作员组，负责设备的入库和出库操作。";
                                break;
                            case "质控组":
                                description = "质量控制组，负责监督设备质量和消毒流程。";
                                break;
                            case "信息科组":
                                description = "信息科技术组，负责系统维护和数据管理。";
                                break;
                        }
                        descLabel.Text = description;
                    }
                    
                    // Update created time
                    Label createdLabel = detailsPanel.Controls["createdValue"] as Label;
                    if (createdLabel != null)
                    {
                        // Generate a random date in 2023 for demonstration
                        Random random = new Random(groupName.GetHashCode());
                        int month = random.Next(1, 13);
                        int day = random.Next(1, 29);
                        createdLabel.Text = $"2023-{month:D2}-{day:D2}";
                    }
                    
                    // Update the members grid
                    DataGridView membersGrid = null;
                    foreach (Control panelControl in rightPanel.Controls)
                    {
                        if (panelControl is DataGridView grid && grid.Name == "membersGrid")
                        {
                            membersGrid = grid;
                            break;
                        }
                    }
                    
                    if (membersGrid != null)
                    {
                        // Clear existing rows
                        membersGrid.Rows.Clear();
                        
                        // Add sample data based on the group
                        switch (groupName)
                        {
                            case "管理员组":
                                membersGrid.Rows.Add("1", "admin", "管理员", "系统管理员", "信息科", "2023-01-10");
                                break;
                            case "医生组":
                                membersGrid.Rows.Add("2", "doctor1", "张医生", "医生", "外科", "2023-02-15");
                                membersGrid.Rows.Add("6", "doctor2", "刘医生", "医生", "内科", "2023-06-05");
                                membersGrid.Rows.Add("8", "doctor3", "钱医生", "医生", "手术室", "2023-08-15");
                                break;
                            case "护士组":
                                membersGrid.Rows.Add("3", "nurse1", "李护士", "护士", "内科", "2023-03-20");
                                membersGrid.Rows.Add("7", "nurse2", "孙护士", "护士", "外科", "2023-07-10");
                                membersGrid.Rows.Add("9", "nurse3", "周护士", "护士", "手术室", "2023-09-20");
                                break;
                            case "操作员组":
                                membersGrid.Rows.Add("4", "operator1", "王操作员", "操作员", "消毒中心", "2023-04-25");
                                membersGrid.Rows.Add("10", "operator2", "吴操作员", "操作员", "消毒中心", "2023-10-25");
                                break;
                            case "信息科组":
                                membersGrid.Rows.Add("1", "admin", "管理员", "系统管理员", "信息科", "2023-01-10");
                                membersGrid.Rows.Add("11", "it1", "技术员1", "技术员", "信息科", "2023-11-05");
                                break;
                            default:
                                // Add some default data
                                membersGrid.Rows.Add("0", "user1", "用户1", "普通用户", "未知", "2023-01-01");
                                break;
                        }
                    }
                }
            }
        }

        private void ShowAddGroupDialog()
        {
            // Create a form for adding a new group
            Form addGroupForm = new Form();
            addGroupForm.Text = "添加用户组";
            addGroupForm.Width = 400;
            addGroupForm.Height = 250;
            addGroupForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            addGroupForm.StartPosition = FormStartPosition.CenterParent;
            addGroupForm.MaximizeBox = false;
            addGroupForm.MinimizeBox = false;

            // Group name
            Label nameLabel = new Label();
            nameLabel.Text = "组名称:";
            nameLabel.Location = new Point(30, 30);
            addGroupForm.Controls.Add(nameLabel);

            TextBox nameTextBox = new TextBox();
            nameTextBox.Location = new Point(120, 30);
            nameTextBox.Width = 230;
            addGroupForm.Controls.Add(nameTextBox);

            // Description
            Label descLabel = new Label();
            descLabel.Text = "描述:";
            descLabel.Location = new Point(30, 70);
            addGroupForm.Controls.Add(descLabel);

            TextBox descTextBox = new TextBox();
            descTextBox.Location = new Point(120, 70);
            descTextBox.Width = 230;
            descTextBox.Height = 80;
            descTextBox.Multiline = true;
            addGroupForm.Controls.Add(descTextBox);

            // Save button
            Button saveButton = new Button();
            saveButton.Text = "保存";
            saveButton.Location = new Point(120, 170);
            saveButton.Width = 80;
            saveButton.BackColor = Color.FromArgb(0, 120, 215);
            saveButton.ForeColor = Color.White;
            saveButton.Click += (s, e) => {
                if (string.IsNullOrEmpty(nameTextBox.Text))
                {
                    MessageBox.Show("请填写组名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                MessageBox.Show($"已添加用户组: {nameTextBox.Text}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // Add the new group to the list
                foreach (Control control in this.Controls)
                {
                    if (control is SplitContainer splitContainer)
                    {
                        Panel leftPanel = splitContainer.Panel1.Controls[0] as Panel;
                        foreach (Control panelControl in leftPanel.Controls)
                        {
                            if (panelControl is ListBox groupListBox)
                            {
                                groupListBox.Items.Add(nameTextBox.Text);
                                groupListBox.SelectedIndex = groupListBox.Items.Count - 1;
                                break;
                            }
                        }
                    }
                }
                
                addGroupForm.Close();
            };
            addGroupForm.Controls.Add(saveButton);

            // Cancel button
            Button cancelButton = new Button();
            cancelButton.Text = "取消";
            cancelButton.Location = new Point(220, 170);
            cancelButton.Width = 80;
            cancelButton.Click += (s, e) => addGroupForm.Close();
            addGroupForm.Controls.Add(cancelButton);

            // Show the form
            addGroupForm.ShowDialog();
        }
    }
} 
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    public class StoreItemDialog : Form
    {
        private ComboBox comboBoxGrid;
        private NumericUpDown numericUpDownNum;
        private Button btnOK;
        private Button btnCancel;
        private Label labelGrid;
        private Label labelNum;

        public int SelectedFloorNum { get; private set; }
        public int SelectedLatticeNum { get; private set; }
        public int StoreNum { get; private set; }

        public StoreItemDialog(List<StoreGridInfo> emptyGrids, int defaultNum)
        {
            this.Text = "选择存放位置";
            this.Size = new Size(350, 200);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;

            labelGrid = new Label { Text = "选择空闲格子:", Location = new Point(30, 30), AutoSize = true };
            comboBoxGrid = new ComboBox { Location = new Point(130, 25), Width = 150, DropDownStyle = ComboBoxStyle.DropDownList };
            foreach (var grid in emptyGrids)
            {
                comboBoxGrid.Items.Add($"{grid.floorNum}层-{grid.latticeNum}格");
            }
            if (comboBoxGrid.Items.Count > 0) comboBoxGrid.SelectedIndex = 0;

            labelNum = new Label { Text = "存放数量:", Location = new Point(30, 70), AutoSize = true };
            numericUpDownNum = new NumericUpDown { Location = new Point(130, 65), Width = 150, Minimum = 1, Maximum = 9999, Value = defaultNum };

            btnOK = new Button { Text = "确定", Location = new Point(60, 120), Width = 80 };
            btnCancel = new Button { Text = "取消", Location = new Point(180, 120), Width = 80 };

            btnOK.Click += (s, e) =>
            {
                if (comboBoxGrid.SelectedIndex >= 0)
                {
                    var sel = emptyGrids[comboBoxGrid.SelectedIndex];
                    SelectedFloorNum = sel.floorNum;
                    SelectedLatticeNum = sel.latticeNum;
                    StoreNum = (int)numericUpDownNum.Value;
                    this.DialogResult = DialogResult.OK;
                }
                else
                {
                    MessageBox.Show("请选择空闲格子！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };
            btnCancel.Click += (s, e) => { this.DialogResult = DialogResult.Cancel; };

            this.Controls.Add(labelGrid);
            this.Controls.Add(comboBoxGrid);
            this.Controls.Add(labelNum);
            this.Controls.Add(numericUpDownNum);
            this.Controls.Add(btnOK);
            this.Controls.Add(btnCancel);
        }
    }
}
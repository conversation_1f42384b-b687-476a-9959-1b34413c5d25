using System;
using System.Drawing;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.PackagingSterilization
{
    public partial class CustomConfirmDialog : Form
    {
        private Label lblTitle;
        private Label lblPackageCode;
        private Label lblPackageName;
        private Button btnYes;
        private Button btnNo;
        private Button btnCancel;
        private PictureBox pictureBox;

        public CustomConfirmDialog()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 设置窗体属性
            this.Text = "操作选择";
            this.Size = new Size(500, 300);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;

            // 图标
            this.pictureBox = new PictureBox();
            this.pictureBox.Location = new Point(80, 80);
            this.pictureBox.Size = new Size(64, 64);
            this.pictureBox.BackColor = Color.FromArgb(52, 152, 219);
            this.pictureBox.Paint += PictureBox_Paint;
            this.Controls.Add(this.pictureBox);

            // 标题标签
            this.lblTitle = new Label();
            this.lblTitle.Text = "选择操作:";
            this.lblTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            this.lblTitle.Location = new Point(160, 80);
            this.lblTitle.Size = new Size(300, 25);
            this.lblTitle.ForeColor = Color.FromArgb(44, 62, 80);
            this.Controls.Add(this.lblTitle);

            // 包条码标签
            this.lblPackageCode = new Label();
            this.lblPackageCode.Text = "包条码: ";
            this.lblPackageCode.Font = new Font("微软雅黑", 10F);
            this.lblPackageCode.Location = new Point(160, 115);
            this.lblPackageCode.Size = new Size(300, 20);
            this.lblPackageCode.ForeColor = Color.FromArgb(52, 73, 94);
            this.Controls.Add(this.lblPackageCode);

            // 包名称标签
            this.lblPackageName = new Label();
            this.lblPackageName.Text = "包名称: ";
            this.lblPackageName.Font = new Font("微软雅黑", 10F);
            this.lblPackageName.Location = new Point(160, 140);
            this.lblPackageName.Size = new Size(300, 20);
            this.lblPackageName.ForeColor = Color.FromArgb(52, 73, 94);
            this.Controls.Add(this.lblPackageName);

            // 是按钮
            this.btnYes = new Button();
            this.btnYes.Text = "是(Y)";
            this.btnYes.Font = new Font("微软雅黑", 10F);
            this.btnYes.Location = new Point(100, 200);
            this.btnYes.Size = new Size(80, 35);
            this.btnYes.BackColor = Color.FromArgb(52, 152, 219);
            this.btnYes.ForeColor = Color.White;
            this.btnYes.FlatStyle = FlatStyle.Flat;
            this.btnYes.FlatAppearance.BorderSize = 0;
            this.btnYes.DialogResult = DialogResult.Yes;
            this.btnYes.Click += BtnYes_Click;
            this.Controls.Add(this.btnYes);

            // 否按钮
            this.btnNo = new Button();
            this.btnNo.Text = "否(N)";
            this.btnNo.Font = new Font("微软雅黑", 10F);
            this.btnNo.Location = new Point(210, 200);
            this.btnNo.Size = new Size(80, 35);
            this.btnNo.BackColor = Color.FromArgb(149, 165, 166);
            this.btnNo.ForeColor = Color.White;
            this.btnNo.FlatStyle = FlatStyle.Flat;
            this.btnNo.FlatAppearance.BorderSize = 0;
            this.btnNo.DialogResult = DialogResult.No;
            this.btnNo.Click += BtnNo_Click;
            this.Controls.Add(this.btnNo);

            // 取消按钮
            this.btnCancel = new Button();
            this.btnCancel.Text = "取消";
            this.btnCancel.Font = new Font("微软雅黑", 10F);
            this.btnCancel.Location = new Point(320, 200);
            this.btnCancel.Size = new Size(80, 35);
            this.btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.DialogResult = DialogResult.Cancel;
            this.btnCancel.Click += BtnCancel_Click;
            this.Controls.Add(this.btnCancel);

            // 设置默认按钮和取消按钮
            this.AcceptButton = this.btnYes;
            this.CancelButton = this.btnCancel;

            // 添加键盘快捷键
            this.KeyPreview = true;
            this.KeyDown += CustomConfirmDialog_KeyDown;

            this.ResumeLayout(false);
        }

        private void PictureBox_Paint(object sender, PaintEventArgs e)
        {
            // 绘制问号图标
            Graphics g = e.Graphics;
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
            
            // 绘制圆形背景
            using (Brush brush = new SolidBrush(Color.FromArgb(52, 152, 219)))
            {
                g.FillEllipse(brush, 0, 0, 64, 64);
            }
            
            // 绘制问号
            using (Brush textBrush = new SolidBrush(Color.White))
            {
                Font font = new Font("Arial", 28, FontStyle.Bold);
                StringFormat sf = new StringFormat();
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;
                g.DrawString("?", font, textBrush, new RectangleF(0, 0, 64, 64), sf);
            }
        }

        private void CustomConfirmDialog_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Y:
                    this.DialogResult = DialogResult.Yes;
                    this.Close();
                    break;
                case Keys.N:
                    this.DialogResult = DialogResult.No;
                    this.Close();
                    break;
                case Keys.Escape:
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                    break;
            }
        }

        private void BtnYes_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Yes;
            this.Close();
        }

        private void BtnNo_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.No;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        public void SetPackageInfo(string packageCode, string packageName)
        {
            this.lblPackageCode.Text = $"包条码: {packageCode}";
            this.lblPackageName.Text = $"包名称: {packageName}";
        }
    }
}

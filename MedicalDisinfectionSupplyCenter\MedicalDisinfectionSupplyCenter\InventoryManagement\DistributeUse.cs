using DevExpress.XtraEditors;
using MedicalDisinfectionSupplyCenter.DepartmentManagement;
using MedicalDisinfectionSupplyCenter.InventoryManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    public partial class DistributeUse : DevExpress.XtraEditors.XtraUserControl
    {
        private int _pageIndex = 1;
        private int _pageSize = 10;
        private int _totalPage = 1;
        private int _totalCount = 0;

        public DistributeUse()
        {
            InitializeComponent();
            this.Load += DistributeUse_Load;
            this.btnSearch.Click += BtnSearch_Click;
            this.btnPrevPage.Click += BtnPrevPage_Click;
            this.btnNextPage.Click += BtnNextPage_Click;
            this.comboBoxPageSize.SelectedIndexChanged += ComboBoxPageSize_SelectedIndexChanged;
            this.btnDistribute.Click += BtnDistribute_Click;
        }

        private void ComboBoxPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(comboBoxPageSize.SelectedItem?.ToString(), out int size))
            {
                _pageSize = size;
                _pageIndex = 1;
                BindGrid();
            }
        }

        private void BtnPrevPage_Click(object sender, EventArgs e)
        {
            if (_pageIndex > 1)
            {
                _pageIndex--;
                BindGrid();
            }
        }

        private void BtnNextPage_Click(object sender, EventArgs e)
        {
            if (_pageIndex < _totalPage)
            {
                _pageIndex++;
                BindGrid();
            }
        }

        private void DistributeUse_Load(object sender, EventArgs e)
        {
            InitGridView();
            this.dateEditStart.EditValue = null;
            this.dateEditEnd.EditValue = null;
            this.comboBoxPageSize.SelectedIndex = 0; // 默认10条
            BindGrid();
        }

        private void InitGridView()
        {
            // 设置表头字体和背景色
            this.gridView1.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(221, 235, 247); // 淡蓝色
            this.gridView1.Appearance.HeaderPanel.Options.UseBackColor = true;
            this.gridView1.Appearance.HeaderPanel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.gridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;

            // 清空已有列
            this.gridView1.Columns.Clear();

            // 添加列
            this.gridView1.Columns.AddVisible("id", "主键"); // 新增主键列
            this.gridView1.Columns.AddVisible("lssueCode", "发放编码");
            this.gridView1.Columns.AddVisible("applyingUnitNmae", "接收单位");
            this.gridView1.Columns.AddVisible("acceptName", "接收人");
            this.gridView1.Columns.AddVisible("lssueName", "发放人");
            this.gridView1.Columns.AddVisible("lssueDate", "发放时间");
            var colCreateTime = this.gridView1.Columns.AddVisible("createTime", "创建时间");
            colCreateTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colCreateTime.DisplayFormat.FormatString = "yyyy-MM-dd";
            // 状态列（蓝色文本，不可点击）
            var colStatus = this.gridView1.Columns.AddVisible("lssueStateName", "状态");
            colStatus.AppearanceCell.ForeColor = System.Drawing.Color.FromArgb(0, 112, 192); // 蓝色
            colStatus.AppearanceCell.Options.UseForeColor = true;

            // 新增操作列
            var colAction = this.gridView1.Columns.AddVisible("action", "操作");
            colAction.UnboundType = DevExpress.Data.UnboundColumnType.String;
            colAction.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            var btnView = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            btnView.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
            btnView.Buttons[0].Caption = "查看";
            btnView.Buttons[0].Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph;
            // 添加删除按钮
            btnView.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph)
            {
                Caption = "删除"
            });
            // 添加接收按钮
            btnView.Buttons.Add(new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph)
            {
                Caption = "接收"
            });
            this.gridControl1.RepositoryItems.Add(btnView);
            colAction.ColumnEdit = btnView;
            btnView.ButtonClick += BtnView_ButtonClick;
            // 隐藏id列
            if (this.gridView1.Columns["id"] != null)
                this.gridView1.Columns["id"].Visible = false;
        }

        private void BindStaticData()
        {
            var table = new System.Data.DataTable();
            table.Columns.Add("id", typeof(string)); // 新增主键列
            table.Columns.Add("lssueCode", typeof(string));
            table.Columns.Add("lssueName", typeof(string)); 
            table.Columns.Add("lssueDate", typeof(string));
            table.Columns.Add("applyingUnitNmae", typeof(string));
            table.Columns.Add("acceptName", typeof(string));
            table.Columns.Add("lssueStateName", typeof(string));
            table.Columns.Add("createTime", typeof(string));
            table.Columns.Add("action", typeof(string)); // 新增操作列
            table.Columns.Add("lssueState", typeof(string)); // 新增lssueState列

            table.Rows.Add("1", "F001", "张三", "2022-05-19 10:00:00", "外科", "李四", "已发放", "2022-05-18", "0", "查看");
            table.Rows.Add("2", "F002", "王五", "2022-05-19 10:00:00", "内科", "赵六", "已接收", "2022-05-18", "1", "查看");
            table.Rows.Add("3", "F003", "赵七", "2022-05-19 10:00:00", "急诊", "钱八", "已接收", "2022-05-18", "2", "查看");

            this.gridControl1.DataSource = table;
        }

        private async void BindGrid()
        {
            try
            {
                string baseUrl = ReadApiConfig.GetApiUrl("Wms", "GetLssueByPage");
                var paramList = new List<string>();
                if (this.dateEditStart.EditValue != null && DateTime.TryParse(this.dateEditStart.EditValue.ToString(), out DateTime start))
                {
                    paramList.Add($"StartTime={Uri.EscapeDataString(start.ToString("yyyy-MM-dd HH:mm:ss"))}");
                }
                if (this.dateEditEnd.EditValue != null && DateTime.TryParse(this.dateEditEnd.EditValue.ToString(), out DateTime end))
                {
                    paramList.Add($"EndTime={Uri.EscapeDataString(end.ToString("yyyy-MM-dd HH:mm:ss"))}");
                }
                paramList.Add($"PageIndex={_pageIndex}");
                paramList.Add($"PageSize={_pageSize}");
                string url = baseUrl;
                if (paramList.Count > 0)
                {
                    url += (baseUrl.Contains("?") ? "&" : "?") + string.Join("&", paramList);
                }

                var result = await HttpClientHelper.ClientAsync("GET", url, false, null);
                var client = new HttpClient();
                var response = await client.GetStringAsync(url);
                // 容错：接口返回内容为空或不是JSON
                if (string.IsNullOrWhiteSpace(result) || !result.TrimStart().StartsWith("{"))
                {
                    MessageBox.Show("接口返回内容不是有效的JSON：" + result);
                    gridControl1.DataSource = null;
                    return;
                }

                var jobj = JObject.Parse(result);
                if (jobj["code"]?.ToObject<int>() == 200)
                {
                    var pageData = jobj["data"]?["pageData"]?.ToObject<List<JObject>>();
                    _totalCount = jobj["data"]?["totalCount"]?.ToObject<int>() ?? 0;
                    _totalPage = (_totalCount + _pageSize - 1) / _pageSize;
                    if (_totalPage == 0) _totalPage = 1;
                    lblPageInfo.Text = $"第{_pageIndex}页/共{_totalPage}页";
                    if (pageData != null)
                    {
                        // 创建DataTable并定义列
                        var table = new System.Data.DataTable();
                        table.Columns.Add("id", typeof(string)); // 新增主键列
                        table.Columns.Add("lssueCode", typeof(string));
                        table.Columns.Add("lssueName", typeof(string));
                        table.Columns.Add("lssueDate", typeof(string));
                        table.Columns.Add("applyingUnitNmae", typeof(string));
                        table.Columns.Add("acceptName", typeof(string));
                        table.Columns.Add("lssueStateName", typeof(string));
                        table.Columns.Add("createTime", typeof(string));
                        table.Columns.Add("action", typeof(string)); // 新增操作列
                        table.Columns.Add("lssueState", typeof(string)); // 新增lssueState列
                        // 填充数据
                        foreach (var item in pageData)
                        {
                            table.Rows.Add(
                                item["id"]?.ToString() ?? "",
                                item["lssueCode"]?.ToString() ?? "",
                                item["lssueName"]?.ToString() ?? "",
                                item["lssueDate"]?.ToString() ?? "",
                                item["applyingUnitNmae"]?.ToString() ?? "",
                                item["acceptName"]?.ToString() ?? "",
                                item["lssueStateName"]?.ToString() ?? "",
                                item["createTime"]?.ToString() ?? "",
                                "查看" // 新增
                            );
                        }
                        gridControl1.DataSource = table;
                    }
                    else
                    {
                        gridControl1.DataSource = null;
                    }
                }
                else
                {
                    MessageBox.Show("接口返回错误：" + (jobj["msg"]?.ToString() ?? "未知错误"));
                    gridControl1.DataSource = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("获取发放数据失败：" + ex.Message);
                gridControl1.DataSource = null;
            }
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            _pageIndex = 1;
            BindGrid();
        }

        private async void BtnDistribute_Click(object sender, EventArgs e)
        {
            string url = ReadApiConfig.GetApiUrl("Wms", "GetApprovedOutList");
            string result = null;
            JArray dataArray = null;
            try
            {
                result = await HttpClientHelper.ClientAsync("GET", url, false, null);
                var jobj = JObject.Parse(result);
                if (jobj["code"]?.ToObject<int>() == 200)
                {
                    dataArray = jobj["data"] as JArray;
                }
            }
            catch (Exception ex)
            {
                result = $"接口调用失败：{ex.Message}";
            }

            var form = new DevExpress.XtraEditors.XtraForm();
            form.Text = "Tips";
            form.FormBorderStyle = FormBorderStyle.Sizable;
            form.StartPosition = FormStartPosition.CenterParent;
            form.Size = new System.Drawing.Size(900, 600);
            form.MaximizeBox = false;
            form.MinimizeBox = false;
            form.ShowIcon = false;

            if (dataArray != null && dataArray.Count > 0)
            {
                var table = new System.Data.DataTable();
                table.Columns.Add("id", typeof(string)); // 主键列
                table.Columns.Add("outCode", typeof(string));
                table.Columns.Add("outWarehouseName", typeof(string));
                table.Columns.Add("outReasonName", typeof(string));
                table.Columns.Add("applyingUnitName", typeof(string));
                table.Columns.Add("auditStateName", typeof(string));
                table.Columns.Add("action", typeof(string)); // 操作列
                table.Columns.Add("lssueState", typeof(string)); // 新增lssueState列
                foreach (var item in dataArray)
                {
                    table.Rows.Add(
                        item["id"]?.ToString() ?? "",
                        item["outCode"]?.ToString() ?? "",
                        item["outWarehouseName"]?.ToString() ?? "",
                        item["outReasonName"]?.ToString() ?? "",
                        item["applyingUnitName"]?.ToString() ?? "",
                        item["auditStateName"]?.ToString() ?? "",
                        "发放"
                    );
                }
                var grid = new DevExpress.XtraGrid.GridControl();
                var view = new DevExpress.XtraGrid.Views.Grid.GridView();
                grid.MainView = view;
                grid.ViewCollection.Add(view);
                grid.DataSource = table;
                grid.Location = new System.Drawing.Point(20, 20);
                grid.Size = new System.Drawing.Size(820, 480);
                form.Controls.Add(grid);
                // 生成列
                view.PopulateColumns();
                if (view.Columns["id"] != null) view.Columns["id"].Visible = false;
                // 安全设置表头
                if (view.Columns["outCode"] != null) view.Columns["outCode"].Caption = "出库单号";
                if (view.Columns["outWarehouseName"] != null) view.Columns["outWarehouseName"].Caption = "库房名称";
                if (view.Columns["outReasonName"] != null) view.Columns["outReasonName"].Caption = "出库原因";
                if (view.Columns["applyingUnitName"] != null) view.Columns["applyingUnitName"].Caption = "申请单位";
                if (view.Columns["auditStateName"] != null) view.Columns["auditStateName"].Caption = "状态名称";
                if (view.Columns["action"] != null)
                {
                    view.Columns["action"].Caption = "操作";
                    var btnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
                    btnEdit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
                    btnEdit.Buttons[0].Caption = "发放";
                    btnEdit.Buttons[0].Kind = DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph;
                    grid.RepositoryItems.Add(btnEdit);
                    view.Columns["action"].ColumnEdit = btnEdit;
                    view.Columns["action"].ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
                    btnEdit.ButtonClick += (s, args) =>
                    {
                        var row = view.GetDataRow(view.FocusedRowHandle);
                        if (row != null)
                        {
                            string outCode = row["outCode"]?.ToString() ?? "";
                            XtraMessageBox.Show(form, $"发放{outCode}成功！", "操作", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            form.Close();
                        }
                    };
                }
                // 添加下方发放按钮
                var btnDistributeRow = new DevExpress.XtraEditors.SimpleButton();
                btnDistributeRow.Text = "发放";
                btnDistributeRow.Location = new System.Drawing.Point(720, 520);
                btnDistributeRow.Size = new System.Drawing.Size(120, 40);
                form.Controls.Add(btnDistributeRow);
                // 添加发放人和接收人输入框（作用域提升）
                var txtLssueName = new DevExpress.XtraEditors.TextEdit();
                txtLssueName.Location = new System.Drawing.Point(400, 520);
                txtLssueName.Size = new System.Drawing.Size(120, 40);
                txtLssueName.Properties.NullValuePrompt = "发放人";
                form.Controls.Add(txtLssueName);

                var txtAcceptName = new DevExpress.XtraEditors.TextEdit();
                txtAcceptName.Location = new System.Drawing.Point(540, 520);
                txtAcceptName.Size = new System.Drawing.Size(120, 40);
                txtAcceptName.Properties.NullValuePrompt = "接收人";
                form.Controls.Add(txtAcceptName);
                // 添加显示选中id的Label
                var lblSelectedId = new DevExpress.XtraEditors.LabelControl();
                lblSelectedId.Location = new System.Drawing.Point(20, 520);
                lblSelectedId.Size = new System.Drawing.Size(300, 40);
                lblSelectedId.Text = "当前选中Id: ";
                form.Controls.Add(lblSelectedId);

                // 移除自动更新Label的事件，只在弹框初始化时显示一次
                var rowInit = view.GetDataRow(view.FocusedRowHandle);
                //if (rowInit != null)
                //    lblSelectedId.Text = $"当前选中Id: {rowInit["id"]}";
                //else
                //    lblSelectedId.Text = "当前选中Id: ";
                btnDistributeRow.Click += async (s, args) =>
                {
                    int rowHandle = view.FocusedRowHandle;
                    var row = view.GetDataRow(rowHandle);
                    string lssueName = txtLssueName.Text.Trim();
                    string acceptName = txtAcceptName.Text.Trim();
                    if (string.IsNullOrEmpty(lssueName))
                    {
                        XtraMessageBox.Show(form, "请输入发放人！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtLssueName.Focus();
                        return;
                    }
                    if (string.IsNullOrEmpty(acceptName))
                    {
                        XtraMessageBox.Show(form, "请输入接收人！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtAcceptName.Focus();
                        return;
                    }
                    if (row != null)
                    {
                        int outId = 0;
                        int.TryParse(row["id"]?.ToString(), out outId);
                        //XtraMessageBox.Show(form, $"当前选中Id: {outId}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        var postData = new
                        {
                            outId = outId,
                            lssueName = lssueName,
                            acceptName = acceptName
                        };
                        string apiUrl = WriteApiConfig.GetApiUrl("Wms", "AddLssueFromOut");
                        try
                        {
                            string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(postData);
                            var httpContent = new StringContent(postJson, Encoding.UTF8, "application/json");
                            var postResult = await HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                            if (string.IsNullOrEmpty(postResult))
                            {
                                // 解析API响应
                                var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(postResult);
                                string message = apiResponse?.msg ?? "操作完成";
                                XtraMessageBox.Show(form, message, "操作提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                form.Close();
                                // 返回是否提交成功
                                //return apiResponse != null && apiResponse.code == 200;
                            }
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(form, $"接口调用失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show(form, "请先选择一行！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                };
                view.OptionsView.ShowGroupPanel = false;
            }
            else
            {
                var lblContent = new DevExpress.XtraEditors.LabelControl();
                lblContent.Text = string.IsNullOrWhiteSpace(result) ? "无数据" : result;
                lblContent.Location = new System.Drawing.Point(30, 40);
                lblContent.Size = new System.Drawing.Size(640, 40);
                form.Controls.Add(lblContent);
            }

            form.ShowDialog(this);
        }

        // 新增事件处理方法
        private async void BtnView_ButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            var view = this.gridView1;
            var row = view.GetDataRow(view.FocusedRowHandle);
            if (row == null) return;
            string id = row["id"]?.ToString();
            if (string.IsNullOrEmpty(id))
            {
                XtraMessageBox.Show(this, "未获取到主键Id", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            if (e.Button.Index == 0) // 查看按钮
            {
                string url = ReadApiConfig.GetApiUrl("Wms", "GetLssueDetailById") + $"?Id={id}";
                try
                {
                    string result = await HttpClientHelper.ClientAsync("GET", url, false, null);
                    var jobj = JObject.Parse(result);
                    if (jobj["code"]?.ToObject<int>() == 200)
                    {
                        var data = jobj["data"];
                        // 构造主信息
                        string detail = $"发放编码: {data["lssueCode"]}\n" +
                                        $"发放人: {data["lssueName"]}\n" +
                                        $"发放时间: {data["lssueDate"]}\n" +
                                        $"接收人: {data["acceptName"]}\n" +
                                        $"状态: {data["lssueStateName"]}\n";
                        // 构造明细表
                        var details = data["details"] as JArray;
                        if (details != null && details.Count > 0)
                        {
                            detail += "\n明细:\n";
                            foreach (var d in details)
                            {
                                detail += $"物资类型: {d["materialTypeName"]}, 物资编码: {d["materialCode"]}, 物资名称: {d["materialName"]}, 数量: {d["lssueNum"]}\n";
                            }
                        }
                        XtraMessageBox.Show(this, detail, "发放详情", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        XtraMessageBox.Show(this, jobj["msg"]?.ToString() ?? "接口返回错误", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(this, $"接口调用失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else if (e.Button.Index == 1) // 删除按钮
            {
                var confirm = XtraMessageBox.Show(this, "确定要删除该发放记录吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (confirm == DialogResult.Yes)
                {
                    string apiUrl = WriteApiConfig.GetApiUrl("Wms", "DeleteLssue");
                    if (!int.TryParse(id, out int intId))
                    {
                        XtraMessageBox.Show(this, "主键Id格式不正确，无法删除。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    var postData = new { id = intId };
                    try
                    {
                        string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(postData);
                        var httpContent = new StringContent(postJson, Encoding.UTF8, "application/json");
                        string result = await HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                        var jobj = JObject.Parse(result);
                        string msg = jobj["msg"]?.ToString() ?? "无返回信息";
                        XtraMessageBox.Show(this, msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        BindGrid(); // 无论成功失败都刷新列表
                    }
                    catch (Exception ex)
                    {
                        XtraMessageBox.Show(this, $"接口调用失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else if (e.Button.Index == 2) // 接收按钮
            {
                string apiUrl = WriteApiConfig.GetApiUrl("Wms", "SignLssue");
                if (!int.TryParse(id, out int intId))
                {
                    XtraMessageBox.Show(this, "主键Id格式不正确，无法接收。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                var postData = new { id = intId };
                try
                {
                    string postJson = Newtonsoft.Json.JsonConvert.SerializeObject(postData);
                    var httpContent = new StringContent(postJson, Encoding.UTF8, "application/json");
                    string result = await HttpClientHelper.ClientAsync("POST", apiUrl, false, httpContent);
                    var jobj = JObject.Parse(result);
                    string msg = jobj["msg"]?.ToString() ?? "无返回信息";
                    XtraMessageBox.Show(this, msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    BindGrid(); // 无论成功失败都刷新列表
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(this, $"接口调用失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}

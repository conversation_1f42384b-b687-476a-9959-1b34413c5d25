﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAW
        JQAAFiUBSVIk8AAAFJlJREFUaEO9WQdYVFmavRN2e2a3e7+dXUdmRpKijBjbBKiEqqKKAhQEVKRBEETJ
        TXoFKEoriIiICVHEgFlRKECgCJJbRJIgSJKWIAgSTK0ooMjZ774qgvT0zE73zPzfd75b77373jvnhvPf
        d4uQnxEihpkjYhgbEcMcEDGMRMQwjSKGeS1imJEJoMf0PL1O69H6cyY/618WIob5vYhhrEQMc32bv/+j
        Y5GR1ekSSVNdXV1fX1/f4NDQ0MeRkRGMgh7T8/Q6rUfr0/vo/bLn/H7yO/4pIWKYz2nr+fv5lR85cqSq
        vLz88fv374c/fPiAFy9eoKmpCQUFhbh67QYOHTmOoJBwHDgYifMXryI7Oxd1dfV49uwZaP2hoaHhsrKy
        x/Q59HmyXvl88jv/YSFiGA1/P7/ko0ePVra0tPSOjIx8fPnyJcrKyhEdcwbHT11F9IVbOHqhBAfOVmDv
        qSrsPnEPgZHFCDiYA/99yfANvoDtu44iZN9B5OcXsGI+fvw43NLS0kOfS59P3zP53T87RAxjvXPHjtqK
        8vIWSpy2dlbWLZy9EIdLSaWIutGGkNjH8I9qgefhZrgeaIHT/lZsCX0Eh70PsXlPLRyCKrEl6C62fpML
        p8BkuAWcg6f/fly5ch09PT10qH0sLytrpu8RMczGyRx+cogYxufwoUMVfX19r4eGhlBeUYEz567i8s37
        iLzeCb+odrgfegLngz3YEtELh/Be2IX1YFNoNzaGPIX1ni5YBXXAcncrLAObsGFnHSwDKmC9rRB2fknY
        IjoNV+9gZGZm4d27d+jt7f3+8KFDdEgxk7n83UEfci42toKOczpcEsRJuJTwLY4ndMH32GO4RHRia0Qf
        Noc/h334M2wK64NtaA9s9vbAMqgbZjs6YRrQjrU727E+8DHMd7TCLKAZptsaYeb7AOZMCdYz2djofRX2
        HocQHhGFp0+fgr7v3LlzFT9LBL35/LlzFdRF6ENjz1/GpZs12BHTBpeIDmwN74H9fkr6OWxD+2AT2ssS
        twnpwdpdXWyP7L/6CiGXXsB2bydM/FtgFtCCNdu+g4n/Q6wW1WOVTy1We9+DiVcBLDzFsHaLhJv3LjQ+
        fMi6V+zZs1SEz2RufzPoGKTdSFuCko85cxHRNx6COdqMLWEdsA/rlZHug83eXliH9MA6pBvWe7qxNrAT
        HpG9eD2IsaC/nQ60w5B5CBNfSr4BRkw9DLxrIfSshtC9AobuhTBzT4SlyzFscvRHY2Mj61Th4eFVIoZZ
        O5njj4aIYTR37tjxgI5FOrnOxF5CzI16eB56iM2hj7FxbzesQrphtWcUT/FVcBcLi12dsAvtwrM34+RH
        o6zhHYRetTBkGmHoIyWv71kNgcd98NwqwXMpg8A5H8YuYlg4RWKL6zZ0dXWhu7v7dcD27TQJLprM9QdB
        fZhaGXUDOqFuxItx4loVPCIaYLunBVbBnbAM6sKGoE4pdlN0YMOudljsaofJ9jZsP9UzmftY2ARWgO9R
        zwoReNSA734fXNdKcFwqoeNUBh3HYnC35sDI8TosHA8icPd+9Pf3o7CwsNnP1/eqiGF+M5nzJ0GTCfVj
        ammlpaU4fSUbPocbYBP0HSxZkh1YHyglS0s6MdfvfIy1gW1Yu7MVxtta4BXZNZk3G/3972HmlQe+ex30
        ParBd7sPnmsVuC6V0HWugPaWUmg53MXKzd+C45CJVVsuwsIhBOLEmzRXjOwLDa1lfHyMJ3MeCxHDTPH3
        8yt59OhR7/PnzxEVfR77Yptgs6se63e2wpwS3fEYa3dSsm0w3zGKVpm70AnaDGPfJlQ2vZvMH5eS70Pd
        KhP6HrXj5J3vQdepAtqOZVLy9nehaVeE5XYF4NilwHhzNDbY+aOzsxOVlZXdviJRrrub239N5s4GTVZH
        jhyhrT+ckZGJiDNFcNhTg/U7HsJsezNMt7dMwITjbc2ss0jdpQmGPg9h5l+PuIxWPHv5Bp1PXyHmaikM
        nDPBdakC360aPJcqcJzvQcexTNbyJVhufxcadnegsakI6jYF0NyYBZ5tHNbYhePEybN0+TGyJzj4ocfX
        XxtO5s6GiGFulJeXt9EsG3H0DESH7mN9QDXW+DfC2L8JJr5Sgia+46Ctbcy6ihRGTCNW+zRC37MeHOdK
        GLnfgb5zHjRsCqHrXAW+azV7XtfpHnS2VEDboQxam6XkNTfdgbptEZbZ3MYSqwIsscqFplUKhLYnscF+
        J5486URCfPxjTw+Pg4SQX04mP4euDqlt3rlTjP0nMmETWA5jUa2UlGgUlGgjVjONMGKoFTbA0EcGb4p6
        CD3rwHF5wELbWQpdWjpWQ8+tGlynKug63oP25jKstC+Bhm0x1G2KsMyqEEu+KmCxyDIPX27Iw2KLdKzY
        cBF663YhTXIL7e3tgyKGafnK0vJPkwXY0CUuXSUePBwF34g7MBOVw9C7FgY+9aztSUHJ1rNEKQy862Dg
        VQ8DrzoYeNZB4F4LvmsVrL9pxI6T7Z+AzqUlVnewcH0eZq9Jx0zDFCgbJEFRXwxFQYIMYijpi6EsTIKK
        QTJmr76JL82uYaXpPnj6hrKOFLR7d6vj1q10GP1iooCw1NTUJjp594SdgH1gEVZ534PQ6wGEXjUQetKE
        Mw79UXjI4F4DgXsN9NxqIPy6GsU1LwF8xNBAP1697ENfdwcKyp5gtv5xyOnG4U+cOEzj3hiDPI8iHvIT
        zrHnudcxQ3ARi4wOYpWFHzo6nuBUTMxjVxeXnYSQX08UkFJTU9NDs9/O0POw8C2A/tfl4LtXge9eDT61
        PVpSuE2AazV4rtRRKKqh61iN1V5VePXqe9TX1YNacVFREfLz8vC4vROz+UegyDnPkp3GjYc8Jx7TOJT4
        OOh59hoVxY2Hst5VzDOIgtZqP9wuKkZqauozN1fXK4SQ8ZwgYpiGnp6egVvZ2fAOuoY1XoXgu5WzSUaK
        qnG4VLEuwnWmTlIFjst9ttTcdBczDNOx0DwNXV3dKC0tQVHRHXbdn52VibY2KiASiroXMI2bMC6AmwB5
        TgIrhCU/QRQ9VuLGQU3/FDRX7cTd0kpqpx++dnfPJ4R8MTaM6Dcr/ew7c+YsXAPjYeReAK5LGXSpT0+E
        UyU4TvdYsL+dK6HlUIE5pplQ4CdgitYNLDBPY327qOg28nLzkJ2VBUlaKlrbOqDGj4TCmIAJYAVM+i0T
        osC9DlVBLJYa7EZ61m00NzePeHl61hJC/pcQ8qtRASN09RcUHIKtOxIhdM2DjmMJtJ3K2CSjvYWWFTLf
        LmMtUMfxHhZa5GGGQTLkeQlQ4IsxVSceC8zS0NbWjrzcHNB8IkmTICkpEc0tbVDjH5P2AEc8AaOExZim
        S5EwDk4CO9xm8i9gsTAEkdGX2JWxn59fJyHkD4SQf/9EACPyg71/AviOudLM6FCKlQ4lbEmTjbQsg6Zd
        iazVxVDgiaXlmIBUtLS0IDMjnY5XJCcnIyHhBr5rpgKioKAzWUDi+O8fCBBDXpcKuIhFwlAcOHKeFcD4
        +HQQQuTH5sGogN1Be7BJFAe9rdlYsfkONO2LoWlXLE00dnex3L4ES22KoGKUCgUetb1EKPIToSDDVG0x
        5q9JYT/uU1NT2JYXi8WIvxH3iQB5TiLkdZMwjUXiX8CoGDE7P2byL2GxMBSnz4tHBTwhhCgTQn47KuDF
        wMDAx+iTMbD1Ogu9LRnQtLsNjU13oGFLM+QdVsCirwqgLEyGIp96t5T8mAAeFZCIucbJqK9rQHKiGAkJ
        CSzGBRyHovZlyOsms5gmKydCKmpcnAInHqr8C1hiEILMnLt48uQJfLy92wgh0wkh/zEqoKarq+tdWpoE
        tu5HwXdIgYZtPpbZfItlNEvaFGHhhnwoGyRDUZAEJUGSjHwSFCh4UsjJBNTW1kEslpKnPZCYKEZLy2PM
        oQJ0rkJeNxXyuikTyon4VIwiJx5q+uegYRSM/G9LUF9fj28CA5sJITMmCkiqqqrqqa6uhr3bXgg2x0Nz
        YzaWWOVjqdVtfGlZICOfKCXPCpBilPyogDkTBCQmJrJzgA4n6kJz+CehqH0N8pw0KHAkUNCVQP4TpMkw
        LkyZm4h5wjPQMdmN8or7kEgk77y9vPIm98C+1JSUpu7ubjg4+0FofxnqVulYbJnLOo2KYSqU9JOkmED+
        hwKSpD1QV4eUFEo8Fenp6ciieaC9E2q8GCjpXIciJwMKnHS2ZKGbAQUW6SzkWUigyEnFDJ4YC4XRMLPd
        h5cvXyHm5Mnv169bd3TyHLCNOnas+v379/ARBcDQ9jjUNyRg0YZbmGUsYYkr6yf/UABLPFkKbgrktJMx
        1zgV9Q2NSJdIkJmZidzcXBQW5ONJZzcrQFknHoqcrB9BJhR1M6HAIh1KnDSo8q9jsTACAcFn2N0/fz+/
        Xm0tLSeZC302KkB1m79/68DAwAfq3frr/KC57hzmmyazCysp+WQoCaRQ5MvAkk9hyVNM1UphBVAXysnJ
        Qn5+PruUKC29i86nvVDjnoKyjhiKnBwZssd+K40dU0jFTOelYb7wErSM9yEz+zaq7t177+7m1iInJ8cl
        hMgRQv6NFSATcbGoqKiN2hRPuBYrTA9hrvEVKAsTWOcZFaA4KoBHkTJBQCqmaqVh/ppUPHrUjMLCAhQX
        F6OiogJ1dbXo7XsBNe5pTNdNhBI3D8qcXChzaCmFEotcFlSQMvcW/iy4iSVGMTC3DUPHk06ciIx8bmFh
        cZkQsoAQ8j9jmVgmYF1ERET1x48fP4btD8cKQy/MNzqBmQbXMV1Ix38KlAQpUKTgjYMKUOSmsZDTkmDB
        GpqJ21BWVorq6hq2N7q6OvHy1RvM5p7GDN2bUOYW/AjyoczJx3RuLmbqZWGh4Q2sWHUAF66k4PXr1yO+
        IlHP8uXLPQkhMwkhdCP4kyX17/x8fe80NDT00LUM7YUlBnugZngBM4SJUBbcnCQgVQqWvASK3HTIaaVj
        wRoJuxZqqK9jhfT09OLNmzd482YAszlUQApmcAtZKMvKicdUiIpeLuYKJVBfFQNzmzB2GZ0YF/dms739
        7S+++IJDCJk2Nv4nhohhzA6Eh9fQXYmzsbHQ0t+MRUaHoWpwBdPZCXwTSvyJAqQtLxWQATmtDCwwzUDf
        s2dsqz979gL9/e/wcXgY/W8HMZtzBjN00zCDe1uGIhmkxyrcbzGTl485+llQN74Grul+3EzNph8ytPV7
        NTQ0/CcMn/HvgQkCfuvn6ysuLi5uff36NdzcvbBM4IMFhlGYpX8NyjSJsQJkrc+TQJGXzpJX4mZCbmUm
        vjTLZFv89fffY2BgEB8+DLMfN1SAGvcsVDgSqPDu/AAzuUWYxbsNNf1cLDNOgrbxEewMjsGrV69w8dSp
        l9ZWVpmfffaZnsw+qf+PD5+JIWKYhdu3bWvq6up6TRdlJmbWWCbYhvkG0ZgljIMyPxlKo63Pk0BJRl6J
        mwW5lVlYZJ6FgcEhDA9/wPDwMIZlAt6+G4Ia9xxUOOlQ4RVDhXd3DDN5xZilV4Q5wnyoG6dCx+QEbBz3
        obW1DQ3V1UOeHh7dqqqq1Dq/JIRM+cR9/lIwPj5rw8LC7tO9yaqqKnY+LNPfgQWG0VDVj8N0PSoiDUq8
        dKkAXhaUuNn448psLDLPRl7Z08nbQsgs6sBsXdoDmTLSUszSuwtV/h3ME+ZDwyQFuqYnYW4djJoHdRh4
        +3Zkm69v94oVK/YRQrQJIUp/tfUnhreXlwtNbnSVWllZidWmVtAQMPjS8BhmC69AhZ8EZT0JlHiZUOJl
        Q5lHfTwXsw1vY617DraHZsF/L0U2AsJywN+YiOkrr+HPvCzM5JVgFu8uVPWKoSa4jS8Nc7DSNBE8syhY
        2QfjfvUDuhuHkG++6Rbw+acJIQaEEDVCyO/+4tj/kfiFp4eHW2Rk5IPBwcFhaofOLl5YIXDBYmE45gtj
        8WfBDajwUzFDjyadHMzg5WE6Nx9/1IjH1CVXIbfkGuSWxGHq4utQXJ4INX4mZvHvQlVQjNn6tzHfMB/q
        JhLoml0BzzQCvgGH0PTdI7zr7x8JCwzsFQgEsYSQNYQQurFLP2Do+v9vt/6E+KW7m5v73pCQuo6Ojv7e
        3l7ExJwCR98S6gI/LDaIxAKDi1DTF0OVL4EqPwuz9HIwU68Qs/npUONnsJgjoEhnSc8xKMRCo1wsM0mH
        jnkcuGZRWLVuO65eE9N/Z9D68OHwN/7+o+TNCCHqsmUDHTqfbmj9P+NXzk5Olv5+fo+ys7Of0C0+6u+h
        oeHQEWyEhsAP6oZHsHTVWSw2vIqFBglYIEzFPGEG5glvYYHwFhYaZWLxagnUTW5Cyywe3HWXwFlzBIZm
        27H/QDTq6xvw4f17ZNy40c94efVpa2nRxZqpjLyiLGmNZ92fEL/eZGur5eXpmbY3JORhfX394ODg4Eht
        bS1OxpyGsak1tPibsFzAQFO4BytWHYH2mhjomseCY06XwiegvfowdFcFQ2+1CBbWHog9d5klPvjuHe4V
        FQ2Fbd/+fLOd3b15c+cGEEKMCCFLCCEK/wjyo/GrpUuXTrG3s3Py9vKqDg4OfiRJTe17298/QjfE6J8h
        fc+eo6S0EuLEDERFX0LE4bOIir6MuPg0FHxbgkfNbWhv7wCt//bNm5HMhIR3YQEBL9xcXFp5XO6Jzz//
        3IoQQhdq8wghfySE/Oc/ivxo0DH4G01NTRVrKys3ZyenDMbH52lIcPDjlMTEgQcPHrD/qrx9+3bsX3qK
        gYEB9jztsbycHBwLCurf5un5vZ2tbTmPyz0+ZcoUO0KIPiFkqexLi26Z0An7k8b83wrqAjSR0I2lP3A5
        HP3169bttLWxueq4dWsJXXD5ikQDIobBKHxFoiE/X9/nDps337ewsJDoCwTRampq3oQQE0IIT0Z8lsxp
        6JChz/+73OanBG0duqCiQqbKUvxcGRktQghN+9S/VxNC6D8rtKTH9Dy9TuvR+vQ+ej99Dn3eP6XV/1rQ
        F9IWozb33zIydLVIsyYdDrRlVWWliuw8vU7r0fr0Pnr/v5z45KBdTknQTElbkn6vUnJ0SIyCHtPz9Dqt
        R+v/7KHyfziUp28KhmiAAAAAAElFTkSuQmCC
</value>
  </data>
</root>
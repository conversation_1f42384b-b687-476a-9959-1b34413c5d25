// OutboundManagement 用户控件，用于出库管理
// 包含控件初始化和相关逻辑
using DevExpress.XtraEditors;
using Newtonsoft.Json;
using MedicalDisinfectionSupplyCenter;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json.Linq;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    // 出库管理控件
    public partial class OutboundManagement : UserControl
    {
        private int _pageIndex = 1;
        private int _pageSize = 10;
        private int _totalPage = 1;
        private int _totalCount = 0;

        // 构造函数，初始化控件
        public OutboundManagement()
        {
            InitializeComponent(); // 初始化界面组件
            InitializeEvents();
        }

        /// <summary>
        /// 初始化事件绑定
        /// </summary>
        private void InitializeEvents()
        {
            this.Load += OutboundManagement_Load;

            // 搜索按钮事件
            if (btnSearch != null)
                this.btnSearch.Click += BtnSearch_Click;

            // 新增按钮事件
            if (btnAdd != null)
                this.btnAdd.Click += BtnAdd_Click;

            // 分页按钮事件
            if (btnPrevPage != null)
                this.btnPrevPage.Click += BtnPrevPage_Click;
            if (btnNextPage != null)
                this.btnNextPage.Click += BtnNextPage_Click;
            if (comboBoxPageSize != null)
                this.comboBoxPageSize.SelectedIndexChanged += ComboBoxPageSize_SelectedIndexChanged;
        }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        private async void OutboundManagement_Load(object sender, EventArgs e)
        {
            InitializeGridView();
            InitializeStaticData();
            await LoadOutboundData();
        }

        /// <summary>
        /// 初始化表格视图
        /// </summary>
        private void InitializeGridView()
        {
            try
            {
                if (gridView1 != null)
                {
                    // 设置表头字体和背景色
                    gridView1.Appearance.HeaderPanel.BackColor = System.Drawing.Color.FromArgb(221, 235, 247);
                    gridView1.Appearance.HeaderPanel.Options.UseBackColor = true;
                    gridView1.Appearance.HeaderPanel.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
                    gridView1.Appearance.HeaderPanel.Options.UseFont = true;
                    gridView1.OptionsView.ShowGroupPanel = false;
                    gridView1.OptionsView.ShowIndicator = false;

                    // 设置选择模式
                    gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
                    gridView1.OptionsSelection.EnableAppearanceFocusedRow = true;
                    gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;

                    // 添加自定义行单元格编辑事件
                    gridView1.CustomRowCellEdit += GridView1_CustomRowCellEdit;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化表格视图失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 自定义行单元格编辑事件
        /// </summary>
        private void GridView1_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs e)
        {
            if (e.Column.FieldName == "Action")
            {
                var view = (DevExpress.XtraGrid.Views.Grid.GridView)sender;
                var row = view.GetDataRow(e.RowHandle);
                if (row == null) return;

                // 使用auditState数字状态判断按钮显示
                int auditState = 0; // 默认登记状态
                if (row.Table.Columns.Contains("auditState"))
                {
                    var auditStateValue = row["auditState"];
                    if (auditStateValue is int state)
                    {
                        auditState = state;
                    }
                    else if (auditStateValue != null)
                    {
                        int.TryParse(auditStateValue.ToString(), out auditState);
                    }
                }

                var btnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
                btnEdit.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.HideTextEditor;
                btnEdit.Buttons.Clear();

                // 添加查看按钮
                var viewButton = new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "查看" };
                viewButton.Appearance.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
                viewButton.Appearance.ForeColor = System.Drawing.Color.Black;
                viewButton.Appearance.Options.UseBackColor = true;
                viewButton.Appearance.Options.UseForeColor = true;
                btnEdit.Buttons.Add(viewButton);

                // 添加删除按钮
                var deleteButton = new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "删除" };
                deleteButton.Appearance.BackColor = System.Drawing.Color.FromArgb(231, 76, 60);
                deleteButton.Appearance.ForeColor = System.Drawing.Color.Black;
                deleteButton.Appearance.Options.UseBackColor = true;
                deleteButton.Appearance.Options.UseForeColor = true;
                btnEdit.Buttons.Add(deleteButton);

                // 根据审批状态添加审批/撤销按钮
                if (auditState == 0) // 0是登记状态，显示审批按钮
                {
                    // 登记状态显示审批按钮
                    var auditButton = new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "审批" };
                    auditButton.Appearance.BackColor = System.Drawing.Color.FromArgb(46, 204, 113);
                    auditButton.Appearance.ForeColor = System.Drawing.Color.Black;
                    auditButton.Appearance.Options.UseBackColor = true;
                    auditButton.Appearance.Options.UseForeColor = true;
                    btnEdit.Buttons.Add(auditButton);
                }
                else if (auditState == 5) // 5是审核状态，显示撤销按钮
                {
                    // 审核状态显示撤销按钮
                    var undoButton = new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph) { Caption = "撤销" };
                    undoButton.Appearance.BackColor = System.Drawing.Color.FromArgb(243, 156, 18);
                    undoButton.Appearance.ForeColor = System.Drawing.Color.Black;
                    undoButton.Appearance.Options.UseBackColor = true;
                    undoButton.Appearance.Options.UseForeColor = true;
                    btnEdit.Buttons.Add(undoButton);
                }
                // auditState == 10 (接收状态) 时不显示审批/撤销按钮

                btnEdit.ButtonClick += OnActionButtonClick;
                e.RepositoryItem = btnEdit;
            }
        }

        /// <summary>
        /// 设置列标题
        /// </summary>
        private void SetColumnCaptions()
        {
            try
            {
                if (gridView1 != null)
                {
                    // 等待列创建完成
                    gridView1.BeginUpdate();

                    // 设置各列的中文标题
                    foreach (DevExpress.XtraGrid.Columns.GridColumn column in gridView1.Columns)
                    {
                        switch (column.FieldName)
                        {
                            case "id":
                                column.Caption = "ID";
                                column.Visible = false; // 隐藏ID列
                                break;
                            case "outCode":
                                column.Caption = "出库编码";
                                break;
                            case "outWarehouseName":
                                column.Caption = "出库原因";
                                break;
                            case "applyingUnitName":
                                column.Caption = "申请单位";
                                break;
                            case "auditStateName":
                                column.Caption = "审批状态";
                                break;
                            case "auditName":
                                column.Caption = "审批人";
                                break;
                            case "auditDate":
                                column.Caption = "审批时间";
                                break;
                            case "createTime":
                                column.Caption = "创建时间";
                                break;
                            case "Action":
                                column.Caption = "操作";
                                column.Width = 200;
                                column.MinWidth = 200;
                                column.MaxWidth = 200;
                                column.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Right;
                                break;

                        }
                    }

                    gridView1.EndUpdate();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置列标题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 操作按钮点击事件
        /// </summary>
        private async void OnActionButtonClick(object sender, DevExpress.XtraEditors.Controls.ButtonPressedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"按钮点击事件触发，按钮标题: {e.Button.Caption}");

                // 获取当前行数据
                int rowHandle = gridView1.FocusedRowHandle;
                if (rowHandle < 0) return;

                var row = gridView1.GetRow(rowHandle);
                if (row == null) return;

                int outboundId = 0;
                if (row is System.Data.DataRow dataRow)
                {
                    if (dataRow.Table.Columns.Contains("id"))
                    {
                        outboundId = Convert.ToInt32(dataRow["id"]);
                    }
                }
                else if (row is System.Data.DataRowView dataRowView)
                {
                    if (dataRowView.DataView.Table.Columns.Contains("id"))
                    {
                        outboundId = Convert.ToInt32(dataRowView["id"]);
                    }
                }

                if (outboundId == 0)
                {
                    System.Windows.Forms.MessageBox.Show("无法获取出库记录ID", "错误",
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    return;
                }

                if (e.Button.Caption == "查看")
                {
                    await ShowOutboundDetail(outboundId);
                }
                else if (e.Button.Caption == "删除")
                {
                    await DeleteOutboundRecord(outboundId);
                }
                else if (e.Button.Caption == "审批")
                {
                    await AuditOutboundRecord(outboundId);
                }
                else if (e.Button.Caption == "撤销")
                {
                    await UndoAuditOutboundRecord(outboundId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"操作按钮点击异常: {ex.Message}");
                System.Windows.Forms.MessageBox.Show($"操作失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 审批出库记录
        /// </summary>
        private async Task AuditOutboundRecord(int outboundId)
        {
            try
            {
                // 确认审批
                var result = System.Windows.Forms.MessageBox.Show(
                    "确定要审批这条出库记录吗？",
                    "确认审批",
                    System.Windows.Forms.MessageBoxButtons.YesNo,
                    System.Windows.Forms.MessageBoxIcon.Question);

                if (result != System.Windows.Forms.DialogResult.Yes)
                    return;

                // 显示加载提示
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                // 调用审批API
                string url = WriteApiConfig.GetApiUrl("Wms", "AuditOut");

                using (var httpClient = new System.Net.Http.HttpClient())
                {
                    // 构建请求参数
                    var requestData = new { id = outboundId };
                    var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);
                    var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                    var response = await httpClient.PostAsync(url, content);
                    if (response.IsSuccessStatusCode)
                    {
                        string jsonResult = await response.Content.ReadAsStringAsync();
                        var resultObj = Newtonsoft.Json.Linq.JObject.Parse(jsonResult);

                        if (resultObj["code"]?.ToString() == "Success")
                        {
                            System.Windows.Forms.MessageBox.Show("审批成功！", "提示",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);

                            // 刷新数据
                            await LoadOutboundData();
                        }
                        else
                        {
                            System.Windows.Forms.MessageBox.Show($"审批失败: {resultObj["msg"]?.ToString()}", "错误",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        System.Windows.Forms.MessageBox.Show($"网络请求失败: {response.StatusCode}", "错误",
                            System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"审批失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            finally
            {
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        /// <summary>
        /// 撤销出库记录审批
        /// </summary>
        private async Task UndoAuditOutboundRecord(int outboundId)
        {
            try
            {
                // 确认撤销
                var result = System.Windows.Forms.MessageBox.Show(
                    "确定要撤销这条出库记录的审批吗？",
                    "确认撤销",
                    System.Windows.Forms.MessageBoxButtons.YesNo,
                    System.Windows.Forms.MessageBoxIcon.Question);

                if (result != System.Windows.Forms.DialogResult.Yes)
                    return;

                // 显示加载提示
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                // 调用撤销API
                string url = WriteApiConfig.GetApiUrl("Wms", "UndoAuditOut");

                using (var httpClient = new System.Net.Http.HttpClient())
                {
                    // 构建请求参数
                    var requestData = new { id = outboundId };
                    var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);
                    var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                    var response = await httpClient.PostAsync(url, content);
                    if (response.IsSuccessStatusCode)
                    {
                        string jsonResult = await response.Content.ReadAsStringAsync();
                        var resultObj = Newtonsoft.Json.Linq.JObject.Parse(jsonResult);

                        if (resultObj["code"]?.ToString() == "Success")
                        {
                            System.Windows.Forms.MessageBox.Show("撤销成功！", "提示",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);

                            // 刷新数据
                            await LoadOutboundData();
                        }
                        else
                        {
                            System.Windows.Forms.MessageBox.Show($"撤销失败: {resultObj["msg"]?.ToString()}", "错误",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        System.Windows.Forms.MessageBox.Show($"网络请求失败: {response.StatusCode}", "错误",
                            System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"撤销失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            finally
            {
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        /// <summary>
        /// 删除出库记录
        /// </summary>
        private async Task DeleteOutboundRecord(int outboundId)
        {
            try
            {
                // 确认删除
                var result = System.Windows.Forms.MessageBox.Show(
                    "确定要删除这条出库记录吗？此操作不可恢复。",
                    "确认删除",
                    System.Windows.Forms.MessageBoxButtons.YesNo,
                    System.Windows.Forms.MessageBoxIcon.Question);

                if (result != System.Windows.Forms.DialogResult.Yes)
                    return;

                // 显示加载提示
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                // 调用删除API
                string url = WriteApiConfig.GetApiUrl("Wms", "DeleteOut");

                using (var httpClient = new System.Net.Http.HttpClient())
                {
                    // 构建请求参数
                    var requestData = new { id = outboundId };
                    var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(requestData);
                    var content = new System.Net.Http.StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                    var response = await httpClient.PostAsync(url, content);
                    if (response.IsSuccessStatusCode)
                    {
                        string jsonResult = await response.Content.ReadAsStringAsync();
                        var resultObj = Newtonsoft.Json.Linq.JObject.Parse(jsonResult);

                        if (resultObj["code"]?.ToString() == "Success")
                        {
                            System.Windows.Forms.MessageBox.Show("删除成功！", "提示",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);

                            // 刷新数据
                            await LoadOutboundData();
                        }
                        else
                        {
                            System.Windows.Forms.MessageBox.Show($"删除失败: {resultObj["msg"]?.ToString()}", "错误",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        System.Windows.Forms.MessageBox.Show($"网络请求失败: {response.StatusCode}", "错误",
                            System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"删除失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            finally
            {
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        /// <summary>
        /// 显示出库详情
        /// </summary>
        private async Task ShowOutboundDetail(int outboundId)
        {
            try
            {
                // 显示加载提示
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.WaitCursor;

                // 调用API获取详情
                string url = ReadApiConfig.GetApiUrl("Wms", "GetOutCauseDetailById") + $"?id={outboundId}";

                using (var httpClient = new System.Net.Http.HttpClient())
                {
                    var response = await httpClient.GetAsync(url);
                    if (response.IsSuccessStatusCode)
                    {
                        string jsonResult = await response.Content.ReadAsStringAsync();
                        var result = Newtonsoft.Json.Linq.JObject.Parse(jsonResult);

                        if (result["code"]?.ToString() == "200" && result["data"] != null)
                        {
                            var data = (Newtonsoft.Json.Linq.JObject)result["data"];
                            ShowDetailDialog(data);
                        }
                        else
                        {
                            System.Windows.Forms.MessageBox.Show($"获取详情失败: {result["msg"]?.ToString()}", "错误",
                                System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        System.Windows.Forms.MessageBox.Show($"网络请求失败: {response.StatusCode}", "错误",
                            System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"加载详情失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
            finally
            {
                System.Windows.Forms.Cursor.Current = System.Windows.Forms.Cursors.Default;
            }
        }

        /// <summary>
        /// 显示详情对话框
        /// </summary>
        private void ShowDetailDialog(Newtonsoft.Json.Linq.JObject data)
        {
            try
            {
                var detailMessage = new System.Text.StringBuilder();
                detailMessage.AppendLine($"出库编码：{data["outCode"]?.ToString() ?? ""}");
                detailMessage.AppendLine($"出库仓库：{data["outWarehouse"]?.ToString() ?? ""}");
                detailMessage.AppendLine($"出库原因：{data["outReason"]?.ToString() ?? ""}");
                detailMessage.AppendLine($"审批状态：{data["auditState"]?.ToString() ?? ""}");
                detailMessage.AppendLine($"审批人：{data["auditName"]?.ToString() ?? ""}");

                // 处理日期格式
                if (data["auditDate"] != null && !string.IsNullOrEmpty(data["auditDate"].ToString()))
                {
                    DateTime auditDate = DateTime.Parse(data["auditDate"].ToString());
                    detailMessage.AppendLine($"审批时间：{auditDate:yyyy-MM-dd HH:mm:ss}");
                }
                else
                {
                    detailMessage.AppendLine("审批时间：未审批");
                }

                if (data["createTime"] != null && !string.IsNullOrEmpty(data["createTime"].ToString()))
                {
                    DateTime createTime = DateTime.Parse(data["createTime"].ToString());
                    detailMessage.AppendLine($"创建时间：{createTime:yyyy-MM-dd HH:mm:ss}");
                }
                else
                {
                    detailMessage.AppendLine("创建时间：未知");
                }

                // 添加明细信息
                var details = data["details"];
                if (details != null && details.HasValues)
                {
                    detailMessage.AppendLine();
                    detailMessage.AppendLine("=== 出库明细 ===");
                    int itemIndex = 1;
                    foreach (var item in details)
                    {
                        detailMessage.AppendLine($"明细 {itemIndex}：");
                        detailMessage.AppendLine($"  物料类型：{item["materialTypeNmae"]?.ToString() ?? ""}");
                        detailMessage.AppendLine($"  物料编码：{item["materialCode"]?.ToString() ?? ""}");
                        detailMessage.AppendLine($"  物料名称：{item["materialName"]?.ToString() ?? ""}");
                        detailMessage.AppendLine($"  出库数量：{item["outNum"]?.ToString() ?? ""}");
                        detailMessage.AppendLine();
                        itemIndex++;
                    }
                }

                System.Windows.Forms.MessageBox.Show(detailMessage.ToString(), "出库详情",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"显示详情失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化静态数据
        /// </summary>
        private void InitializeStaticData()
        {
            try
            {
                // 初始化分页大小选择
                if (comboBoxPageSize != null)
                {
                    comboBoxPageSize.Properties.Items.Clear();
                    comboBoxPageSize.Properties.Items.AddRange(new object[] { "10", "20", "50", "100" });
                    comboBoxPageSize.SelectedIndex = 0;
                    comboBoxPageSize.Visible = true; // 确保可见
                    InitializePaginationStyles();
                }

                // 确保分页控件可见
                if (btnPrevPage != null)
                    btnPrevPage.Visible = true;
                if (btnNextPage != null)
                    btnNextPage.Visible = true;
                if (lblPageInfo != null)
                    lblPageInfo.Visible = true;
                if (labelPageSize != null)
                    labelPageSize.Visible = true;

                // 初始化日期控件为空
                if (dateEditStart != null)
                    dateEditStart.EditValue = null;
                if (dateEditEnd != null)
                    dateEditEnd.EditValue = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化静态数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化分页控件样式
        /// </summary>
        private void InitializePaginationStyles()
        {
            try
            {
                // 设置分页按钮样式
                if (btnPrevPage != null)
                {
                    btnPrevPage.FlatStyle = FlatStyle.Flat;
                    btnPrevPage.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
                    btnPrevPage.ForeColor = System.Drawing.Color.White;
                    btnPrevPage.Font = new System.Drawing.Font("微软雅黑", 9.5F, FontStyle.Regular);
                    btnPrevPage.Cursor = Cursors.Hand;
                    btnPrevPage.FlatAppearance.BorderSize = 0;
                    btnPrevPage.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(41, 128, 185);
                    btnPrevPage.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(31, 97, 141);
                }

                if (btnNextPage != null)
                {
                    btnNextPage.FlatStyle = FlatStyle.Flat;
                    btnNextPage.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
                    btnNextPage.ForeColor = System.Drawing.Color.White;
                    btnNextPage.Font = new System.Drawing.Font("微软雅黑", 9.5F, FontStyle.Regular);
                    btnNextPage.Cursor = Cursors.Hand;
                    btnNextPage.FlatAppearance.BorderSize = 0;
                    btnNextPage.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(41, 128, 185);
                    btnNextPage.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(31, 97, 141);
                }

                // 设置分页信息标签样式
                if (lblPageInfo != null)
                {
                    lblPageInfo.Font = new System.Drawing.Font("微软雅黑", 9F, FontStyle.Regular);
                    lblPageInfo.ForeColor = System.Drawing.Color.FromArgb(52, 73, 94);
                    lblPageInfo.BackColor = System.Drawing.Color.Transparent;
                }

                // 设置每页条数标签样式
                if (labelPageSize != null)
                {
                    labelPageSize.Font = new System.Drawing.Font("微软雅黑", 9F, FontStyle.Regular);
                    labelPageSize.ForeColor = System.Drawing.Color.FromArgb(52, 73, 94);
                    labelPageSize.BackColor = System.Drawing.Color.Transparent;
                }

                // 设置分页大小选择框样式
                if (comboBoxPageSize != null)
                {
                    comboBoxPageSize.Properties.Appearance.Font = new System.Drawing.Font("微软雅黑", 9F, FontStyle.Regular);
                    comboBoxPageSize.Properties.Appearance.ForeColor = System.Drawing.Color.FromArgb(52, 73, 94);
                    comboBoxPageSize.Properties.Appearance.BackColor = System.Drawing.Color.White;
                    comboBoxPageSize.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
                    comboBoxPageSize.Properties.Appearance.BorderColor = System.Drawing.Color.FromArgb(189, 195, 199);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化分页样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新按钮样式
        /// </summary>
        private void UpdateButtonStyle(Button button, bool enabled)
        {
            try
            {
                if (enabled)
                {
                    button.BackColor = System.Drawing.Color.FromArgb(52, 152, 219);
                    button.ForeColor = System.Drawing.Color.White;
                    button.Cursor = Cursors.Hand;
                }
                else
                {
                    button.BackColor = System.Drawing.Color.FromArgb(189, 195, 199);
                    button.ForeColor = System.Drawing.Color.FromArgb(127, 140, 141);
                    button.Cursor = Cursors.Default;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新按钮样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载出库数据
        /// </summary>
        private async Task LoadOutboundData()
        {
            try
            {
                string url = ReadApiConfig.GetApiUrl("Wms", "GetOutCauseByPag");
                var paramList = new List<string>();

                // 判断日期控件是否有值
                if (dateEditStart != null && dateEditStart.EditValue != null && dateEditStart.EditValue != DBNull.Value)
                {
                    paramList.Add($"StartTime={Uri.EscapeDataString(((DateTime)dateEditStart.EditValue).ToString("yyyy-MM-dd"))}");
                }
                if (dateEditEnd != null && dateEditEnd.EditValue != null && dateEditEnd.EditValue != DBNull.Value)
                {
                    paramList.Add($"EndTime={Uri.EscapeDataString(((DateTime)dateEditEnd.EditValue).ToString("yyyy-MM-dd"))}");
                }

                // 分页参数
                paramList.Add($"PageIndex={_pageIndex}");
                paramList.Add($"PageSize={_pageSize}");

                if (paramList.Count > 0)
                {
                    url += (url.Contains("?") ? "&" : "?") + string.Join("&", paramList);
                }

                var client = new HttpClient();
                var response = await client.GetStringAsync(url);

                if (string.IsNullOrWhiteSpace(response) || !response.TrimStart().StartsWith("{"))
                {
                    MessageBox.Show("接口返回内容不是有效的JSON: " + response);
                    if (gridControl1 != null)
                        gridControl1.DataSource = null;
                    return;
                }

                var jobj = Newtonsoft.Json.Linq.JObject.Parse(response);
                if (jobj["code"]?.ToObject<int>() == 200)
                {
                    var pageData = jobj["data"]?["pageData"]?.ToObject<List<Newtonsoft.Json.Linq.JObject>>();
                    _totalCount = jobj["data"]?["totalCount"]?.ToObject<int>() ?? 0;
                    _totalPage = (_totalCount + _pageSize - 1) / _pageSize;
                    if (_totalPage == 0) _totalPage = 1;

                    // 更新分页信息
                    if (lblPageInfo != null)
                        lblPageInfo.Text = $"第{_pageIndex}页/共{_totalPage}页";

                    // 更新分页按钮状态
                    if (btnPrevPage != null)
                    {
                        btnPrevPage.Enabled = _pageIndex > 1;
                        UpdateButtonStyle(btnPrevPage, _pageIndex > 1);
                    }
                    if (btnNextPage != null)
                    {
                        btnNextPage.Enabled = _pageIndex < _totalPage;
                        UpdateButtonStyle(btnNextPage, _pageIndex < _totalPage);
                    }

                    if (pageData != null)
                    {
                        var table = new DataTable();
                        table.Columns.Add("id", typeof(string));
                        table.Columns.Add("outCode", typeof(string));
                        table.Columns.Add("outWarehouseName", typeof(string));
                        table.Columns.Add("applyingUnitName", typeof(string));
                        table.Columns.Add("auditStateName", typeof(string));
                        table.Columns.Add("auditState", typeof(int)); // 添加auditState数字状态列
                        table.Columns.Add("auditName", typeof(string));
                        table.Columns.Add("auditDate", typeof(string));
                        table.Columns.Add("createTime", typeof(string));
                        table.Columns.Add("Action", typeof(string)); // 添加操作列

                        foreach (var item in pageData)
                        {
                            // 处理auditState数字状态
                            int auditState = 0; // 默认登记状态
                            if (item["auditState"] != null)
                            {
                                if (int.TryParse(item["auditState"].ToString(), out int state))
                                {
                                    auditState = state;
                                }
                            }

                            table.Rows.Add(
                                item["id"]?.ToString() ?? "",
                                item["outCode"]?.ToString() ?? "",
                                item["outWarehouseName"]?.ToString() ?? "",
                                item["applyingUnitName"]?.ToString() ?? "",
                                item["auditStateName"]?.ToString() ?? "",
                                auditState, // 使用数字状态
                                item["auditName"]?.ToString() ?? "",
                                item["auditDate"]?.ToString() ?? "",
                                item["createTime"]?.ToString() ?? "",
                                "" // 操作列留空，让按钮编辑器处理显示
                            );
                        }

                        if (gridControl1 != null)
                        {
                            gridControl1.DataSource = table;
                            // 数据绑定后设置列标题
                            SetColumnCaptions();
                        }
                    }
                    else
                    {
                        if (gridControl1 != null)
                        {
                            // 创建空的DataTable，包含所有列
                            var emptyTable = new DataTable();
                            emptyTable.Columns.Add("id", typeof(string));
                            emptyTable.Columns.Add("outCode", typeof(string));
                            emptyTable.Columns.Add("outWarehouseName", typeof(string));
                            emptyTable.Columns.Add("applyingUnitName", typeof(string));
                            emptyTable.Columns.Add("auditStateName", typeof(string));
                            emptyTable.Columns.Add("auditState", typeof(int)); // 添加auditState数字状态列
                            emptyTable.Columns.Add("auditName", typeof(string));
                            emptyTable.Columns.Add("auditDate", typeof(string));
                            emptyTable.Columns.Add("createTime", typeof(string));
                            emptyTable.Columns.Add("Action", typeof(string));

                            gridControl1.DataSource = emptyTable;
                            SetColumnCaptions();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("接口返回错误: " + (jobj["msg"]?.ToString() ?? "未知错误"));
                    if (gridControl1 != null)
                        gridControl1.DataSource = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("获取出库数据失败: " + ex.Message);
                if (gridControl1 != null)
                    gridControl1.DataSource = null;
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            _pageIndex = 1;
            await LoadOutboundData();
        }

        /// <summary>
        /// 上一页
        /// </summary>
        private async void BtnPrevPage_Click(object sender, EventArgs e)
        {
            if (_pageIndex > 1)
            {
                _pageIndex--;
                await LoadOutboundData();
            }
        }

        /// <summary>
        /// 下一页
        /// </summary>
        private async void BtnNextPage_Click(object sender, EventArgs e)
        {
            if (_pageIndex < _totalPage)
            {
                _pageIndex++;
                await LoadOutboundData();
            }
        }

        /// <summary>
        /// 分页大小改变
        /// </summary>
        private async void ComboBoxPageSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (int.TryParse(comboBoxPageSize.SelectedItem?.ToString(), out int size))
            {
                _pageSize = size;
                _pageIndex = 1;
                await LoadOutboundData();
            }
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            ShowShelfInfoDialog();
        }

        /// <summary>
        /// 显示货架信息对话框
        /// </summary>
        private async void ShowShelfInfoDialog()
        {
            var form = new Form();
            form.Text = "货架信息";
            form.FormBorderStyle = FormBorderStyle.Sizable;
            form.StartPosition = FormStartPosition.CenterParent;
            form.Size = new System.Drawing.Size(1500, 700);
            form.MaximizeBox = true;
            form.MinimizeBox = false;

            // 选择出库物品的数据结构
            var selectedList = new List<SelectedOutItem>();

            // 创建主布局
            var mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Padding = new Padding(10);

            // 左侧货架列表面板
            var leftPanel = new Panel();
            leftPanel.Width = 200;
            leftPanel.Dock = DockStyle.Left;
            leftPanel.BorderStyle = BorderStyle.FixedSingle;
            leftPanel.BackColor = Color.White;

            var shelfInfoLabel = new Label();
            shelfInfoLabel.Text = "货架信息";
            shelfInfoLabel.Font = new Font("微软雅黑", 12, FontStyle.Bold);
            shelfInfoLabel.Location = new Point(20, 20);
            shelfInfoLabel.AutoSize = true;

            var listBoxShelf = new ListBox();
            listBoxShelf.Location = new Point(20, 60);
            listBoxShelf.Size = new Size(160, 400);
            listBoxShelf.Font = new Font("微软雅黑", 10);
            listBoxShelf.BorderStyle = BorderStyle.None;
            leftPanel.Controls.Add(shelfInfoLabel);
            leftPanel.Controls.Add(listBoxShelf);

            // 右侧主Panel（包含格子和表格）
            var rightMainPanel = new Panel();
            rightMainPanel.Dock = DockStyle.Fill;
            rightMainPanel.BackColor = Color.White;

            // 右侧货架格子面板
            var rightPanel = new Panel();
            rightPanel.Dock = DockStyle.Left;
            rightPanel.Width = 1000;
            rightPanel.BackColor = Color.White;

            var tableLayoutPanelSlots = new TableLayoutPanel();
            tableLayoutPanelSlots.Dock = DockStyle.Fill;
            tableLayoutPanelSlots.ColumnCount = 5;
            tableLayoutPanelSlots.RowCount = 3;
            tableLayoutPanelSlots.ColumnStyles.Clear();
            tableLayoutPanelSlots.RowStyles.Clear();
            for (int i = 0; i < 5; i++)
                tableLayoutPanelSlots.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F));
            for (int i = 0; i < 3; i++)
                tableLayoutPanelSlots.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            for (int row = 1; row <= 3; row++)
            {
                for (int col = 1; col <= 5; col++)
                {
                    var cellPanel = new Panel();
                    cellPanel.BorderStyle = BorderStyle.FixedSingle;
                    cellPanel.BackColor = Color.White;
                    cellPanel.Margin = new Padding(2);
                    var slotLabel = new Label();
                    slotLabel.Text = $"{row}-{col}";
                    slotLabel.Font = new Font("微软雅黑", 9);
                    slotLabel.ForeColor = Color.Blue;
                    slotLabel.Location = new Point(5, 5);
                    slotLabel.AutoSize = true;
                    slotLabel.Tag = "slotLabel";
                    cellPanel.Controls.Add(slotLabel);
                    tableLayoutPanelSlots.Controls.Add(cellPanel, col - 1, row - 1);
                }
            }
            rightPanel.Controls.Add(tableLayoutPanelSlots);

            // 右侧DataGridView表格上方下拉区
            var topPanel = new Panel();
            topPanel.Dock = DockStyle.Top;
            topPanel.Height = 56;
            topPanel.BackColor = Color.White;

            var layout = new TableLayoutPanel();
            layout.Dock = DockStyle.Fill;
            layout.ColumnCount = 6;
            layout.RowCount = 1;
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // label1
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150)); // cmb1
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // label2
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 120)); // cmb2
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // label3
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150)); // cmb3
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            var lblWarehouse = new Label { Text = "库房：", AutoSize = true, Anchor = AnchorStyles.Right, Font = new Font("微软雅黑", 10, FontStyle.Bold), Margin = new Padding(10, 0, 0, 0) };
            var cmbWarehouse = new System.Windows.Forms.ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Width = 140, Anchor = AnchorStyles.Left, Font = new Font("微软雅黑", 10), Margin = new Padding(0, 8, 20, 8) };
            cmbWarehouse.Items.Add(new ComboBoxItem("消毒供应室", 0));
            cmbWarehouse.Items.Add(new ComboBoxItem("手术供应室", 5));
            cmbWarehouse.Items.Add(new ComboBoxItem("清洁供应室", 10));
            cmbWarehouse.SelectedIndex = 0;

            var lblUnit = new Label { Text = "申请单位：", AutoSize = true, Anchor = AnchorStyles.Right, Font = new Font("微软雅黑", 10, FontStyle.Bold), Margin = new Padding(10, 0, 0, 0) };
            var cmbUnit = new System.Windows.Forms.ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Width = 110, Anchor = AnchorStyles.Left, Font = new Font("微软雅黑", 10), Margin = new Padding(0, 8, 20, 8) };
            cmbUnit.Items.Add(new ComboBoxItem("内科", 0));
            cmbUnit.Items.Add(new ComboBoxItem("手术室", 5));
            cmbUnit.SelectedIndex = 0;

            var lblReason = new Label { Text = "出库原因：", AutoSize = true, Anchor = AnchorStyles.Right, Font = new Font("微软雅黑", 10, FontStyle.Bold), Margin = new Padding(10, 0, 0, 0) };
            var cmbReason = new System.Windows.Forms.ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Width = 140, Anchor = AnchorStyles.Left, Font = new Font("微软雅黑", 10), Margin = new Padding(0, 8, 0, 8) };
            cmbReason.Items.Add(new ComboBoxItem("领用出库", 0));
            cmbReason.Items.Add(new ComboBoxItem("失效出库", 5));
            cmbReason.Items.Add(new ComboBoxItem("调拨出库", 10));
            cmbReason.SelectedIndex = 0;

            layout.Controls.Add(lblWarehouse, 0, 0);
            layout.Controls.Add(cmbWarehouse, 1, 0);
            layout.Controls.Add(lblUnit, 2, 0);
            layout.Controls.Add(cmbUnit, 3, 0);
            layout.Controls.Add(lblReason, 4, 0);
            layout.Controls.Add(cmbReason, 5, 0);

            topPanel.Controls.Clear();
            topPanel.Controls.Add(layout);

            // 右侧DataGridView表格
            var dgv = new DataGridView();
            dgv.Dock = DockStyle.Fill;
            dgv.Width = 450;
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgv.Columns.Add("shelfName", "货架");
            dgv.Columns.Add("materialTypeName", "物品类型");
            dgv.Columns.Add("materialName", "物品名称");
            // 删除按钮列
            var delCol = new DataGridViewButtonColumn();
            delCol.Name = "deleteCol";
            delCol.HeaderText = "操作";
            delCol.Text = "删除";
            delCol.UseColumnTextForButtonValue = true;
            dgv.Columns.Add(delCol);
            // 删除格子列、存放数量列

            // 右侧表格Panel
            var rightTablePanel = new Panel();
            rightTablePanel.Dock = DockStyle.Fill;
            rightTablePanel.Width = 450;
            rightTablePanel.Controls.Add(dgv);
            rightTablePanel.Controls.Add(topPanel);

            rightMainPanel.Controls.Clear();
            rightMainPanel.Controls.Add(rightTablePanel); // Dock.Fill
            rightMainPanel.Controls.Add(rightPanel);     // Dock.Left

            mainPanel.Controls.Clear();
            mainPanel.Controls.Add(rightMainPanel); // Dock.Fill
            mainPanel.Controls.Add(leftPanel);      // Dock.Left

            form.Controls.Clear();
            form.Controls.Add(mainPanel);

            // 异步加载货架列表并默认选中第一个
            form.Load += async (s, e) =>
            {
                var shelfList = await GetShelfListAsync();
                listBoxShelf.Items.Clear();
                foreach (var shelf in shelfList)
                {
                    listBoxShelf.Items.Add(new ShelfListBoxItem { Id = shelf.id, Name = shelf.shelfName });
                }
                if (listBoxShelf.Items.Count > 0)
                    listBoxShelf.SelectedIndex = 0;
            };

            // 货架切换事件
            listBoxShelf.SelectedIndexChanged += async (s, e) =>
            {
                if (listBoxShelf.SelectedItem is ShelfListBoxItem shelfItem)
                {
                    await LoadAndShowShelfSlots(tableLayoutPanelSlots, shelfItem.Id, shelfItem.Name, dgv, selectedList);
                }
            };

            // DataGridView删除按钮事件
            // 只允许点击删除按钮时移除该行
            // 放在form.ShowDialog()前
            dgv.CellContentClick += (s, e) =>
            {
                if (e.RowIndex >= 0 && e.ColumnIndex == dgv.Columns["deleteCol"].Index)
                {
                    if (e.RowIndex < dgv.Rows.Count)
                    {
                        var row = dgv.Rows[e.RowIndex];
                        string shelfName = row.Cells["shelfName"].Value?.ToString();
                        string materialName = row.Cells["materialName"].Value?.ToString();
                        // 移除selectedList中对应项
                        var toRemove = selectedList.FirstOrDefault(x => x.ShelfName == shelfName && x.MaterialName == materialName);
                        if (toRemove != null)
                        {
                            selectedList.Remove(toRemove);
                            dgv.Rows.RemoveAt(e.RowIndex);
                        }
                    }
                }
            };

            form.ShowDialog();
        }

        // 货架列表项
        private class ShelfListBoxItem
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public override string ToString() => Name;
        }

        // 获取货架列表（与存放页面一致）
        private async Task<List<ShelfInfo>> GetShelfListAsync()
        {
            var shelfList = new List<ShelfInfo>();
            string url = ReadApiConfig.GetApiUrl("Common", "GetShelvesList");
            using (var client = new HttpClient())
            {
                var result = await client.GetStringAsync(url);
                if (!string.IsNullOrEmpty(result))
                {
                    try
                    {
                        shelfList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ShelfInfo>>(result);
                    }
                    catch
                    {
                        var jobj = JObject.Parse(result);
                        if (jobj["code"]?.ToObject<int>() == 200)
                        {
                            shelfList = jobj["data"]?.ToObject<List<ShelfInfo>>();
                        }
                    }
                }
            }
            return shelfList;
        }

        // 加载并显示货架格子内容
        private async Task LoadAndShowShelfSlots(TableLayoutPanel panel, int shelfId, string shelfName, DataGridView dgv, List<SelectedOutItem> selectedList)
        {
            string url = ReadApiConfig.GetApiUrl("Wms", "GetStoresList") + $"?ShelfId={shelfId}";
            using (var client = new HttpClient())
            {
                var result = await client.GetStringAsync(url);
                if (!string.IsNullOrEmpty(result))
                {
                    var jobj = JObject.Parse(result);
                    var dataArr = jobj["data"] as JArray;
                    if (dataArr != null)
                    {
                        var gridList = dataArr.ToObject<List<StoreGridInfo>>();
                        int maxRow = gridList.Count > 0 ? gridList.Max(g => g.floorNum) : 1; 
                        int maxCol = gridList.Count > 0 ? gridList.Max(g => g.latticeNum) : 1;
                        panel.Controls.Clear();
                        panel.RowCount = maxRow;
                        panel.ColumnCount = maxCol;
                        panel.RowStyles.Clear();
                        panel.ColumnStyles.Clear();
                        for (int i = 0; i < maxCol; i++)
                            panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F / maxCol));
                        for (int i = 0; i < maxRow; i++)
                            panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F / maxRow));
                        for (int row = 1; row <= maxRow; row++)
                        {
                            for (int col = 1; col <= maxCol; col++)
                            {
                                var cellPanel = new Panel();
                                cellPanel.BorderStyle = BorderStyle.FixedSingle;
                                cellPanel.BackColor = Color.White;
                                cellPanel.Margin = new Padding(2);
                                var slotLabel = new Label();
                                slotLabel.Text = $"{row}-{col}";
                                slotLabel.Font = new Font("微软雅黑", 9);
                                slotLabel.ForeColor = Color.Blue;
                                slotLabel.Location = new Point(5, 5);
                                slotLabel.AutoSize = true;
                                slotLabel.Tag = "slotLabel";
                                cellPanel.Controls.Add(slotLabel);
                                // 查找该格子是否有物品
                                var slot = gridList.FirstOrDefault(g => g.floorNum == row && g.latticeNum == col && !string.IsNullOrWhiteSpace(g.materialName) && g.storesNum > 0);
                                if (slot != null)
                                {
                                    var contentLabel = new Label
                                    {
                                        Font = new Font("微软雅黑", 8),
                                        ForeColor = Color.Black,
                                        Location = new Point(5, 25),
                                        Size = new Size(150, 40),
                                        AutoSize = false,
                                        Text = $"{slot.materialTypeName} {slot.materialName} x{slot.storesNum}"
                                    };
                                    cellPanel.Controls.Add(contentLabel);
                                    // 点击事件：选择出库数量
                                    cellPanel.Cursor = Cursors.Hand;
                                    cellPanel.Click += (s, e) =>
                                    {
                                        // 检查是否已选，已选则禁止再次选择
                                        var exist = selectedList.FirstOrDefault(x => x.ShelfName == shelfName && x.Slot == slotLabel.Text && x.MaterialName == slot.materialName);
                                        if (exist != null)
                                        {
                                            MessageBox.Show("该格子已选择出库数量，不能重复选择！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                            return;
                                        }
                                        int maxCanSelect = slot.storesNum;
                                        // 自定义输入对话框
                                        using (var inputForm = new Form())
                                        {
                                            inputForm.Text = "选择出库数量";
                                            inputForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                                            inputForm.StartPosition = FormStartPosition.CenterParent;
                                            inputForm.Size = new Size(400, 200);
                                            inputForm.MaximizeBox = false;
                                            inputForm.MinimizeBox = false;
                                            var label = new Label { Text = $"请输入出库数量 (最大{maxCanSelect})", Location = new Point(30, 30), AutoSize = true, Font = new Font("微软雅黑", 12) };
                                            var textBox = new TextBox { Location = new Point(30, 70), Width = 320, Font = new Font("微软雅黑", 12) };
                                            var btnOK = new Button { Text = "确定", Location = new Point(80, 130), Size = new Size(80, 32), DialogResult = DialogResult.OK, Font = new Font("微软雅黑", 10) };
                                            var btnCancel = new Button { Text = "取消", Location = new Point(220, 130), Size = new Size(80, 32), DialogResult = DialogResult.Cancel, Font = new Font("微软雅黑", 10) };
                                            inputForm.Controls.Add(label);
                                            inputForm.Controls.Add(textBox);
                                            inputForm.Controls.Add(btnOK);
                                            inputForm.Controls.Add(btnCancel);
                                            inputForm.AcceptButton = btnOK;
                                            inputForm.CancelButton = btnCancel;
                                            if (inputForm.ShowDialog() == DialogResult.OK)
                                            {
                                                if (int.TryParse(textBox.Text, out int outNum) && outNum > 0 && outNum <= maxCanSelect)
                                                {
                                                    selectedList.Add(new SelectedOutItem
                                                    {
                                                        ShelfName = shelfName,
                                                        Slot = slotLabel.Text,
                                                        MaterialTypeName = slot.materialTypeName,
                                                        MaterialName = slot.materialName,
                                                        StoresNum = slot.storesNum,
                                                        OutNum = outNum
                                                    });
                                                    // 刷新表格
                                                    dgv.Rows.Clear();
                                                    foreach (var item in selectedList)
                                                    {
                                                        dgv.Rows.Add(item.ShelfName, item.MaterialTypeName, item.MaterialName);
                                                    }
                                                }
                                                else
                                                {
                                                    MessageBox.Show($"请输入1~{maxCanSelect}之间的整数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                                }
                                            }
                                        }
                                    };
                                }
                                panel.Controls.Add(cellPanel, col - 1, row - 1);
                            }
                        }
                    }
                }
            }
        }

        // 货架信息结构
        private class ShelfInfo
        {
            public int id { get; set; }
            public string shelfName { get; set; }
        }
        private class StoreGridInfo
        {
            public int id { get; set; }
            public int floorNum { get; set; }
            public int latticeNum { get; set; }
            public string materialTypeName { get; set; }
            public string materialName { get; set; }
            public int storesNum { get; set; }
        }

        // 选择出库物品结构
        private class SelectedOutItem
        {
            public string ShelfName { get; set; }
            public string Slot { get; set; }
            public string MaterialTypeName { get; set; }
            public string MaterialName { get; set; }
            public int StoresNum { get; set; }
            public int OutNum { get; set; }
        }

        // ComboBoxItem类
        private class ComboBoxItem
        {
            public string Text { get; set; }
            public int Value { get; set; }
            public ComboBoxItem(string text, int value) { Text = text; Value = value; }
            public override string ToString() => Text;
        }
    }
}
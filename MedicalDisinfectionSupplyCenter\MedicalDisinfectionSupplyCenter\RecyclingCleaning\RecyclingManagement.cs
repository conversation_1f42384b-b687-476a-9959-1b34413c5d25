using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Formatting;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    // 添加包数据模型类
    public class Package
    {
        public string Id { get; set; }
        public string PackageName { get; set; }
        public string Barcode { get; set; }
        public string PackageType { get; set; }
        public string Property { get; set; }
        public string ApplyUnit { get; set; }
    }

    // API响应模型类
    public class ApiResponse
    {
        public int code { get; set; }
        public string msg { get; set; }
        public Newtonsoft.Json.Linq.JToken data { get; set; }
    }

    public partial class RecyclingManagement : UserControl
    {
        // 添加API基础URL配置
        //private string ApiBaseUrl = "http://localhost:5172"; // 默认值，可以从配置文件中读取
        private string ApiBaseUrl = "http://***********:4050"; // 默认值，可以从配置文件中读取

        //private string ApiWriteUrl = "http://localhost:5192"; // 默认值，可以从配置文件中写
        private string ApiWriteUrl = "http://***********:4060"; // 默认值，可以从配置文件中写
        public RecyclingManagement()
        {
            InitializeComponent();
        }

        // 检查API连接是否可用
        private async Task<bool> CheckApiConnection(string url)
        {
            try
            {
                string result = await HttpClientHelper.ClientAsync("GET", url, false);
                return !string.IsNullOrEmpty(result);
            }
            catch
            {
                // 忽略异常，表示连接失败
                return false;
            }
        }

        private void RecyclingManagement_Load(object sender, EventArgs e)
        {
            // 初始化数据
            LoadData();
            
            // 尝试从API加载待回收列表
            LoadPendingRecyclingItemsFromApi();
            
            // 尝试从API加载已回收列表
            LoadRecoveredItemsFromApi();

            // 绑定按钮事件
            searchButton.Click += searchButton_Click;
            refreshButton.Click += refreshButton_Click;

            // 设置gridView1的多选模式
            gridView1.OptionsSelection.MultiSelect = true;
            gridView1.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
            gridView1.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.True;

            // 添加操作列点击事件
            gridView1.CustomUnboundColumnData += GridView1_CustomUnboundColumnData;
            gridView1.RowCellClick += GridView1_RowCellClick;
            gridView2.RowCellClick += GridView2_RowCellClick;
        }

        // 从API加载待回收物品数据的方法
        private async void LoadPendingRecyclingItemsFromApi()
        {
            try
            {
                // 使用API获取待回收列表数据
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetItemInfo";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 尝试解析API响应结构
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                    // 如果响应成功且包含数据
                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 创建待回收列表数据源
                        DataTable pendingTable = new DataTable();
                        pendingTable.Columns.Add("Id", typeof(int));
                        pendingTable.Columns.Add("PackageName", typeof(string));
                        pendingTable.Columns.Add("Barcode", typeof(string));
                        pendingTable.Columns.Add("PackageType", typeof(string));
                        pendingTable.Columns.Add("Property", typeof(string));
                        pendingTable.Columns.Add("ApplyUnit", typeof(string));
                        pendingTable.Columns.Add("ApplyTime", typeof(DateTime));
                        pendingTable.Columns.Add("Operation", typeof(string)); // 添加操作列

                        // 从API响应中填充数据
                        foreach (var item in apiResponse.data)
                        {
                            pendingTable.Rows.Add(
                                int.TryParse(item["id"]?.ToString(), out int id) ? id : 0,
                                item["itemName"]?.ToString(),
                                item["itemCode"]?.ToString(),
                                item["quantity"]?.ToString(),
                                item["itemattribute"]?.ToString(),
                                item["urgent"]?.ToString(),
                                DateTime.TryParse(item["applyTime"]?.ToString(), out DateTime dt) ? dt : DateTime.Now,
                                "回收"
                            );
                        }

                        // 绑定数据到GridControl
                        gridControl1.DataSource = pendingTable;

                        // 设置列标题和格式
                        SetupPendingGridViewColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API调用失败: {ex.Message}");
                // 出错时，保持默认数据，不用显示错误信息打扰用户
            }
        }

        // 从API加载已回收物品数据的方法
        private async void LoadRecoveredItemsFromApi()
        {
            try
            {
                // 使用API获取已回收列表数据
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetRecoveredFinsh";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 尝试解析API响应结构
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                    // 如果响应成功且包含数据
                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 创建已回收列表数据源
                        DataTable recycledTable = new DataTable();
                        recycledTable.Columns.Add("Id", typeof(int));
                        recycledTable.Columns.Add("PackageName", typeof(string));
                        recycledTable.Columns.Add("Barcode", typeof(string));
                        recycledTable.Columns.Add("PackageType", typeof(string));
                        recycledTable.Columns.Add("RecycleTime", typeof(DateTime));
                        recycledTable.Columns.Add("Recycler", typeof(string));
                        recycledTable.Columns.Add("Status", typeof(string));
                        recycledTable.Columns.Add("ApplyUnit", typeof(string));
                        recycledTable.Columns.Add("Operation", typeof(string)); // 添加操作列

                        // 从API响应中填充数据
                        foreach (var item in apiResponse.data)
                        {
                            recycledTable.Rows.Add(
                                int.TryParse(item["id"]?.ToString(), out int id) ? id : 0,
                                item["itemName"]?.ToString(),
                                item["itemCode"]?.ToString(),
                                item["quantity"]?.ToString(),
                                DateTime.TryParse(item["recoveryTime"]?.ToString(), out DateTime dt) ? dt : DateTime.Now,
                                item["recoveredBy"]?.ToString(),
                                item["status"]?.ToString(),
                                item["applyDepartment"]?.ToString(),
                                "查看"
                            );
                        }

                        // 绑定数据到GridControl
                        gridControl2.DataSource = recycledTable;

                        // 设置列标题和格式
                        SetupRecycledGridViewColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"已回收列表API调用失败: {ex.Message}");
                // 出错时，保持默认数据，不用显示错误信息打扰用户
            }
        }

        // 批量回收按钮点击事件
        private void batchRecycleButton_Click(object sender, EventArgs e)
        {
            // 获取选中的行
            int[] selectedRows = gridView1.GetSelectedRows();

            // 检查是否有选中的行
            if (selectedRows == null || selectedRows.Length == 0)
            {
                MessageBox.Show("请先选择需要回收的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 创建回收登记弹框
            using (Form batchRecyclingForm = new Form())
            {
                batchRecyclingForm.Text = "批量回收登记";
                batchRecyclingForm.Size = new Size(500, 350);
                batchRecyclingForm.StartPosition = FormStartPosition.CenterScreen;
                batchRecyclingForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                batchRecyclingForm.MaximizeBox = false;
                batchRecyclingForm.MinimizeBox = false;

                // 标题
                Label titleLabel = new Label
                {
                    Text = "批量回收登记",
                    Font = new Font("微软雅黑", 12, FontStyle.Bold),
                    AutoSize = true,
                    Location = new Point(20, 20)
                };

                // 选中物品数量
                Label selectedCountLabel = new Label
                {
                    Text = $"已选择 {selectedRows.Length} 个物品",
                    Font = new Font("微软雅黑", 10),
                    AutoSize = true,
                    Location = new Point(20, 60)
                };

                // 回收人
                Label recyclerLabel = new Label
                {
                    Text = "回收人:",
                    Location = new Point(20, 100),
                    AutoSize = true
                };

                TextBox recyclerTextBox = new TextBox
                {
                    Location = new Point(100, 100),
                    Size = new Size(150, 25),
                    Text = "张三" // 默认值
                };

                // 回收时间
                Label timeLabel = new Label
                {
                    Text = "回收时间:",
                    Location = new Point(20, 140),
                    AutoSize = true
                };

                DateTimePicker timePicker = new DateTimePicker
                {
                    Location = new Point(100, 140),
                    Size = new Size(200, 25),
                    Format = DateTimePickerFormat.Custom,
                    CustomFormat = "yyyy-MM-dd HH:mm:ss",
                    Value = DateTime.Now
                };

                // 备注
                Label remarkLabel = new Label
                {
                    Text = "备注:",
                    Location = new Point(20, 180),
                    AutoSize = true
                };

                TextBox remarkTextBox = new TextBox
                {
                    Location = new Point(100, 180),
                    Size = new Size(350, 60),
                    Multiline = true
                };

                // 确定和取消按钮
                Button confirmButton = new Button
                {
                    Text = "确定",
                    DialogResult = DialogResult.OK,
                    Location = new Point(270, 260),
                    Size = new Size(80, 30),
                    BackColor = Color.DodgerBlue,
                    ForeColor = Color.White
                };

                Button cancelButton = new Button
                {
                    Text = "取消",
                    DialogResult = DialogResult.Cancel,
                    Location = new Point(370, 260),
                    Size = new Size(80, 30)
                };

                // 添加控件到表单
                batchRecyclingForm.Controls.Add(titleLabel);
                batchRecyclingForm.Controls.Add(selectedCountLabel);
                batchRecyclingForm.Controls.Add(recyclerLabel);
                batchRecyclingForm.Controls.Add(recyclerTextBox);
                batchRecyclingForm.Controls.Add(timeLabel);
                batchRecyclingForm.Controls.Add(timePicker);
                batchRecyclingForm.Controls.Add(remarkLabel);
                batchRecyclingForm.Controls.Add(remarkTextBox);
                batchRecyclingForm.Controls.Add(confirmButton);
                batchRecyclingForm.Controls.Add(cancelButton);

                // 显示弹框并获取结果
                DialogResult result = batchRecyclingForm.ShowDialog();

                // 处理表单结果
                if (result == DialogResult.OK)
                {
                    // 获取回收人和回收时间
                    string recycler = recyclerTextBox.Text;
                    DateTime recycleTime = timePicker.Value;
                    string remark = remarkTextBox.Text;

                    // 收集所有选中的行数据
                    List<string> selectedPackages = new List<string>();
                    List<string> selectedBarcodes = new List<string>();

                    foreach (int rowHandle in selectedRows)
                    {
                        string packageName = gridView1.GetRowCellValue(rowHandle, "PackageName")?.ToString();
                        string barcode = gridView1.GetRowCellValue(rowHandle, "Barcode")?.ToString();

                        if (!string.IsNullOrEmpty(packageName))
                        {
                            selectedPackages.Add(packageName);
                            selectedBarcodes.Add(barcode);
                        }
                    }

                    // 执行批量回收操作
                    if (selectedPackages.Count > 0)
                    {
                        // TODO: 实际应用中，这里应该将数据保存到数据库
                        // 将选中的物品从待回收列表移动到已回收列表
                        ProcessBatchRecycling(selectedRows, recycler, recycleTime, remark);

                        // 构建消息显示选中的包
                        StringBuilder messageBuilder = new StringBuilder();
                        messageBuilder.AppendLine($"批量回收登记信息已提交！");
                        messageBuilder.AppendLine($"回收人: {recycler}");
                        messageBuilder.AppendLine($"回收时间: {recycleTime}");
                        if (!string.IsNullOrEmpty(remark))
                        {
                            messageBuilder.AppendLine($"备注: {remark}");
                        }
                        messageBuilder.AppendLine($"已回收 {selectedPackages.Count} 个物品:");

                        for (int i = 0; i < Math.Min(selectedPackages.Count, 5); i++)
                        {
                            messageBuilder.AppendLine($"- {selectedPackages[i]} (条码: {selectedBarcodes[i]})");
                        }

                        if (selectedPackages.Count > 5)
                        {
                            messageBuilder.AppendLine($"... 等 {selectedPackages.Count} 个物品");
                        }

                        MessageBox.Show(messageBuilder.ToString(), "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 刷新数据
                        LoadData();
                    }
                }
            }
        }

        // 处理批量回收的方法
        private async void ProcessBatchRecycling(int[] selectedRows, string recycler, DateTime recycleTime, string remark)
        {
            // 获取数据源
            DataTable pendingTable = gridControl1.DataSource as DataTable;
            DataTable recycledTable = gridControl2.DataSource as DataTable;

            if (pendingTable == null || recycledTable == null) return;

            // 按照索引降序排列，避免删除行时索引变化
            Array.Sort(selectedRows, (a, b) => b.CompareTo(a));

            // 创建批量提交的数据集合
            List<object> batchRecycleData = new List<object>();
            List<int> itemIds = new List<int>();

            // 处理每一行
            foreach (int rowHandle in selectedRows)
            {
                if (rowHandle < 0 || rowHandle >= pendingTable.Rows.Count) continue;

                DataRow pendingRow = pendingTable.Rows[rowHandle];
                
                // 尝试解析ID
                if (int.TryParse(pendingRow["Id"]?.ToString(), out int itemId))
                {
                    itemIds.Add(itemId);
                }

                // 添加到批量提交数据集合
                batchRecycleData.Add(new
                {
                    itemCode = pendingRow["Barcode"]?.ToString(),
                    itemName = pendingRow["PackageName"]?.ToString(),
                    quantity = pendingRow["PackageType"]?.ToString(),
                    recycler = recycler,
                    recycleTime = recycleTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    departmentName = pendingRow["ApplyUnit"]?.ToString(),
                    status = "已回收",
                    remark = remark
                });

                // 创建新行添加到已回收表
                DataRow recycledRow = recycledTable.NewRow();
                recycledRow["PackageName"] = pendingRow["PackageName"];
                recycledRow["Barcode"] = pendingRow["Barcode"];
                recycledRow["PackageType"] = pendingRow["PackageType"];
                recycledRow["RecycleTime"] = recycleTime;
                recycledRow["Recycler"] = recycler;
                recycledRow["Status"] = "已回收";
                recycledRow["ApplyUnit"] = pendingRow["ApplyUnit"];
                recycledRow["Operation"] = "查看";

                recycledTable.Rows.Add(recycledRow);

                // 从待回收表中删除
                pendingTable.Rows.RemoveAt(rowHandle);
            }

            // 尝试提交批量回收数据到API
            if (itemIds.Count > 0)
            {
                await UpdateRecoveryStatusAsync(itemIds);
            }

            // 刷新数据绑定
            gridControl1.RefreshDataSource();
            gridControl2.RefreshDataSource();
        }

        // 提交批量回收数据到API
        private async Task<bool> SubmitBatchRecyclingDataToApi(List<object> batchRecycleData)
        {
            try
            {
                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(batchRecycleData);

                // 提交到API
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/SubmitBatchRecycling";
                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                string jsonResult = await HttpClientHelper.ClientAsync("POST", apiUrl, true, content);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);
                    
                    // 返回是否提交成功
                    return apiResponse != null && apiResponse.code == 200;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交批量回收数据失败: {ex.Message}");
                return false;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            OpenRecyclingRegistrationDialog();
        }

        private void panelControl1_Paint(object sender, PaintEventArgs e)
        {

        }

        // 移除未使用的事件处理程序
        // private void gridControl1_Click(object sender, EventArgs e)
        // {
        // }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            // 双击待回收列表项，打开详情或进行回收操作
            var selectedRowHandle = gridView1.FocusedRowHandle;
            if (selectedRowHandle >= 0)
            {
                // 获取选中行的数据
                var packageName = gridView1.GetRowCellValue(selectedRowHandle, "PackageName")?.ToString();
                var barcode = gridView1.GetRowCellValue(selectedRowHandle, "Barcode")?.ToString();

                // 显示确认对话框
                DialogResult result = MessageBox.Show($"确认回收该包？\n包名称: {packageName}\n包条码: {barcode}",
                    "确认回收", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // TODO: 执行回收操作，更新数据库
                    MessageBox.Show($"已成功回收包: {packageName}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 刷新数据
                    LoadData();
                }
            }
        }

        private void dateTimePicker1_ValueChanged(object sender, EventArgs e)
        {
            // 获取选择的日期
            DateTime selectedDate = dateTimePicker1.Value.Date;
            
            // 从API加载指定日期的已回收列表
            LoadRecoveredItemsByDateFromApi(selectedDate);
        }

        // 从API加载指定日期的已回收物品数据的方法
        private async void LoadRecoveredItemsByDateFromApi(DateTime filterDate)
        {
            try
            {
                // 格式化日期为API所需的格式
                string formattedDate = filterDate.ToString("yyyy-MM-dd");
                
                // 使用API获取指定日期的已回收列表数据
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetRecoveredFinsh?date={formattedDate}";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 尝试解析API响应结构
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                    // 如果响应成功且包含数据
                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 创建已回收列表数据源
                        DataTable recycledTable = new DataTable();
                        recycledTable.Columns.Add("Id", typeof(int));
                        recycledTable.Columns.Add("PackageName", typeof(string));
                        recycledTable.Columns.Add("Barcode", typeof(string));
                        recycledTable.Columns.Add("PackageType", typeof(string));
                        recycledTable.Columns.Add("RecycleTime", typeof(DateTime));
                        recycledTable.Columns.Add("Recycler", typeof(string));
                        recycledTable.Columns.Add("Status", typeof(string));
                        recycledTable.Columns.Add("ApplyUnit", typeof(string));
                        recycledTable.Columns.Add("Operation", typeof(string)); // 添加操作列

                        // 从API响应中填充数据
                        foreach (var item in apiResponse.data)
                        {
                            recycledTable.Rows.Add(
                                int.TryParse(item["id"]?.ToString(), out int id) ? id : 0,
                                item["itemName"]?.ToString(),
                                item["itemCode"]?.ToString(),
                                item["quantity"]?.ToString(),
                                DateTime.TryParse(item["recycleTime"]?.ToString(), out DateTime dt) ? dt : DateTime.Now,
                                item["recycler"]?.ToString(),
                                "已回收",
                                item["departmentName"]?.ToString(),
                                "查看"
                            );
                        }

                        // 绑定数据到GridControl
                        gridControl2.DataSource = recycledTable;

                        // 设置列标题和格式
                        SetupRecycledGridViewColumns();
                    }
                    else
                    {
                        // 如果API返回错误或无数据，则应用本地筛选
                        FilterRecycledItems(filterDate);
                    }
                }
                else
                {
                    // API返回为空，应用本地筛选
                    FilterRecycledItems(filterDate);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按日期筛选API调用失败: {ex.Message}");
                // 出错时，应用本地筛选
                FilterRecycledItems(filterDate);
            }
        }

        // 设置已回收列表的列标题和格式
        private void SetupRecycledGridViewColumns()
        {
            // 隐藏ID列
            if (gridView2.Columns.Contains(gridView2.Columns["Id"]))
            {
                gridView2.Columns["Id"].Visible = false;
            }

            // 设置列标题
            gridView2.Columns["PackageName"].Caption = "物品名称";
            gridView2.Columns["Barcode"].Caption = "物品条码";
            gridView2.Columns["PackageType"].Caption = "回收数量";
            gridView2.Columns["RecycleTime"].Caption = "回收时间";
            gridView2.Columns["Recycler"].Caption = "回收人";
            gridView2.Columns["ApplyUnit"].Caption = "申请单位";
            gridView2.Columns["Status"].Caption = "状态";
            gridView2.Columns["Operation"].Caption = "操作";

            // 设置日期格式
            gridView2.Columns["RecycleTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridView2.Columns["RecycleTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置初始排序
            gridView2.Columns["RecycleTime"].SortOrder = DevExpress.Data.ColumnSortOrder.Descending;
        }

        // 添加新方法用于加载和筛选数据
        private void LoadData()
        {
            // 创建待回收列表数据源
            DataTable pendingTable = new DataTable();
            pendingTable.Columns.Add("Id", typeof(int));
            pendingTable.Columns.Add("PackageName", typeof(string));
            pendingTable.Columns.Add("Barcode", typeof(string));
            pendingTable.Columns.Add("PackageType", typeof(string));
            pendingTable.Columns.Add("Property", typeof(string));
            pendingTable.Columns.Add("ApplyUnit", typeof(string));
            pendingTable.Columns.Add("ApplyTime", typeof(DateTime));
            pendingTable.Columns.Add("Operation", typeof(string)); // 添加操作列

            // 创建已回收列表数据源
            DataTable recycledTable = new DataTable();
            recycledTable.Columns.Add("PackageName", typeof(string));
            recycledTable.Columns.Add("Barcode", typeof(string));
            recycledTable.Columns.Add("PackageType", typeof(string));
            recycledTable.Columns.Add("RecycleTime", typeof(DateTime));
            recycledTable.Columns.Add("Recycler", typeof(string));
            recycledTable.Columns.Add("Status", typeof(string));
            recycledTable.Columns.Add("ApplyUnit", typeof(string));
            recycledTable.Columns.Add("Operation", typeof(string)); // 添加操作列

            // 绑定数据到GridControl
            gridControl1.DataSource = pendingTable;
            gridControl2.DataSource = recycledTable;

            // 设置列标题
            gridView1.Columns["PackageName"].Caption = "物品名称";
            gridView1.Columns["Barcode"].Caption = "物品条码";
            gridView1.Columns["PackageType"].Caption = "数量";
            gridView1.Columns["Property"].Caption = "紧急度";
            gridView1.Columns["ApplyUnit"].Caption = "申请单位";
            gridView1.Columns["ApplyTime"].Caption = "申请时间";
            gridView1.Columns["Operation"].Caption = "操作";
            
            // 隐藏ID列
            if (gridView1.Columns.Contains(gridView1.Columns["Id"]))
            {
                gridView1.Columns["Id"].Visible = false;
            }

            gridView2.Columns["PackageName"].Caption = "物品名称";
            gridView2.Columns["Barcode"].Caption = "物品条码";
            gridView2.Columns["PackageType"].Caption = "回收数量";
            gridView2.Columns["RecycleTime"].Caption = "回收时间";
            gridView2.Columns["Recycler"].Caption = "回收人";
            gridView2.Columns["ApplyUnit"].Caption = "申请单位";
            gridView2.Columns["Status"].Caption = "状态";
            gridView2.Columns["Operation"].Caption = "操作";

            // 设置日期格式
            gridView1.Columns["ApplyTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridView1.Columns["ApplyTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            gridView2.Columns["RecycleTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridView2.Columns["RecycleTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置初始排序
            gridView1.Columns["ApplyTime"].SortOrder = DevExpress.Data.ColumnSortOrder.Descending;
            gridView2.Columns["RecycleTime"].SortOrder = DevExpress.Data.ColumnSortOrder.Descending;
        }

        private void FilterRecycledItems(DateTime filterDate)
        {
            // 获取当前数据源
            DataTable dataTable = gridControl2.DataSource as DataTable;
            if (dataTable == null) return;

            // 按日期筛选
            gridView2.ActiveFilterString = $"[RecycleTime] >= #{filterDate.ToString("yyyy-MM-dd")}#";
        }

        // 添加事件处理程序，绑定到新添加的按钮
        private void searchButton_Click(object sender, EventArgs e)
        {
            string searchText = searchTextBox.Text.Trim();
            if (!string.IsNullOrEmpty(searchText))
            {
                // 使用API进行搜索
                SearchPackagesByBarcodeAsync(searchText);
            }
            else
            {
                // 如果搜索框为空，则刷新所有数据
                LoadPendingRecyclingItemsFromApi();
                LoadRecoveredItemsFromApi();
                
                // 清除筛选
                gridView1.ActiveFilterString = string.Empty;
                gridView2.ActiveFilterString = string.Empty;
            }
        }

        // 根据条码搜索包数据的异步方法
        private async void SearchPackagesByBarcodeAsync(string barcode)
        {
            try
            {
                // 使用API搜索待回收物品
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetItemInfo?ItemCode={barcode}";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                    // 如果响应成功且包含数据
                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        // 创建待回收列表数据源
                        DataTable pendingTable = new DataTable();
                        pendingTable.Columns.Add("Id", typeof(int));
                        pendingTable.Columns.Add("PackageName", typeof(string));
                        pendingTable.Columns.Add("Barcode", typeof(string));
                        pendingTable.Columns.Add("PackageType", typeof(string));
                        pendingTable.Columns.Add("Property", typeof(string));
                        pendingTable.Columns.Add("ApplyUnit", typeof(string));
                        pendingTable.Columns.Add("ApplyTime", typeof(DateTime));
                        pendingTable.Columns.Add("Operation", typeof(string)); // 添加操作列

                        // 从API响应中填充数据
                        foreach (var item in apiResponse.data)
                        {
                            pendingTable.Rows.Add(
                                int.TryParse(item["id"]?.ToString(), out int id) ? id : 0,
                                item["itemName"]?.ToString(),
                                item["itemCode"]?.ToString(),
                                item["quantity"]?.ToString(),
                                item["itemattribute"]?.ToString(),
                                item["urgent"]?.ToString(),
                                DateTime.TryParse(item["applyTime"]?.ToString(), out DateTime dt) ? dt : DateTime.Now,
                                "回收"
                            );
                        }

                        // 绑定数据到GridControl
                        gridControl1.DataSource = pendingTable;
                        
                        // 设置列标题和格式
                        SetupPendingGridViewColumns();
                    }
                    else
                    {
                        // 如果API返回错误或无数据，则应用本地筛选
                        gridView1.ActiveFilterString = $"[Barcode] LIKE '%{barcode}%'";
                    }
                }
                
                // 同时搜索已回收物品
                string recycledApiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetRecoveredFinsh?ItemCode={barcode}";
                string recycledJsonResult = await HttpClientHelper.ClientAsync("GET", recycledApiUrl, false);
                
                if (!string.IsNullOrEmpty(recycledJsonResult))
                {
                    // 解析API响应
                    var recycledApiResponse = JsonConvert.DeserializeObject<ApiResponse>(recycledJsonResult);
                    
                    // 如果响应成功且包含数据
                    if (recycledApiResponse != null && recycledApiResponse.code == 200 && recycledApiResponse.data != null)
                    {
                        // 创建已回收列表数据源
                        DataTable recycledTable = new DataTable();
                        recycledTable.Columns.Add("Id", typeof(int));
                        recycledTable.Columns.Add("PackageName", typeof(string));
                        recycledTable.Columns.Add("Barcode", typeof(string));
                        recycledTable.Columns.Add("PackageType", typeof(string));
                        recycledTable.Columns.Add("RecycleTime", typeof(DateTime));
                        recycledTable.Columns.Add("Recycler", typeof(string));
                        recycledTable.Columns.Add("Status", typeof(string));
                        recycledTable.Columns.Add("ApplyUnit", typeof(string));
                        recycledTable.Columns.Add("Operation", typeof(string)); // 添加操作列

                        // 从API响应中填充数据
                        foreach (var item in recycledApiResponse.data)
                        {
                            recycledTable.Rows.Add(
                                int.TryParse(item["id"]?.ToString(), out int id) ? id : 0,
                                item["itemName"]?.ToString(),
                                item["itemCode"]?.ToString(),
                                item["quantity"]?.ToString(),
                                DateTime.TryParse(item["recoveryTime"]?.ToString(), out DateTime dt) ? dt : DateTime.Now,
                                item["recoveredBy"]?.ToString(),
                                item["status"]?.ToString(),
                                item["applyDepartment"]?.ToString(),
                                "查看"
                            );
                        }

                        // 绑定数据到GridControl
                        gridControl2.DataSource = recycledTable;
                        
                        // 设置列标题和格式
                        SetupRecycledGridViewColumns();
                    }
                    else
                    {
                        // 如果API返回错误或无数据，则应用本地筛选
                        gridView2.ActiveFilterString = $"[Barcode] LIKE '%{barcode}%'";
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"搜索时发生错误: {ex.Message}");
                
                // 发生异常时在界面上进行本地筛选
                gridView1.ActiveFilterString = $"[Barcode] LIKE '%{barcode}%'";
                gridView2.ActiveFilterString = $"[Barcode] LIKE '%{barcode}%'";
            }
        }

        // 设置待回收列表的列标题和格式
        private void SetupPendingGridViewColumns()
        {
            // 设置列标题
            gridView1.Columns["PackageName"].Caption = "物品名称";
            gridView1.Columns["Barcode"].Caption = "物品条码";
            gridView1.Columns["PackageType"].Caption = "数量";
            gridView1.Columns["Property"].Caption = "紧急度";
            gridView1.Columns["ApplyUnit"].Caption = "申请单位";
            gridView1.Columns["ApplyTime"].Caption = "申请时间";
            gridView1.Columns["Operation"].Caption = "操作";
            
            // 隐藏ID列
            if (gridView1.Columns.Contains(gridView1.Columns["Id"]))
            {
                gridView1.Columns["Id"].Visible = false;
            }

            // 设置日期格式
            gridView1.Columns["ApplyTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridView1.Columns["ApplyTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置初始排序
            gridView1.Columns["ApplyTime"].SortOrder = DevExpress.Data.ColumnSortOrder.Descending;
        }

        private void refreshButton_Click(object sender, EventArgs e)
        {
            // 刷新数据
            LoadData();
            
            // 尝试从API刷新待回收列表
            LoadPendingRecyclingItemsFromApi();
            
            // 尝试从API刷新已回收列表
            LoadRecoveredItemsFromApi();
            
            searchTextBox.Text = string.Empty;
            // 清除筛选
            gridView1.ActiveFilterString = string.Empty;
            gridView2.ActiveFilterString = string.Empty;
        }

        private void label5_Click(object sender, EventArgs e)
        {

        }

       

        // 处理gridView1中操作列的点击事件
        private void GridView1_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {
            // 检查是否点击了操作列
            if (e.Column.FieldName == "Operation")
            {
                // 获取当前行数据
                var packageName = gridView1.GetRowCellValue(e.RowHandle, "PackageName")?.ToString();
                var barcode = gridView1.GetRowCellValue(e.RowHandle, "Barcode")?.ToString();

                // 显示确认对话框
                DialogResult result = MessageBox.Show($"确认回收该物品？\n物品名称: {packageName}\n物品条码: {barcode}",
                    "确认回收", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 执行回收操作
                    ProcessSingleRecycling(e.RowHandle);
                    MessageBox.Show($"已成功回收物品: {packageName}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        // 处理gridView2中操作列的点击事件
        private void GridView2_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {
            // 检查是否点击了操作列
            if (e.Column.FieldName == "Operation")
            {
                // 获取当前行数据
                var packageName = gridView2.GetRowCellValue(e.RowHandle, "PackageName")?.ToString();
                var barcode = gridView2.GetRowCellValue(e.RowHandle, "Barcode")?.ToString();
                var recycler = gridView2.GetRowCellValue(e.RowHandle, "Recycler")?.ToString();
                var recycleTime = gridView2.GetRowCellValue(e.RowHandle, "RecycleTime");
                var applyUnit = gridView2.GetRowCellValue(e.RowHandle, "ApplyUnit")?.ToString();

                // 创建一个包含"查看"和"撤销"按钮的弹出菜单
                ContextMenuStrip menu = new ContextMenuStrip();
                
                // 添加查看按钮
                ToolStripMenuItem viewItem = new ToolStripMenuItem("查看详情");
                viewItem.Click += (s, args) =>
                {
                    // 显示详情信息
                    StringBuilder detailBuilder = new StringBuilder();
                    detailBuilder.AppendLine($"物品名称: {packageName}");
                    detailBuilder.AppendLine($"物品条码: {barcode}");
                    detailBuilder.AppendLine($"回收人: {recycler}");
                    detailBuilder.AppendLine($"回收时间: {recycleTime}");
                    detailBuilder.AppendLine($"申请单位: {applyUnit}");
                    detailBuilder.AppendLine($"状态: 已回收");

                    MessageBox.Show(detailBuilder.ToString(), "物品详情", MessageBoxButtons.OK, MessageBoxIcon.Information);
                };
                menu.Items.Add(viewItem);
                
                // 添加撤销按钮
                ToolStripMenuItem cancelItem = new ToolStripMenuItem("撤销回收");
                cancelItem.Click += async (s, args) =>
                {
                    // 显示确认对话框
                    DialogResult result = MessageBox.Show($"确认撤销该物品的回收状态？\n物品名称: {packageName}\n物品条码: {barcode}",
                        "确认撤销", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // 尝试获取ID
                        if (int.TryParse(gridView2.GetRowCellValue(e.RowHandle, "Id")?.ToString(), out int itemId))
                        {
                            // 调用撤销API
                            bool success = await CancelRecoveryStatusAsync(itemId);
                            
                            if (success)
                            {
                                // 从已回收列表中移除
                                DataTable recycledTable = gridControl2.DataSource as DataTable;
                                if (recycledTable != null && e.RowHandle >= 0 && e.RowHandle < recycledTable.Rows.Count)
                                {
                                    recycledTable.Rows.RemoveAt(e.RowHandle);
                                    gridControl2.RefreshDataSource();
                                }
                                
                                MessageBox.Show($"已成功撤销物品 {packageName} 的回收状态", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                
                                // 刷新待回收列表
                                LoadPendingRecyclingItemsFromApi();
                            }
                            else
                            {
                                MessageBox.Show("撤销回收状态失败，请重试", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            MessageBox.Show("无法获取物品ID，撤销操作失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                };
                menu.Items.Add(cancelItem);
                
                // 在点击位置显示菜单
                Point pt = gridControl2.PointToClient(Cursor.Position);
                menu.Show(gridControl2, pt);
            }
        }

        // 自定义列数据处理
        private void GridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.Column.FieldName == "Operation" && e.IsGetData)
            {
                e.Value = "回收";
            }
        }

        // 处理单个物品回收
        private async void ProcessSingleRecycling(int rowHandle)
        {
            // 获取数据源
            DataTable pendingTable = gridControl1.DataSource as DataTable;
            DataTable recycledTable = gridControl2.DataSource as DataTable;

            if (pendingTable == null || recycledTable == null || rowHandle < 0 || rowHandle >= pendingTable.Rows.Count)
                return;

            DataRow pendingRow = pendingTable.Rows[rowHandle];

            // 获取当前用户名（实际应用中应该从登录信息获取）
            string recycler = "当前用户";
            DateTime recycleTime = DateTime.Now;
            
            // 尝试解析ID
            List<int> itemIds = new List<int>();
            if (int.TryParse(pendingRow["Id"]?.ToString(), out int itemId))
            {
                itemIds.Add(itemId);
                
                // 调用更新状态API
                await UpdateRecoveryStatusAsync(itemIds);
            }

            // 创建新行添加到已回收表
            DataRow recycledRow = recycledTable.NewRow();
            recycledRow["PackageName"] = pendingRow["PackageName"];
            recycledRow["Barcode"] = pendingRow["Barcode"];
            recycledRow["PackageType"] = pendingRow["PackageType"];
            recycledRow["RecycleTime"] = recycleTime;
            recycledRow["Recycler"] = recycler;
            recycledRow["Status"] = "已回收";
            recycledRow["ApplyUnit"] = pendingRow["ApplyUnit"];
            recycledRow["Operation"] = "查看";

            recycledTable.Rows.Add(recycledRow);

            // 从待回收表中删除
            pendingTable.Rows.RemoveAt(rowHandle);

            // 刷新数据绑定
            gridControl1.RefreshDataSource();
            gridControl2.RefreshDataSource();
        }

        // 提交回收数据到API
        private async Task<bool> SubmitRecyclingDataToApi(string barcode, string packageName, string quantity, string recycler, DateTime recycleTime, string departmentName)
        {
            try
            {
                // 构建提交数据
                var recycleData = new
                {
                    itemCode = barcode,
                    itemName = packageName,
                    quantity = quantity,
                    recycler = recycler,
                    recycleTime = recycleTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    departmentName = departmentName,
                    status = "已回收"
                };

                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(recycleData);

                // 提交到API
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/SubmitRecycling";
                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                string jsonResult = await HttpClientHelper.ClientAsync("POST", apiUrl, true, content);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);
                    
                    // 返回是否提交成功
                    return apiResponse != null && apiResponse.code == 200;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交回收数据失败: {ex.Message}");
                return false;
            }
        }

        // 添加默认数据到表格的方法
        private void AddDefaultData(DataGridView gridView)
        {
            // 不添加默认数据，保持表格为空
        }

        // 添加一个方法用于根据条码搜索包数据
        private async Task SearchPackagesByBarcode(string barcode, DataGridView gridView)
        {
            try
            {
                // 使用HttpClientHelper发起GET请求
                string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetItemTableInfo?ItemCode={barcode}";
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 反序列化JSON结果
                    List<Package> packages = JsonConvert.DeserializeObject<List<Package>>(jsonResult);

                    // 检查返回的数据是否为空
                    if (packages != null && packages.Count > 0)
                    {
                        // 将API返回的数据添加到表格
                        foreach (var package in packages)
                        {
                            gridView.Rows.Add(
                                false, // 复选框默认未选中
                                package.Id,
                                package.PackageName,
                                package.Barcode,
                                package.PackageType,
                                package.Property,
                                package.ApplyUnit
                            );
                        }
                    }
                    else
                    {
                        // 没有数据，显示提示信息
                        MessageBox.Show("未找到相关物品信息", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    // API返回为空，显示提示信息
                    MessageBox.Show("未能获取数据，请稍后重试", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                // 发生异常，显示错误信息
                MessageBox.Show($"搜索数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 添加一个方法用于打开回收登记弹窗
        private async void OpenRecyclingRegistrationDialog()
        {
            // 创建回收登记弹框
            using (Form recyclingForm = new Form())
            {
                recyclingForm.Text = "回收登记";
                recyclingForm.Size = new Size(750, 500);
                recyclingForm.StartPosition = FormStartPosition.CenterScreen;
                recyclingForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                recyclingForm.MaximizeBox = false;
                recyclingForm.MinimizeBox = false;

                // 标题和搜索区域
                Label titleLabel = new Label
                {
                    Text = "回收登记",
                    Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
                    AutoSize = true,
                    Location = new Point(20, 20)
                };

                // 包条码/申请单号搜索区域
                Label barcodeLabel = new Label
                {
                    Text = "包条码/申请单号:",
                    Location = new Point(20, 70),
                    AutoSize = true
                };

                TextBox barcodeTextBox = new TextBox
                {
                    Location = new Point(150, 70),
                    Size = new Size(250, 25)
                };

                // 搜索按钮
                Button searchButton = new Button
                {
                    Text = "搜索",
                    BackColor = Color.DodgerBlue,
                    ForeColor = Color.White,
                    Location = new Point(410, 68),
                    Size = new Size(80, 30)
                };

                // 创建数据表格
                DataGridView packageGridView = new DataGridView
                {
                    Location = new Point(20, 110),
                    Size = new Size(700, 280),
                    AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                    AllowUserToAddRows = false,
                    AllowUserToDeleteRows = false,
                    SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                    BackgroundColor = Color.White,
                    MultiSelect = true
                };

                // 添加复选框列
                DataGridViewCheckBoxColumn checkBoxColumn = new DataGridViewCheckBoxColumn
                {
                    HeaderText = "选择",
                    Name = "SelectCheckBox",
                    Width = 50,
                    FalseValue = false,
                    TrueValue = true,
                    IndeterminateValue = false,
                    ReadOnly = false
                };
                packageGridView.Columns.Add(checkBoxColumn);

                // 添加ID列（隐藏）
                DataGridViewTextBoxColumn idColumn = new DataGridViewTextBoxColumn
                {
                    HeaderText = "ID",
                    Name = "ID",
                    Visible = false
                };
                packageGridView.Columns.Add(idColumn);

                // 添加其他列到表格
                packageGridView.Columns.Add("PackageName", "包名称");
                packageGridView.Columns.Add("Barcode", "包条码");
                packageGridView.Columns.Add("PackageType", "包类型");
                packageGridView.Columns.Add("Property", "包属性");
                packageGridView.Columns.Add("ApplyUnit", "申请单位");

                // 底部区域
                Label recyclerLabel = new Label
                {
                    Text = "回收人:",
                    Location = new Point(20, 410),
                    AutoSize = true
                };

                TextBox recyclerTextBox = new TextBox
                {
                    Location = new Point(80, 410),
                    Size = new Size(120, 25),
                    Text = "张三" // 默认值
                };

                Label timeLabel = new Label
                {
                    Text = "回收时间:",
                    Location = new Point(250, 410),
                    AutoSize = true
                };

                DateTimePicker timePicker = new DateTimePicker
                {
                    Location = new Point(320, 410),
                    Size = new Size(180, 25),
                    Format = DateTimePickerFormat.Custom,
                    CustomFormat = "yyyy-MM-dd HH:mm:ss",
                    Value = DateTime.Now
                };

                // 确定和退出按钮
                Button confirmButton = new Button
                {
                    Text = "确定",
                    DialogResult = DialogResult.OK,
                    Location = new Point(550, 410),
                    Size = new Size(80, 30),
                    BackColor = Color.DodgerBlue,
                    ForeColor = Color.White
                };

                Button exitButton = new Button
                {
                    Text = "退出",
                    DialogResult = DialogResult.Cancel,
                    Location = new Point(640, 410),
                    Size = new Size(80, 30)
                };

                // 添加所有控件到表单
                recyclingForm.Controls.Add(titleLabel);
                recyclingForm.Controls.Add(barcodeLabel);
                recyclingForm.Controls.Add(barcodeTextBox);
                recyclingForm.Controls.Add(searchButton);
                recyclingForm.Controls.Add(packageGridView);
                recyclingForm.Controls.Add(recyclerLabel);
                recyclingForm.Controls.Add(recyclerTextBox);
                recyclingForm.Controls.Add(timeLabel);
                recyclingForm.Controls.Add(timePicker);
                recyclingForm.Controls.Add(confirmButton);
                recyclingForm.Controls.Add(exitButton);

                // 加载状态标签
                Label loadingLabel = new Label
                {
                    Text = "正在加载数据...",
                    Font = new Font("Microsoft YaHei", 10),
                    AutoSize = true,
                    Location = new Point(300, 240),
                    Visible = false
                };
                recyclingForm.Controls.Add(loadingLabel);

                // 定义方法用于查询并显示包信息
                async Task LoadPackageInfo(string searchText = "")
                {
                    // 清空当前表格数据
                    packageGridView.Rows.Clear();

                    // 显示加载提示
                    loadingLabel.Visible = true;

                    try
                    {
                        // 使用与待回收列表相同的API端点
                        string apiUrl = $"{ApiBaseUrl}/api/RecyclingCleaning/GetItemTableInfo";
                        
                        // 如果有搜索条件，添加参数
                        if (!string.IsNullOrEmpty(searchText))
                        {
                            apiUrl += $"?ItemCode={searchText}";
                        }

                        string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false);

                        if (!string.IsNullOrEmpty(jsonResult))
                        {
                            // 尝试解析API响应结构
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                            // 如果响应成功且包含数据
                            if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                            {
                                // 直接从API响应中获取值并添加到表格
                                foreach (var item in apiResponse.data)
                                {
                                    packageGridView.Rows.Add(
                                        false, // 复选框默认不选中
                                        item["id"]?.ToString(),
                                        item["itemName"]?.ToString(),
                                        item["itemCode"]?.ToString(), 
                                        item["itemType"]?.ToString(),
                                        item["itemattribute"]?.ToString(),
                                        item["recyclingId"]?.ToString()
                                    );
                                }

                                if (apiResponse.data.Count() == 0)
                                {
                                    MessageBox.Show("没有可回收的物品", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                            }
                            else if (apiResponse != null && apiResponse.code != 200)
                            {
                                // API返回错误状态码
                                MessageBox.Show($"获取数据失败: {apiResponse.msg}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                        else
                        {
                            // API返回为空，不添加默认数据
                            MessageBox.Show("未能获取数据，请稍后重试", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"加载数据时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        
                        // API调用失败，不添加默认数据
                        MessageBox.Show("无法连接到服务器，请检查网络连接后重试", "连接错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                    finally
                    {
                        // 隐藏加载提示
                        loadingLabel.Visible = false;
                    }
                }

                // 处理搜索按钮点击事件
                searchButton.Click += async (s, ev) =>
                {
                    string searchText = barcodeTextBox.Text.Trim();
                    await LoadPackageInfo(searchText);
                };

                // 处理复选框选中事件
                packageGridView.CellContentClick += (s, ev) =>
                {
                    // 检查是否点击了复选框列
                    if (ev.ColumnIndex == packageGridView.Columns["SelectCheckBox"].Index && ev.RowIndex >= 0)
                    {
                        // 切换复选框状态
                        bool currentValue = Convert.ToBoolean(packageGridView.Rows[ev.RowIndex].Cells["SelectCheckBox"].Value);
                        packageGridView.Rows[ev.RowIndex].Cells["SelectCheckBox"].Value = !currentValue;
                    }
                };

                // 窗体加载完成后，自动加载所有包信息
                recyclingForm.Shown += async (s, ev) =>
                {
                    await LoadPackageInfo();
                };

                // 处理确认按钮点击事件
                confirmButton.Click += async (s, ev) =>
                {
                    // 获取回收人和回收时间
                    string recycler = recyclerTextBox.Text;
                    DateTime recycleTime = timePicker.Value;

                    // 收集所有选中的行的ID
                    List<int> selectedItemIds = new List<int>();

                    foreach (DataGridViewRow row in packageGridView.Rows)
                    {
                        bool isSelected = Convert.ToBoolean(row.Cells["SelectCheckBox"].Value);
                        if (isSelected)
                        {
                            // 尝试解析ID为整数
                            if (row.Cells["ID"].Value != null && int.TryParse(row.Cells["ID"].Value.ToString(), out int id))
                            {
                                selectedItemIds.Add(id);
                            }
                        }
                    }

                    // 检查是否有选中的行
                    if (selectedItemIds.Count > 0)
                    {
                        // 创建符合后端API要求的数据格式
                        var recoveryData = new
                        {
                            recoveredBy = recycler,
                            recoveryTime = recycleTime.ToString("yyyy-MM-dd"),
                            equipmentPackageId = selectedItemIds.ToArray()
                        };

                        // 提交回收数据到API
                        bool success = await SubmitRecyclingRegistrationToApi(recoveryData);

                        if (success)
                        {
                            // 构建消息显示选中的包
                            StringBuilder messageBuilder = new StringBuilder();
                            messageBuilder.AppendLine($"回收登记信息已提交！");
                            messageBuilder.AppendLine($"回收人: {recycler}");
                            messageBuilder.AppendLine($"回收时间: {recycleTime.ToString("yyyy-MM-dd")}");
                            messageBuilder.AppendLine($"已选择 {selectedItemIds.Count} 个物品");

                            MessageBox.Show(messageBuilder.ToString(), "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 关闭弹窗并返回OK结果
                            recyclingForm.DialogResult = DialogResult.OK;
                        }
                        else
                        {
                            MessageBox.Show("提交回收数据失败，请重试", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("请至少选择一个物品进行回收", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                };

                // 显示弹框并获取结果
                DialogResult result = recyclingForm.ShowDialog();

                // 处理表单结果
                if (result == DialogResult.OK)
                {
                    // 刷新主界面数据
                    LoadPendingRecyclingItemsFromApi();
                    LoadRecoveredItemsFromApi();
                }
            }
        }
        
        // 提交回收登记数据到API
        private async Task<bool> SubmitRecyclingRegistrationToApi(object recoveryData)
        {
            try
            {
                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(recoveryData);

                // 提交到API
                string apiUrl = $"{ApiWriteUrl}/api/RecyclingCleaning/AddRecoveryRegistration";
                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                string jsonResult = await HttpClientHelper.ClientAsync("POST", apiUrl, true, content);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                   
                    // 返回是否提交成功
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提交回收登记数据失败: {ex.Message}");
                MessageBox.Show($"提交回收登记数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        // 新增方法：更新回收状态
        private async Task<bool> UpdateRecoveryStatusAsync(List<int> ids)
        {
            try
            {
                // 构建请求数据
                var requestData = new
                {
                    ids = ids.Distinct().ToArray() // 使用Distinct()去重
                };

                // 将对象转换为JSON字符串
                string jsonData = JsonConvert.SerializeObject(requestData);

                // 提交到API
                string apiUrl = $"{ApiWriteUrl}/api/RecyclingCleaning/UpdateRecoveryStatus";
                
                // 创建HttpContent对象
                HttpContent content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                string jsonResult = await HttpClientHelper.ClientAsync("PUT", apiUrl, true, content);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse>(jsonResult);

                    // 返回是否提交成功
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新回收状态失败: {ex.Message}");
                return false;
            }
        }

        // 添加撤销回收状态的API方法
        private async Task<bool> CancelRecoveryStatusAsync(int id)
        {
            try
            {
                // 调用撤销API
                string apiUrl = $"{ApiWriteUrl}/api/RecyclingCleaning/CancelRecoveryStatus/{id}";
                
                // 发送PUT请求
                string jsonResult = await HttpClientHelper.ClientAsync("PUT", apiUrl, false);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    // 返回是否提交成功
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"撤销回收状态失败: {ex.Message}");
                return false;
            }
        }
    }
}

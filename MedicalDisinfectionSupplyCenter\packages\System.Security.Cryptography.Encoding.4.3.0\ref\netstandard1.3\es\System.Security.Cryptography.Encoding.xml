﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Encoding</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.AsnEncodedData">
      <summary>Representa datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One).</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" /> utilizando una matriz de bytes.</summary>
      <param name="rawData">Matriz de bytes que contiene datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One).</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" /> utilizando una instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">Instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> es null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.Security.Cryptography.Oid,System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" /> utilizando un objeto <see cref="T:System.Security.Cryptography.Oid" /> y una matriz de bytes.</summary>
      <param name="oid">Un objeto <see cref="T:System.Security.Cryptography.Oid" />.</param>
      <param name="rawData">Matriz de bytes que contiene datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One).</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.#ctor(System.String,System.Byte[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.AsnEncodedData" /> utilizando una matriz de bytes.</summary>
      <param name="oid">Cadena que representa la información de <see cref="T:System.Security.Cryptography.Oid" />.</param>
      <param name="rawData">Matriz de bytes que contiene datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One).</param>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copia información de un objeto <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <param name="asnEncodedData">Objeto <see cref="T:System.Security.Cryptography.AsnEncodedData" /> en el que se va a basar el nuevo objeto.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="asnEncodedData " />es null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AsnEncodedData.Format(System.Boolean)">
      <summary>Devuelve una versión con formato de los datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One) como una cadena.</summary>
      <returns>Cadena con formato que representa los datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One).</returns>
      <param name="multiLine">Es true si la cadena de retorno debe contener los retornos de carro; de lo contrario, es false.</param>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.Oid">
      <summary>Obtiene o establece el valor <see cref="T:System.Security.Cryptography.Oid" /> de un objeto <see cref="T:System.Security.Cryptography.AsnEncodedData" />.</summary>
      <returns>Un objeto <see cref="T:System.Security.Cryptography.Oid" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AsnEncodedData.RawData">
      <summary>Obtiene o establece los datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One) que están representados en una matriz de bytes.</summary>
      <returns>Matriz de bytes que representa los datos codificados mediante Notación de sintaxis abstracta uno (ASN.1, Abstract Syntax Notation One).</returns>
      <exception cref="T:System.ArgumentNullException">El valor es null.</exception>
    </member>
    <member name="T:System.Security.Cryptography.Oid">
      <summary>Representa un identificador de objeto criptográfico.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.Security.Cryptography.Oid)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.Oid" /> usando el objeto <see cref="T:System.Security.Cryptography.Oid" /> especificado.</summary>
      <param name="oid">Información del identificador de objeto que se va a utilizar para crear el nuevo identificador de objeto.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="oid " />es null.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.Oid" /> utilizando un valor de cadena de un objeto <see cref="T:System.Security.Cryptography.Oid" />.</summary>
      <param name="oid">Identificador de objeto.</param>
    </member>
    <member name="M:System.Security.Cryptography.Oid.#ctor(System.String,System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.Oid" /> con el nombre descriptivo y el valor especificados.</summary>
      <param name="value">El número separado por puntos del identificador.</param>
      <param name="friendlyName">Nombre descriptivo del identificador.</param>
    </member>
    <member name="P:System.Security.Cryptography.Oid.FriendlyName">
      <summary>Obtiene o establece el nombre descriptivo del identificador.</summary>
      <returns>Nombre descriptivo del identificador.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromFriendlyName(System.String,System.Security.Cryptography.OidGroup)">
      <summary>Crea un objeto <see cref="T:System.Security.Cryptography.Oid" /> a partir del nombre descriptivo de OID buscando en el grupo especificado.</summary>
      <returns>Objeto que representa el OID especificado.</returns>
      <param name="friendlyName">Nombre descriptivo del identificador.</param>
      <param name="group">Grupo en el que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="friendlyName " /> es null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">No se encontró el OID.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Oid.FromOidValue(System.String,System.Security.Cryptography.OidGroup)">
      <summary>Crea un objeto <see cref="T:System.Security.Cryptography.Oid" /> utilizando el grupo y el valor de OID especificados.</summary>
      <returns>Nueva instancia de un objeto <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="oidValue">Valor OID.</param>
      <param name="group">Grupo en el que se va a buscar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidValue" /> es null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">El nombre descriptivo para el valor de OID no se encontró.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Oid.Value">
      <summary>Obtiene o establece el número separado por puntos del identificador.</summary>
      <returns>El número separado por puntos del identificador.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidCollection">
      <summary>Representa una colección de objetos <see cref="T:System.Security.Cryptography.Oid" />.Esta clase no puede heredarse.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.Add(System.Security.Cryptography.Oid)">
      <summary>Agrega un objeto <see cref="T:System.Security.Cryptography.Oid" /> al objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Índice del objeto <see cref="T:System.Security.Cryptography.Oid" /> agregado.</returns>
      <param name="oid">Objeto <see cref="T:System.Security.Cryptography.Oid" /> que se va a agregar a la colección.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.CopyTo(System.Security.Cryptography.Oid[],System.Int32)">
      <summary>Copia el objeto <see cref="T:System.Security.Cryptography.OidCollection" /> en una matriz.</summary>
      <param name="array">Matriz en la que se va a copiar el objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</param>
      <param name="index">Ubicación donde se inicia la operación de copia.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Count">
      <summary>Obtiene el número de objetos <see cref="T:System.Security.Cryptography.Oid" /> de la colección. </summary>
      <returns>Número de objetos <see cref="T:System.Security.Cryptography.Oid" /> de la colección.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.GetEnumerator">
      <summary>Devuelve un objeto <see cref="T:System.Security.Cryptography.OidEnumerator" /> que puede utilizarse para navegar en el objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Un objeto <see cref="T:System.Security.Cryptography.OidEnumerator" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.Int32)">
      <summary>Obtiene un objeto <see cref="T:System.Security.Cryptography.Oid" /> del objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Un objeto <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="index">Ubicación del objeto <see cref="T:System.Security.Cryptography.Oid" /> en la colección.</param>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.Item(System.String)">
      <summary>Obtiene el primer objeto <see cref="T:System.Security.Cryptography.Oid" /> que contenga un valor de la propiedad <see cref="P:System.Security.Cryptography.Oid.Value" /> o de la propiedad <see cref="P:System.Security.Cryptography.Oid.FriendlyName" /> que coincida con el valor de cadena especificado en el objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Un objeto <see cref="T:System.Security.Cryptography.Oid" />.</returns>
      <param name="oid">Cadena que representa una propiedad <see cref="P:System.Security.Cryptography.Oid.Value" /> o una propiedad <see cref="P:System.Security.Cryptography.Oid.FriendlyName" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia el objeto <see cref="T:System.Security.Cryptography.OidCollection" /> en una matriz.</summary>
      <param name="array">Matriz en la que se va a copiar el objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</param>
      <param name="index">Ubicación donde se inicia la operación de copia.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> no puede ser un matriz multidimensional.O bienLa longitud de <paramref name="array" /> es una longitud de desplazamiento no válida.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor de <paramref name="index" /> está fuera del intervalo.</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Security.Cryptography.OidCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Security.Cryptography.OidCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un objeto <see cref="T:System.Security.Cryptography.OidEnumerator" /> que puede utilizarse para navegar en el objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objeto <see cref="T:System.Security.Cryptography.OidEnumerator" /> que se puede utilizar para navegar en la colección.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidEnumerator">
      <summary>Proporciona la capacidad para navegar en un objeto <see cref="T:System.Security.Cryptography.OidCollection" />.Esta clase no puede heredarse.</summary>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.Current">
      <summary>Obtiene el objeto <see cref="T:System.Security.Cryptography.Oid" /> actual de un objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>El objeto <see cref="T:System.Security.Cryptography.Oid" /> actual de la colección.</returns>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.MoveNext">
      <summary>Avanza al siguiente objeto <see cref="T:System.Security.Cryptography.Oid" /> de un objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Es true si el enumerador avanzó con éxito hasta el siguiente elemento; es false si el enumerador alcanzó el final de la colección.</returns>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador.</exception>
    </member>
    <member name="M:System.Security.Cryptography.OidEnumerator.Reset">
      <summary>Establece un enumerador en su posición inicial.</summary>
      <exception cref="T:System.InvalidOperationException">La colección se modificó después de crear el enumerador.</exception>
    </member>
    <member name="P:System.Security.Cryptography.OidEnumerator.System#Collections#IEnumerator#Current">
      <summary>Obtiene el objeto <see cref="T:System.Security.Cryptography.Oid" /> actual de un objeto <see cref="T:System.Security.Cryptography.OidCollection" />.</summary>
      <returns>Objeto <see cref="T:System.Security.Cryptography.Oid" /> actual.</returns>
    </member>
    <member name="T:System.Security.Cryptography.OidGroup">
      <summary>Identifica los grupos del identificador de objetos criptográficos (OID) de Windows.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.All">
      <summary>Todos los grupos.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Attribute">
      <summary>El grupo de Windows que se representa mediante CRYPT_RDN_ATTR_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EncryptionAlgorithm">
      <summary>El grupo de Windows que se representa mediante CRYPT_ENCRYPT_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.EnhancedKeyUsage">
      <summary>El grupo de Windows que se representa mediante CRYPT_ENHKEY_USAGE_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.ExtensionOrAttribute">
      <summary>El grupo de Windows que se representa mediante CRYPT_EXT_OR_ATTR_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.HashAlgorithm">
      <summary>El grupo de Windows que se representa mediante CRYPT_HASH_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.KeyDerivationFunction">
      <summary>El grupo de Windows que se representa mediante CRYPT_KDF_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Policy">
      <summary>El grupo de Windows que se representa mediante CRYPT_POLICY_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.PublicKeyAlgorithm">
      <summary>El grupo de Windows que se representa mediante CRYPT_PUBKEY_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.SignatureAlgorithm">
      <summary>El grupo de Windows que se representa mediante CRYPT_SIGN_ALG_OID_GROUP_ID.</summary>
    </member>
    <member name="F:System.Security.Cryptography.OidGroup.Template">
      <summary>El grupo de Windows que se representa mediante CRYPT_TEMPLATE_OID_GROUP_ID.</summary>
    </member>
  </members>
</doc>
# 包装登记 API 集成实现文档

## 功能概述

已成功实现了包装登记窗体与 `http://localhost:5172/api/RecyclingCleaning/GetItemTableInfo` API 的集成，实现了以下功能：

1. **自动从 API 获取数据**：窗体加载时自动调用 API 获取物品信息
2. **数据填充到表格**：将 API 返回的数据填充到红框标出的表格中
3. **复选框功能**：每行左侧有复选框，存储对应数据的 ID
4. **错误处理**：完善的异常处理和用户提示

## 实现的主要功能

### 1. 数据模型
```csharp
// API响应数据模型
public class ApiResponse<T>
{
    public string msg { get; set; }
    public T data { get; set; }
    public int code { get; set; }
}

// 物品信息数据模型
public class ItemInfo
{
    public int id { get; set; }
    public int touseId { get; set; }
    public int recyclingId { get; set; }
    public int useId { get; set; }
    public string itemCode { get; set; }
    public string itemName { get; set; }
    public int itemType { get; set; }
    public string itemattribute { get; set; }
    public int itemNumber { get; set; }
}
```

### 2. 表格列结构
- **Selected (bool)**：复选框列，用于选择项目
- **Id (int)**：存储 API 返回的 ID（隐藏列）
- **包名称 (string)**：映射到 `itemName`
- **包条码 (string)**：映射到 `itemCode`
- **包类型 (string)**：映射到 `itemType`（转换为文本描述）
- **包属性 (string)**：映射到 `itemattribute`
- **使用科室 (string)**：暂时为空

### 3. API 数据映射
根据提供的 API 返回数据格式：
```json
{
  "msg": "获取物品信息成功",
  "data": [
    {
      "id": 13,
      "touseId": 0,
      "recyclingId": 2,
      "useId": 0,
      "itemCode": "1009",
      "itemName": "更好发",
      "itemType": 0,
      "itemattribute": "太容易太容易",
      "itemNumber": 3
    }
  ],
  "code": 200
}
```

数据映射关系：
- `id` → 存储在隐藏的 Id 列中
- `itemName` → 包名称列
- `itemCode` → 包条码列
- `itemType` → 包类型列（0=器械包, 1=敷料包, 2=混合包）
- `itemattribute` → 包属性列

### 4. 主要方法

#### LoadDataFromApiAsync()
- 异步调用 API 获取数据
- 解析 JSON 响应
- 错误处理和用户提示

#### FillGridWithApiData(List<ItemInfo> items)
- 将 API 数据填充到表格
- 清空现有数据
- 添加新数据行

#### GetSelectedItemIds()
- 获取用户选中的项目 ID 列表
- 遍历表格行检查复选框状态
- 返回选中项目的 ID 集合

#### GetItemTypeText(int itemType)
- 将数字类型转换为文本描述
- 支持扩展更多类型

### 5. 用户交互

#### 确认按钮功能
- 点击确认按钮时显示选中的项目 ID
- 如果没有选中任何项目，提示用户先选择
- 显示格式：`已选中的项目ID: 13, 10, 5`

#### 刷新功能
- 提供 `RefreshData()` 方法重新加载数据
- 可以在需要时调用以更新表格内容

## 使用说明

1. **窗体加载**：打开包装登记窗体时，会自动从 API 获取数据并填充到表格
2. **选择项目**：通过左侧复选框选择需要登记的项目
3. **确认操作**：点击确认按钮查看选中的项目 ID
4. **错误处理**：如果 API 调用失败，会显示相应的错误信息

## 技术特点

1. **异步操作**：使用 async/await 模式，不阻塞 UI 线程
2. **错误处理**：完善的 try-catch 异常处理
3. **数据绑定**：使用 DataTable 进行数据绑定
4. **类型转换**：智能的数据类型转换和映射
5. **用户体验**：友好的错误提示和操作反馈

## 配置说明

API 基础 URL 配置在常量中：
```csharp
private const string API_BASE_URL = "http://localhost:5172";
```

如需修改 API 地址，只需更改此常量值即可。

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Windows.Forms;
using WinFormsAppDemo2.Common;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using DevExpress.XtraEditors;

namespace MedicalDisinfectionSupplyCenter
{
    public partial class Login : DevExpress.XtraEditors.XtraForm
    {
        public Login()
        {
            InitializeComponent();
            // 添加回车键触发登录
            this.textusername1.KeyDown += new KeyEventHandler(LoginTextBox_KeyDown);
            this.textpassword.KeyDown += new KeyEventHandler(LoginTextBox_KeyDown);
        }

        // 新增：回车键处理方法
        private void LoginTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                button2_Click(button2, EventArgs.Empty);
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            var username = textName1.Text;
            var password = textPassword1.Text;
            var url = $"http://***********:4050/api/RBAC/Login?username={Uri.EscapeDataString(username)}&password={Uri.EscapeDataString(password)}";
            //var url = $"http://localhost:5172/api/RBAC/Login?username={Uri.EscapeDataString(username)}&password={Uri.EscapeDataString(password)}";
            var result = HttpClientHelper.ClientAsync("GET", url, true, null).Result;

            // 反序列化json，判断code字段
            try
            {
                var jobj = JObject.Parse(result);
                var code = jobj["code"]?.ToObject<int>() ?? 0;
                if (code == 200)
                {
                    XtraMessageBox.Show("登录成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // 获取用户信息和权限
                    var data = jobj["data"];
                    string userName = data["userName"]?.ToString() ?? "";
                    string roleName = data["roleName"]?.ToString() ?? "";
                    var permissions = data["permissions"]?.ToString() ?? "[]";
                    // 跳转到FluentDesignForm1并传递数据
                    FluentDesignForm1 mainForm = new FluentDesignForm1(userName, roleName, permissions);
                    mainForm.Show();
                    this.Hide();
                }
                else
                {
                    XtraMessageBox.Show("登录失败：" + (jobj["msg"]?.ToString() ?? "未知错误"), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch
            {
                XtraMessageBox.Show("服务器返回数据格式错误或无法解析", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            var username = this.textusername1.Text;
            var password = this.textpassword.Text;
            // 非空校验
            if (string.IsNullOrWhiteSpace(username))
            {
                XtraMessageBox.Show("请输入用户名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                this.textusername1.Focus();
                return;
            }
            if (string.IsNullOrWhiteSpace(password))
            {
                XtraMessageBox.Show("请输入密码！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                this.textpassword.Focus();
                return;
            }
            var url = $"http://***********:4050/api/RBAC/Login?username={Uri.EscapeDataString(username)}&password={Uri.EscapeDataString(password)}";
            var result = HttpClientHelper.ClientAsync("GET", url, true, null).Result;

            // 反序列化json，判断code字段
            try
            {
                var jobj = JObject.Parse(result);
                var code = jobj["code"]?.ToObject<int>() ?? 0;
                if (code == 200)
                {
                    XtraMessageBox.Show("登录成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // 获取用户信息和权限
                    var data = jobj["data"];
                    string userName = data["userName"]?.ToString() ?? "";
                    string roleName = data["roleName"]?.ToString() ?? "";
                    var permissions = data["permissions"]?.ToString() ?? "[]";
                    // 跳转到FluentDesignForm1并传递数据
                    FluentDesignForm1 mainForm = new FluentDesignForm1(userName, roleName, permissions);
                    mainForm.Show();
                    this.Hide();
                }
                else
                {
                    XtraMessageBox.Show("登录失败：" + (jobj["msg"]?.ToString() ?? "未知错误"), "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch
            {
                XtraMessageBox.Show("服务器返回数据格式错误或无法解析", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter
{
    /// <summary>
    /// 菜单导航测试类，用于验证所有菜单跳转配置是否正确
    /// </summary>
    public static class MenuNavigationTest
    {
        /// <summary>
        /// 测试所有菜单跳转配置
        /// </summary>
        public static void TestAllMenuNavigation()
        {
            Console.WriteLine("=== 菜单跳转配置测试 ===");
            
            // 获取所有测试菜单配置
            var testMenus = GetAllTestMenuConfigurations();
            
            int successCount = 0;
            int failCount = 0;
            
            foreach (var menu in testMenus)
            {
                bool isValid = TestMenuNavigation(menu.MenuName, menu.PermissionUrl);
                if (isValid)
                {
                    successCount++;
                    Console.WriteLine($"✅ {menu.MenuName} -> {menu.PermissionUrl}");
                }
                else
                {
                    failCount++;
                    Console.WriteLine($"❌ {menu.MenuName} -> {menu.PermissionUrl}");
                }
            }
            
            Console.WriteLine($"\n=== 测试结果 ===");
            Console.WriteLine($"成功: {successCount}");
            Console.WriteLine($"失败: {failCount}");
            Console.WriteLine($"总计: {testMenus.Count}");
        }
        
        /// <summary>
        /// 测试单个菜单跳转
        /// </summary>
        private static bool TestMenuNavigation(string menuName, string permissionUrl)
        {
            if (string.IsNullOrEmpty(permissionUrl))
            {
                return true; // 空URL表示显示欢迎页，这是有效的
            }
            
            try
            {
                // 处理斜杠格式的路径
                if (permissionUrl.Contains("/"))
                {
                    permissionUrl = permissionUrl.Replace("/", ".");
                }
                
                // 在当前程序集中查找类型
                Assembly currentAssembly = Assembly.GetExecutingAssembly();
                
                // 检查是否包含命名空间
                if (permissionUrl.Contains("."))
                {
                    // 包含命名空间的完整类型名称
                    string fullTypeName = $"MedicalDisinfectionSupplyCenter.{permissionUrl}";
                    Type exactType = currentAssembly.GetType(fullTypeName);
                    if (exactType != null && typeof(UserControl).IsAssignableFrom(exactType))
                    {
                        return true;
                    }
                }
                
                // 如果没有找到精确匹配，尝试模糊匹配
                Type formType = currentAssembly.GetTypes()
                    .FirstOrDefault(t => t.Name.Equals(permissionUrl.Split('.').Last(), StringComparison.OrdinalIgnoreCase) &&
                                        typeof(UserControl).IsAssignableFrom(t));
                
                return formType != null;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 获取所有测试菜单配置
        /// </summary>
        private static List<MenuTestItem> GetAllTestMenuConfigurations()
        {
            return new List<MenuTestItem>
            {
                // 工作台
                new MenuTestItem("工作台", "Workbenches.Workbenches"),
                
                // 设备管理菜单
                new MenuTestItem("设备字典", "BasicManagement.DeviceDictionary"),
                new MenuTestItem("器械包字典", "BasicManagement.EquipmentPackageDictionary"),
                new MenuTestItem("器械字典", "BasicManagement.EquipmentDictionary"),
                
                // 基础数据菜单
                new MenuTestItem("器械包字典", "BasicManagement.EquipmentPackageDictionary"),
                new MenuTestItem("设备字典", "BasicManagement.DeviceDictionary"),
                new MenuTestItem("货架字典", "BasicManagement.ShelfDictionary"),
                new MenuTestItem("回收申请", "DepartmentManagement.RecyclingApplication"),
                new MenuTestItem("领用申请", "DepartmentManagement.ApplicationRequisition"),
                new MenuTestItem("签收管理", "DepartmentManagement.SignManagement"),
                new MenuTestItem("使用管理", "DepartmentManagement.UseManagement"),
                new MenuTestItem("存放管理", "InventoryManagement.StorageManagement"),
                new MenuTestItem("发放使用", "InventoryManagement.DistributeUse"),
                new MenuTestItem("入库管理", "InventoryManagement.WarehouseManagement"),
                new MenuTestItem("出库管理", "InventoryManagement.OutboundManagement"),
                
                // 报表管理菜单
                new MenuTestItem("入库报表", "ReportManagement.InboundReportControl"),
                new MenuTestItem("出库报表", "ReportManagement.OutboundReportControl"),
                new MenuTestItem("库存报表", "ReportManagement.InventoryReportControl"),
                
                // 系统管理菜单（空URL，显示欢迎页）
                new MenuTestItem("系统设置", ""),
                new MenuTestItem("数据备份", ""),
                new MenuTestItem("系统日志", "")
            };
        }
        
        /// <summary>
        /// 菜单测试项
        /// </summary>
        private class MenuTestItem
        {
            public string MenuName { get; set; }
            public string PermissionUrl { get; set; }
            
            public MenuTestItem(string menuName, string permissionUrl)
            {
                MenuName = menuName;
                PermissionUrl = permissionUrl;
            }
        }
    }
} 
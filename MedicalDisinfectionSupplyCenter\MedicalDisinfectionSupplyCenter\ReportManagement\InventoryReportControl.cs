using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.ReportManagement
{
    public partial class InventoryReportControl : UserControl
    {
        public InventoryReportControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // InventoryReportControl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Name = "InventoryReportControl";
            this.Size = new System.Drawing.Size(800, 600);
            this.Load += new System.EventHandler(this.InventoryReportControl_Load);
            this.ResumeLayout(false);
        }

        private void InventoryReportControl_Load(object sender, EventArgs e)
        {
            // Add a header label
            Label headerLabel = new Label();
            headerLabel.Text = "库存报表";
            headerLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            headerLabel.ForeColor = Color.FromArgb(0, 120, 215);
            headerLabel.AutoSize = false;
            headerLabel.TextAlign = ContentAlignment.MiddleCenter;
            headerLabel.Dock = DockStyle.Top;
            headerLabel.Height = 50;
            this.Controls.Add(headerLabel);

            // Create filter panel
            Panel filterPanel = new Panel();
            filterPanel.Dock = DockStyle.Top;
            filterPanel.Height = 100;
            filterPanel.Padding = new Padding(10);
            this.Controls.Add(filterPanel);

            // Start date
            Label startLabel = new Label();
            startLabel.Text = "开始日期:";
            startLabel.Location = new Point(20, 20);
            startLabel.AutoSize = true;
            filterPanel.Controls.Add(startLabel);

            DateTimePicker startDatePicker = new DateTimePicker();
            startDatePicker.Format = DateTimePickerFormat.Short;
            startDatePicker.Location = new Point(100, 20);
            startDatePicker.Width = 120;
            startDatePicker.Value = DateTime.Now.AddMonths(-1);
            filterPanel.Controls.Add(startDatePicker);

            // End date
            Label endLabel = new Label();
            endLabel.Text = "结束日期:";
            endLabel.Location = new Point(240, 20);
            endLabel.AutoSize = true;
            filterPanel.Controls.Add(endLabel);

            DateTimePicker endDatePicker = new DateTimePicker();
            endDatePicker.Format = DateTimePickerFormat.Short;
            endDatePicker.Location = new Point(320, 20);
            endDatePicker.Width = 120;
            endDatePicker.Value = DateTime.Now;
            filterPanel.Controls.Add(endDatePicker);

            // Device type
            Label typeLabel = new Label();
            typeLabel.Text = "设备类型:";
            typeLabel.Location = new Point(20, 60);
            typeLabel.AutoSize = true;
            filterPanel.Controls.Add(typeLabel);

            ComboBox typeComboBox = new ComboBox();
            typeComboBox.Items.AddRange(new object[] { "全部", "灭菌器", "清洗机", "消毒柜" });
            typeComboBox.SelectedIndex = 0;
            typeComboBox.Location = new Point(100, 60);
            typeComboBox.Width = 120;
            filterPanel.Controls.Add(typeComboBox);

            // Search button
            Button searchButton = new Button();
            searchButton.Text = "查询";
            searchButton.Location = new Point(460, 20);
            searchButton.Width = 80;
            searchButton.Height = 30;
            searchButton.BackColor = Color.FromArgb(0, 120, 215);
            searchButton.ForeColor = Color.White;
            filterPanel.Controls.Add(searchButton);

            // Export button
            Button exportButton = new Button();
            exportButton.Text = "导出Excel";
            exportButton.Location = new Point(460, 60);
            exportButton.Width = 80;
            exportButton.Height = 30;
            exportButton.BackColor = Color.FromArgb(46, 204, 113);
            exportButton.ForeColor = Color.White;
            filterPanel.Controls.Add(exportButton);

            // Print button
            Button printButton = new Button();
            printButton.Text = "打印";
            printButton.Location = new Point(560, 20);
            printButton.Width = 80;
            printButton.Height = 30;
            printButton.BackColor = Color.FromArgb(52, 152, 219);
            printButton.ForeColor = Color.White;
            filterPanel.Controls.Add(printButton);

            // Create statistics panel
            Panel statsPanel = new Panel();
            statsPanel.Dock = DockStyle.Top;
            statsPanel.Height = 80;
            statsPanel.BackColor = Color.FromArgb(240, 240, 240);
            statsPanel.Padding = new Padding(20);
            this.Controls.Add(statsPanel);

            // Total count
            Label totalLabel = new Label();
            totalLabel.Text = "设备总数: 256台";
            totalLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            totalLabel.Location = new Point(20, 20);
            totalLabel.AutoSize = true;
            statsPanel.Controls.Add(totalLabel);

            // In use count
            Label inUseLabel = new Label();
            inUseLabel.Text = "在用设备: 198台";
            inUseLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            inUseLabel.Location = new Point(200, 20);
            inUseLabel.AutoSize = true;
            statsPanel.Controls.Add(inUseLabel);

            // Available count
            Label availableLabel = new Label();
            availableLabel.Text = "可用设备: 58台";
            availableLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            availableLabel.Location = new Point(380, 20);
            availableLabel.AutoSize = true;
            statsPanel.Controls.Add(availableLabel);

            // Total value
            Label valueLabel = new Label();
            valueLabel.Text = "设备总值: ¥3,582,500.00";
            valueLabel.Font = new Font("微软雅黑", 10, FontStyle.Bold);
            valueLabel.Location = new Point(560, 20);
            valueLabel.AutoSize = true;
            statsPanel.Controls.Add(valueLabel);

            // Create report grid
            DataGridView reportGrid = new DataGridView();
            reportGrid.Dock = DockStyle.Fill;
            reportGrid.BackgroundColor = Color.White;
            reportGrid.BorderStyle = BorderStyle.None;
            reportGrid.AllowUserToAddRows = false;
            reportGrid.AllowUserToDeleteRows = false;
            reportGrid.ReadOnly = true;
            reportGrid.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            reportGrid.RowHeadersVisible = false;
            reportGrid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            reportGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 240, 240);
            this.Controls.Add(reportGrid);

            // Add columns
            reportGrid.Columns.Add("DeviceCode", "设备编码");
            reportGrid.Columns.Add("DeviceName", "设备名称");
            reportGrid.Columns.Add("DeviceType", "设备类型");
            reportGrid.Columns.Add("Department", "所属科室");
            reportGrid.Columns.Add("InboundDate", "入库日期");
            reportGrid.Columns.Add("Status", "状态");
            reportGrid.Columns.Add("Count", "数量");
            reportGrid.Columns.Add("UnitPrice", "单价");
            reportGrid.Columns.Add("TotalPrice", "总价");

            // Add sample data
            reportGrid.Rows.Add("MJ-001", "医用灭菌器", "灭菌器", "消毒中心", "2023-01-15", "在用", "5", "¥50,000.00", "¥250,000.00");
            reportGrid.Rows.Add("MJ-002", "高温灭菌器", "灭菌器", "手术室", "2023-02-20", "在用", "3", "¥75,000.00", "¥225,000.00");
            reportGrid.Rows.Add("QX-001", "全自动清洗机", "清洗机", "消毒中心", "2023-03-10", "在用", "4", "¥60,000.00", "¥240,000.00");
            reportGrid.Rows.Add("QX-002", "超声波清洗机", "清洗机", "消毒中心", "2023-04-05", "在用", "2", "¥45,000.00", "¥90,000.00");
            reportGrid.Rows.Add("XDG-001", "立式消毒柜", "消毒柜", "外科", "2023-05-12", "在用", "8", "¥30,000.00", "¥240,000.00");
            reportGrid.Rows.Add("XDG-002", "卧式消毒柜", "消毒柜", "内科", "2023-06-20", "在用", "6", "¥25,000.00", "¥150,000.00");
            reportGrid.Rows.Add("MJ-003", "低温灭菌器", "灭菌器", "手术室", "2023-07-15", "在用", "2", "¥80,000.00", "¥160,000.00");
            reportGrid.Rows.Add("QX-003", "多功能清洗机", "清洗机", "消毒中心", "2023-08-10", "可用", "4", "¥55,000.00", "¥220,000.00");
            reportGrid.Rows.Add("XDG-003", "壁挂式消毒柜", "消毒柜", "外科", "2023-09-05", "可用", "10", "¥20,000.00", "¥200,000.00");
            reportGrid.Rows.Add("MJ-004", "压力蒸汽灭菌器", "灭菌器", "消毒中心", "2023-10-18", "可用", "2", "¥90,000.00", "¥180,000.00");
            reportGrid.Rows.Add("QX-004", "喷淋式清洗机", "清洗机", "消毒中心", "2023-11-22", "可用", "3", "¥65,000.00", "¥195,000.00");
            reportGrid.Rows.Add("XDG-004", "臭氧消毒柜", "消毒柜", "内科", "2023-12-15", "可用", "4", "¥35,000.00", "¥140,000.00");
        }
    }
} 
using System;
using System.Drawing;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public partial class CompletedDisinfectionOperationDialog : Form
    {
        public enum OperationResult
        {
            View,               // 查看
            Cancel_Operation,   // 撤销
            Cancel             // 取消
        }

        public OperationResult Result { get; private set; } = OperationResult.Cancel;

        private Button btnView;
        private Button btnCancelOperation;
        private Label lblMessage;
        private PictureBox pictureBox;

        public CompletedDisinfectionOperationDialog(string disinfectionBatch)
        {
            InitializeComponent();
            lblMessage.Text = $"消毒批次：{disinfectionBatch}\n\n请选择要执行的操作：\n\n【查看】- 查看详细信息\n【撤销】- 撤销完成状态";
        }

        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CompletedDisinfectionOperationDialog));
            this.lblMessage = new System.Windows.Forms.Label();
            this.pictureBox = new System.Windows.Forms.PictureBox();
            this.btnView = new System.Windows.Forms.Button();
            this.btnCancelOperation = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox)).BeginInit();
            this.SuspendLayout();
            // 
            // lblMessage
            // 
            this.lblMessage.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblMessage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblMessage.Location = new System.Drawing.Point(80, 20);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new System.Drawing.Size(340, 150);
            this.lblMessage.TabIndex = 1;
            // 
            // pictureBox
            // 
            this.pictureBox.Image = SystemIcons.Question.ToBitmap();
            this.pictureBox.Location = new System.Drawing.Point(20, 20);
            this.pictureBox.Name = "pictureBox";
            this.pictureBox.Size = new System.Drawing.Size(48, 48);
            this.pictureBox.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox.TabIndex = 0;
            this.pictureBox.TabStop = false;
            // 
            // btnView
            // 
            this.btnView.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnView.Location = new System.Drawing.Point(180, 200);
            this.btnView.Name = "btnView";
            this.btnView.Size = new System.Drawing.Size(80, 35);
            this.btnView.TabIndex = 2;
            this.btnView.Text = "查看";
            this.btnView.UseVisualStyleBackColor = true;
            this.btnView.Click += new System.EventHandler(this.BtnView_Click);
            //
            // btnCancelOperation
            //
            this.btnCancelOperation.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.btnCancelOperation.Location = new System.Drawing.Point(280, 200);
            this.btnCancelOperation.Name = "btnCancelOperation";
            this.btnCancelOperation.Size = new System.Drawing.Size(80, 35);
            this.btnCancelOperation.TabIndex = 3;
            this.btnCancelOperation.Text = "撤销";
            this.btnCancelOperation.UseVisualStyleBackColor = true;
            this.btnCancelOperation.Click += new System.EventHandler(this.BtnCancelOperation_Click);
            // 
            // CompletedDisinfectionOperationDialog
            // 
            this.AcceptButton = this.btnView;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(440, 280);
            this.Controls.Add(this.pictureBox);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.btnView);
            this.Controls.Add(this.btnCancelOperation);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CompletedDisinfectionOperationDialog";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "已完成消毒操作";
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox)).EndInit();
            this.ResumeLayout(false);

        }

        private void BtnView_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("🔍 点击了查看按钮");
            Result = OperationResult.View;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancelOperation_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("↩️ 点击了撤销按钮");
            Result = OperationResult.Cancel_Operation;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        // 重写ProcessCmdKey方法处理ESC键
        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                Result = OperationResult.Cancel;
                this.DialogResult = DialogResult.Cancel;
                this.Close();
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}

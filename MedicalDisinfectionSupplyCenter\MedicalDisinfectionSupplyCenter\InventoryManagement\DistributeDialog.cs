using DevExpress.XtraEditors;
using System;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.InventoryManagement
{
    public class DistributeDialog : XtraForm
    {
        private SimpleButton btnClose;
        public DistributeDialog()
        {
            this.Text = "发放对话框";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Size = new System.Drawing.Size(350, 180);
            btnClose = new SimpleButton();
            btnClose.Text = "关闭";
            btnClose.Location = new System.Drawing.Point(120, 90);
            btnClose.Size = new System.Drawing.Size(100, 32);
            btnClose.Click += (s, e) => this.Close();
            this.Controls.Add(btnClose);
        }
    }
}
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Newtonsoft.Json;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.PackagingSterilization
{
    /// <summary>
    /// 包装登记功能测试类
    /// </summary>
    public class PackagingRegistrationTest
    {
        private const string API_BASE_URL = "http://localhost:5172";

        /// <summary>
        /// 测试API数据获取功能
        /// </summary>
        public static async Task<bool> TestApiDataRetrieval()
        {
            try
            {
                Console.WriteLine("开始测试API数据获取...");
                
                // 构建API URL
                string apiUrl = $"{API_BASE_URL}/api/RecyclingCleaning/GetItemTableInfo";
                Console.WriteLine($"API URL: {apiUrl}");

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false, null);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    Console.WriteLine($"API返回数据长度: {jsonResult.Length}");
                    Console.WriteLine($"API返回数据前200字符: {jsonResult.Substring(0, Math.Min(200, jsonResult.Length))}");

                    // 解析API响应
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ItemInfo>>>(jsonResult);

                    if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                    {
                        Console.WriteLine($"解析成功，获取到 {apiResponse.data.Count} 条数据");
                        Console.WriteLine($"API消息: {apiResponse.msg}");
                        
                        // 显示前几条数据
                        for (int i = 0; i < Math.Min(3, apiResponse.data.Count); i++)
                        {
                            var item = apiResponse.data[i];
                            Console.WriteLine($"数据 {i + 1}: ID={item.id}, 名称={item.itemName}, 条码={item.itemCode}, 属性={item.itemattribute}");
                        }
                        
                        return true;
                    }
                    else
                    {
                        Console.WriteLine($"API返回错误: {apiResponse?.msg ?? "未知错误"}");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("API调用失败，未返回数据");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"异常详情: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 测试数据表格创建和填充功能
        /// </summary>
        public static bool TestDataTableCreation()
        {
            try
            {
                Console.WriteLine("开始测试数据表格创建...");

                // 创建数据表结构
                DataTable dt = new DataTable();
                dt.Columns.Add("Selected", typeof(bool));
                dt.Columns.Add("Id", typeof(int));
                dt.Columns.Add("包名称", typeof(string));
                dt.Columns.Add("包条码", typeof(string));
                dt.Columns.Add("包类型", typeof(string));
                dt.Columns.Add("包属性", typeof(string));
                dt.Columns.Add("使用科室", typeof(string));

                Console.WriteLine($"数据表创建成功，包含 {dt.Columns.Count} 列");

                // 添加测试数据
                var testItems = new List<ItemInfo>
                {
                    new ItemInfo { id = 1, itemName = "测试包1", itemCode = "TEST001", itemType = 0, itemattribute = "测试属性1", itemNumber = 5 },
                    new ItemInfo { id = 2, itemName = "测试包2", itemCode = "TEST002", itemType = 1, itemattribute = "测试属性2", itemNumber = 3 }
                };

                foreach (var item in testItems)
                {
                    dt.Rows.Add(
                        false,                    // Selected
                        item.id,                  // Id
                        item.itemName,            // 包名称
                        item.itemCode,            // 包条码
                        GetItemTypeText(item.itemType), // 包类型
                        item.itemattribute,       // 包属性
                        ""                        // 使用科室
                    );
                }

                Console.WriteLine($"测试数据添加成功，表格包含 {dt.Rows.Count} 行数据");

                // 验证数据
                foreach (DataRow row in dt.Rows)
                {
                    Console.WriteLine($"行数据: ID={row["Id"]}, 包名称={row["包名称"]}, 包条码={row["包条码"]}, 包类型={row["包类型"]}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"数据表格测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将数字类型转换为文本描述
        /// </summary>
        private static string GetItemTypeText(int itemType)
        {
            switch (itemType)
            {
                case 0:
                    return "器械包";
                case 1:
                    return "敷料包";
                case 2:
                    return "混合包";
                default:
                    return "未知类型";
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task<bool> RunAllTests()
        {
            Console.WriteLine("=== 包装登记功能测试开始 ===");
            
            bool dataTableTest = TestDataTableCreation();
            Console.WriteLine($"数据表格测试: {(dataTableTest ? "通过" : "失败")}");
            
            bool apiTest = await TestApiDataRetrieval();
            Console.WriteLine($"API数据获取测试: {(apiTest ? "通过" : "失败")}");
            
            bool allPassed = dataTableTest && apiTest;
            Console.WriteLine($"=== 测试结果: {(allPassed ? "全部通过" : "存在失败")} ===");
            
            return allPassed;
        }
    }
}
